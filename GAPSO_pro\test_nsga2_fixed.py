"""
测试修复后的NSGA-II算法
验证解选择策略和适应度函数的改进
"""

import numpy as np
from utils import DataUtils
from generate_topology import generate_topology
from algorithm import GAPSOServerPlacer
from multi_objective_algorithms import NSGAIIServerPlacer

def compare_algorithms():
    """对比修复后的NSGA-II与GA-PSO"""
    print("=== 对比修复后的NSGA-II与GA-PSO ===")
    
    # 加载数据
    data = DataUtils('./dataset/basestations.csv', './dataset/userdata.csv')
    base_stations = data.base_stations
    
    # 实验参数
    n = 100
    budget = 15
    t_max = 100
    rate = 1.0
    initial_basestations = {10: 100000, 25: 100000, 40: 100000, 55: 100000, 70: 100000}
    seed = 100
    
    # 生成拓扑
    delay_matrix = generate_topology(data, n)
    
    print(f"实验参数：{n}个基站，预算{budget}，覆盖半径{t_max}")
    print("-" * 60)
    
    # 测试GA-PSO
    print("运行GA-PSO算法...")
    gapso = GAPSOServerPlacer(base_stations[:n], delay_matrix)
    gapso.place_server(
        base_station_num=n,
        budget=budget,
        t_max=t_max,
        rate=rate,
        initial_basestations=initial_basestations,
        cross_proability=0.8,
        mutation_proability=0.1,
        seed=seed
    )
    
    # GA-PSO结果
    gapso_servers = len(gapso.edge_servers) if gapso.edge_servers else 0
    gapso_delay = gapso.objective_latency()
    gapso_workload = gapso.objective_workload()
    gapso_fitness = gapso.objective_fitness()
    
    # 处理可能的元组返回值
    if isinstance(gapso_delay, tuple):
        gapso_delay = gapso_delay[0]
    if isinstance(gapso_workload, tuple):
        gapso_workload = gapso_workload[0]
    if isinstance(gapso_fitness, tuple):
        gapso_fitness = gapso_fitness[0]
    
    print(f"GA-PSO结果：")
    print(f"  - 边缘服务器数量：{gapso_servers}")
    print(f"  - 平均延迟：{gapso_delay:.2f} ms")
    print(f"  - 工作负载标准差：{gapso_workload:.2f}")
    print(f"  - 适应度：{gapso_fitness:.4f}")
    
    print("\n" + "-" * 60)
    
    # 测试修复后的NSGA-II
    print("运行修复后的NSGA-II算法...")
    nsga2 = NSGAIIServerPlacer(base_stations[:n], delay_matrix)
    nsga2.place_server(
        base_station_num=n,
        budget=budget,
        t_max=t_max,
        rate=rate,
        initial_basestations=initial_basestations,
        cross_probability=0.8,
        mutation_probability=0.1,
        seed=seed
    )
    
    # NSGA-II结果
    nsga2_servers = len(nsga2.edge_servers) if nsga2.edge_servers else 0
    nsga2_delay = nsga2.objective_latency()
    nsga2_workload = nsga2.objective_workload()
    nsga2_fitness = nsga2.objective_fitness()
    
    print(f"NSGA-II结果：")
    print(f"  - 边缘服务器数量：{nsga2_servers}")
    print(f"  - 平均延迟：{nsga2_delay:.2f} ms")
    print(f"  - 工作负载标准差：{nsga2_workload:.2f}")
    print(f"  - 适应度：{nsga2_fitness:.4f}")
    
    print("\n" + "=" * 60)
    print("对比分析：")
    print("=" * 60)
    
    # 性能对比
    delay_improvement = ((gapso_delay - nsga2_delay) / gapso_delay) * 100
    workload_improvement = ((gapso_workload - nsga2_workload) / gapso_workload) * 100
    fitness_improvement = ((gapso_fitness - nsga2_fitness) / gapso_fitness) * 100
    
    print(f"延迟对比：")
    print(f"  GA-PSO: {gapso_delay:.2f} ms")
    print(f"  NSGA-II: {nsga2_delay:.2f} ms")
    print(f"  改进：{delay_improvement:+.1f}%")
    
    print(f"\n工作负载均衡对比：")
    print(f"  GA-PSO: {gapso_workload:.2f}")
    print(f"  NSGA-II: {nsga2_workload:.2f}")
    print(f"  改进：{workload_improvement:+.1f}%")
    
    print(f"\n适应度对比：")
    print(f"  GA-PSO: {gapso_fitness:.4f}")
    print(f"  NSGA-II: {nsga2_fitness:.4f}")
    print(f"  改进：{fitness_improvement:+.1f}%")
    
    print(f"\n服务器部署对比：")
    print(f"  GA-PSO: {gapso_servers}个服务器")
    print(f"  NSGA-II: {nsga2_servers}个服务器")
    
    # 判断哪个算法更好
    if nsga2_fitness < gapso_fitness:
        print(f"\n🎉 NSGA-II在综合适应度上优于GA-PSO！")
        print(f"   适应度改进：{fitness_improvement:.1f}%")
    elif abs(nsga2_fitness - gapso_fitness) < 0.01:
        print(f"\n⚖️ NSGA-II与GA-PSO性能相当")
    else:
        print(f"\n📊 GA-PSO在综合适应度上仍优于NSGA-II")
        print(f"   但NSGA-II在某些单一目标上可能更优")
    
    return {
        'gapso': {
            'servers': gapso_servers,
            'delay': gapso_delay,
            'workload': gapso_workload,
            'fitness': gapso_fitness
        },
        'nsga2': {
            'servers': nsga2_servers,
            'delay': nsga2_delay,
            'workload': nsga2_workload,
            'fitness': nsga2_fitness
        }
    }

def analyze_pareto_solutions():
    """分析NSGA-II的Pareto解集"""
    print("\n" + "=" * 60)
    print("NSGA-II Pareto解集分析")
    print("=" * 60)
    
    # 这里可以添加更详细的Pareto前沿分析
    # 由于当前实现限制，我们先展示概念
    
    print("多目标优化的优势：")
    print("1. 提供多个可选解，而不是单一解")
    print("2. 展示不同目标间的权衡关系")
    print("3. 决策者可以根据实际需求选择最合适的解")
    print("4. 避免了预先设定权重的主观性")
    
    print("\n当前实现的改进：")
    print("1. ✅ 使用加权和方法选择解，而不是只考虑延迟")
    print("2. ✅ 统一了适应度函数计算方法")
    print("3. ✅ 添加了膝点选择作为备选策略")
    print("4. 🔄 未来可以展示完整的Pareto前沿")

if __name__ == "__main__":
    try:
        results = compare_algorithms()
        analyze_pareto_solutions()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中出现错误：{e}")
        import traceback
        traceback.print_exc()
