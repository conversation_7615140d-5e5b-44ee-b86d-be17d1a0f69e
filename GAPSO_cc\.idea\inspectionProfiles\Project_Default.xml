<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="6">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="pyyaml" />
            <item index="2" class="java.lang.String" itemvalue="tensorflow" />
            <item index="3" class="java.lang.String" itemvalue="keras" />
            <item index="4" class="java.lang.String" itemvalue="numpy" />
            <item index="5" class="java.lang.String" itemvalue="more_itertools" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="tuple.issubset" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>