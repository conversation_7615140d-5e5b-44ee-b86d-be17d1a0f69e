'''
利用数据集，生成基站的连接关系

将孤岛状的基站，做出拓扑结构
'''
from config import *
from utils import *
import heapq

# 运用topology计算距离，直接用bs的id带入矩阵下标即可
# @memorize('cache/topology_l'+str(max_link)+'_d'+str(max_dis))
def generate_topology(data,n):
    # ================初始化=====================
    distances = data.distances[:n]
    for i in range(n):
        distances[i]=distances[i][:n]
    base_stations = data.base_stations[:n]
    hop_topology = [[9999 for i in range(n)]for j in range(n)]
    link_topology = [[9999 for i in range(n)]for j in range(n)]
    delay_matrix = [[9999 for i in range(n)]for j in range(n)]
    for i in range(n):
        # 研究第i行，也就是第i个基站
        dis_list_i = distances[i].copy()
        a = base_stations[i]  # 标记第i个自身

        # distances矩阵中，自身和其它因为数据错误导致的0不考虑
        for j in range(n):
            if dis_list_i[j] == 0:
                b = base_stations[j]
                dis = DataUtils.calc_distance(a.latitude, a.longitude, b.latitude, b.longitude)
                dis_list_i[j] = dis if dis > 0 else 9999

        # 求出最近的max_link个的id
        min_num_index_list = list(map(dis_list_i.index, heapq.nsmallest(max_link, dis_list_i)))

        for min_index in min_num_index_list:
            # 几个最近的基站中，小于max_dis的标1表示一跳距离
            if dis_list_i[min_index] < max_dis:
                hop_topology[i][min_index] = 1
                link_topology[i][min_index] = dis_list_i[min_index]

    # 保证矩阵对称，图是双向图
    for row in range(n):
        for col in range(row, n):  # 只遍历上半矩阵
            hop_topology[col][row] = hop_topology[row][col]
            link_topology[col][row] = link_topology[row][col]
    # debug
    # for line in topology:
    #     print(line.count(1)+line.count(9999))

    # ================利用弗洛伊德计算最短路径=====================
    dis_topology,hop_topology = floyd(link_topology,hop_topology)
    for i in range(n):
        for j in range(n):
            delay_matrix[i][j] = alpha * dis_topology[i][j] + beta * hop_topology[i][j]
    # 返回生成的topology
    return delay_matrix


# 弗洛伊德计算最短距离
def floyd(dis_topology: List[List[int]],hop_topology: List[List[int]]):
    total_num = len(dis_topology)
    for k in range(total_num):
        print('\r' + str(k) + '[' + '#'*int(k/total_num*50) + ' '*(50-int(k/total_num*50)) + ']', end='', flush=True)
        for i in range(total_num):  # 行
            for j in range(total_num):  # 列
                if i == j:
                    dis_topology[i][j] = 0
                    hop_topology[i][j] = 0
                elif dis_topology[i][j] > dis_topology[i][k]+dis_topology[k][j]:
                    dis_topology[i][j] = dis_topology[i][k] + dis_topology[k][j]
                    hop_topology[i][j] = hop_topology[i][k] + hop_topology[k][j]
    return dis_topology,hop_topology