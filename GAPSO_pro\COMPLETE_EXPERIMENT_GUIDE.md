# GAPSO完整实验运行指南

## 1. 实验前准备

### 1.1 环境检查
```bash
# 1. 检查Python环境
python --version  # 确保Python 3.7+

# 2. 安装依赖
python install_dependencies.py

# 3. 验证算法实现
python test_nsga2_fixed.py

# 4. 检查数据集
ls -la dataset/  # 确保basestations.csv和userdata.csv存在
```

### 1.2 参数配置修复
首先修复参数名称不一致问题：

```python
# 在maintest.py中统一参数名称
cross_probability = 0.8  # 统一使用正确拼写
mutation_probability = 0.1
```

### 1.3 创建实验目录结构
```bash
mkdir -p experiments/{bsnum,budget,workload,complete}
mkdir -p results_backup
mkdir -p logs
```

## 2. 分阶段实验运行计划

### 2.1 阶段1：基站数量影响实验（对应图9）

#### 实验参数
- 基站数量: 300, 350, 400, 450, 500, 550, 600
- 预算: 30个服务器
- 工作负载比例: 1.0
- 重复次数: 50次
- 算法: 全部7个算法

#### 运行命令
```bash
# 创建基站数量实验脚本
python create_bsnum_experiment.py
# 运行实验
python run_bsnum_experiment.py
```

#### 预计时间: 3-4天

### 2.2 阶段2：预算约束影响实验（对应图10）

#### 实验参数
- 基站数量: 450
- 预算: 15, 20, 25, 30, 35, 40, 45个服务器
- 工作负载比例: 1.0
- 重复次数: 50次

#### 运行命令
```bash
python run_budget_experiment.py
```

#### 预计时间: 1-2天

### 2.3 阶段3：工作负载规模实验（对应图11）

#### 实验参数
- 基站数量: 450
- 预算: 30个服务器
- 工作负载比例: 1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2
- 重复次数: 50次

#### 运行命令
```bash
python run_workload_experiment.py
```

#### 预计时间: 1-2天

## 3. 具体实验脚本实现

### 3.1 基站数量实验脚本
创建 `run_bsnum_experiment.py`:

```python
#!/usr/bin/env python3
"""
基站数量影响实验
对应论文图9：基站数量 vs 性能指标
"""

import os
import sys
import time
import json
from datetime import datetime
from multiprocessing import Pool
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/bsnum_experiment.log'),
        logging.StreamHandler()
    ]
)

def run_single_experiment(params):
    """运行单次实验"""
    n, algorithm_name, seed, budget, rate = params
    
    try:
        # 导入必要模块
        from utils import DataUtils
        from generate_topology import generate_topology
        from algorithm import *
        from multi_objective_algorithms import *
        
        # 加载数据
        data = DataUtils('./dataset/basestations.csv', './dataset/userdata.csv')
        base_stations = data.base_stations
        delay_matrix = generate_topology(data, n)
        
        # 初始基站配置
        initial_basestations = {
            int(n*0.1): 100000, int(n*0.3): 100000, int(n*0.5): 100000,
            int(n*0.7): 100000, int(n*0.9): 100000
        }
        
        # 选择算法
        algorithms = {
            'Top-K': TopKServerPlacer,
            'Random': RandomServerPlacer,
            'ACO': ACOServerPlacer,
            'GA-PSO': GAPSOServerPlacer,
            'NSGA-II': NSGAIIServerPlacer,
            'MOEA-D': MOEADServerPlacer,
            'DE': DifferentialEvolutionServerPlacer
        }
        
        if algorithm_name not in algorithms:
            return None
            
        algorithm = algorithms[algorithm_name](base_stations[:n], delay_matrix)
        
        # 运行算法
        start_time = time.time()
        
        if algorithm_name in ['NSGA-II', 'MOEA-D', 'DE']:
            algorithm.place_server(
                base_station_num=n,
                budget=budget,
                t_max=100,
                rate=rate,
                initial_basestations=initial_basestations,
                cross_probability=0.8,
                mutation_probability=0.1,
                seed=seed
            )
        else:
            algorithm.place_server(
                base_station_num=n,
                budget=budget,
                t_max=100,
                rate=rate,
                initial_basestations=initial_basestations,
                cross_proability=0.8,  # 注意拼写
                mutation_proability=0.1,
                seed=seed
            )
        
        end_time = time.time()
        runtime = end_time - start_time
        
        # 计算结果
        if hasattr(algorithm, 'edge_servers') and algorithm.edge_servers:
            # 处理不同算法的返回值格式
            if algorithm_name in ['NSGA-II', 'MOEA-D', 'DE']:
                delay = algorithm.objective_latency()
                workload = algorithm.objective_workload()
                fitness = algorithm.objective_fitness()
            else:
                delay_result = algorithm.objective_latency()
                delay = delay_result[0] if isinstance(delay_result, tuple) else delay_result
                
                workload_result = algorithm.objective_workload()
                workload = workload_result[0] if isinstance(workload_result, tuple) else workload_result
                
                fitness_result = algorithm.objective_fitness()
                fitness = fitness_result[0] if isinstance(fitness_result, tuple) else fitness_result
            
            num_servers = len(algorithm.edge_servers)
            
            # 保存结果
            result = {
                'algorithm': algorithm_name,
                'n': n,
                'seed': seed,
                'budget': budget,
                'rate': rate,
                'fitness': fitness,
                'delay': delay,
                'workload': workload,
                'num_servers': num_servers,
                'runtime': runtime,
                'timestamp': datetime.now().isoformat()
            }
            
            # 写入结果文件
            result_file = f'experiments/bsnum/{algorithm_name}_N{n}.txt'
            os.makedirs(os.path.dirname(result_file), exist_ok=True)
            
            with open(result_file, 'a', encoding='utf-8') as f:
                result_line = f"seed={seed},N={n},t_max=100,budget={budget},rate={rate} {algorithm_name}的fitness:{fitness},delay:{delay},workload:{workload},runtime:{runtime}\n"
                f.write(result_line)
            
            logging.info(f"完成: {algorithm_name}, N={n}, seed={seed}, fitness={fitness:.4f}")
            return result
            
        else:
            logging.error(f"失败: {algorithm_name}, N={n}, seed={seed} - 无边缘服务器")
            return None
            
    except Exception as e:
        logging.error(f"错误: {algorithm_name}, N={n}, seed={seed} - {str(e)}")
        return None

def main():
    """主实验函数"""
    logging.info("开始基站数量影响实验")
    
    # 实验参数
    base_station_numbers = [300, 350, 400, 450, 500, 550, 600]
    algorithms = ['Top-K', 'Random', 'ACO', 'GA-PSO', 'NSGA-II', 'MOEA-D', 'DE']
    budget = 30
    rate = 1.0
    repetitions = 50
    
    # 生成所有实验参数组合
    experiment_params = []
    for n in base_station_numbers:
        for algorithm_name in algorithms:
            for seed in range(100, 100 + repetitions):
                experiment_params.append((n, algorithm_name, seed, budget, rate))
    
    total_experiments = len(experiment_params)
    logging.info(f"总实验数量: {total_experiments}")
    
    # 并行运行实验
    start_time = time.time()
    
    # 使用进程池并行执行
    with Pool(processes=4) as pool:  # 根据CPU核心数调整
        results = pool.map(run_single_experiment, experiment_params)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 统计结果
    successful_results = [r for r in results if r is not None]
    success_rate = len(successful_results) / total_experiments * 100
    
    logging.info(f"实验完成!")
    logging.info(f"总时间: {total_time/3600:.2f} 小时")
    logging.info(f"成功率: {success_rate:.1f}% ({len(successful_results)}/{total_experiments})")
    
    # 保存实验摘要
    summary = {
        'experiment_type': 'base_station_number',
        'total_experiments': total_experiments,
        'successful_experiments': len(successful_results),
        'success_rate': success_rate,
        'total_time_hours': total_time / 3600,
        'parameters': {
            'base_station_numbers': base_station_numbers,
            'algorithms': algorithms,
            'budget': budget,
            'rate': rate,
            'repetitions': repetitions
        },
        'timestamp': datetime.now().isoformat()
    }
    
    with open('experiments/bsnum/experiment_summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    logging.info("实验摘要已保存到 experiments/bsnum/experiment_summary.json")

if __name__ == "__main__":
    main()
```

### 3.2 实验监控脚本
创建 `monitor_experiment.py`:

```python
#!/usr/bin/env python3
"""
实验进度监控脚本
"""

import os
import time
import json
from datetime import datetime, timedelta

def monitor_experiment(experiment_dir):
    """监控实验进度"""
    
    while True:
        # 统计结果文件
        result_files = []
        total_lines = 0
        
        if os.path.exists(experiment_dir):
            for file in os.listdir(experiment_dir):
                if file.endswith('.txt'):
                    file_path = os.path.join(experiment_dir, file)
                    with open(file_path, 'r') as f:
                        lines = len(f.readlines())
                        total_lines += lines
                        result_files.append((file, lines))
        
        # 显示进度
        print(f"\n=== 实验进度监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")
        print(f"结果文件数量: {len(result_files)}")
        print(f"总实验完成数: {total_lines}")
        
        for file, lines in sorted(result_files):
            print(f"  {file}: {lines} 次实验")
        
        # 估算剩余时间
        expected_total = 7 * 7 * 50  # 7个基站规模 × 7个算法 × 50次重复
        if total_lines > 0:
            progress = total_lines / expected_total * 100
            print(f"总体进度: {progress:.1f}% ({total_lines}/{expected_total})")
        
        time.sleep(60)  # 每分钟更新一次

if __name__ == "__main__":
    import sys
    experiment_dir = sys.argv[1] if len(sys.argv) > 1 else "experiments/bsnum"
    monitor_experiment(experiment_dir)
```

## 4. 结果验证和图表生成

### 4.1 数据验证脚本
创建 `validate_results.py`:

```python
#!/usr/bin/env python3
"""
实验结果验证脚本
"""

import os
import numpy as np
import pandas as pd
from collections import defaultdict

def validate_experiment_results(experiment_dir):
    """验证实验结果的完整性和合理性"""
    
    print(f"验证实验结果: {experiment_dir}")
    
    # 收集所有结果
    all_results = defaultdict(list)
    
    for file in os.listdir(experiment_dir):
        if file.endswith('.txt'):
            algorithm = file.split('_')[0]
            file_path = os.path.join(experiment_dir, file)
            
            with open(file_path, 'r') as f:
                for line in f:
                    if 'fitness:' in line:
                        # 解析结果
                        parts = line.strip().split()
                        params = parts[0].split(',')
                        
                        # 提取数值
                        fitness = float(line.split('fitness:')[1].split(',')[0])
                        delay = float(line.split('delay:')[1].split(',')[0])
                        workload = float(line.split('workload:')[1].split(',')[0])
                        
                        all_results[algorithm].append({
                            'fitness': fitness,
                            'delay': delay,
                            'workload': workload
                        })
    
    # 验证数据合理性
    print("\n=== 数据合理性检查 ===")
    for algorithm, results in all_results.items():
        if not results:
            print(f"⚠️  {algorithm}: 无结果数据")
            continue
            
        fitness_values = [r['fitness'] for r in results]
        delay_values = [r['delay'] for r in results]
        workload_values = [r['workload'] for r in results]
        
        print(f"\n{algorithm} ({len(results)} 次实验):")
        print(f"  适应度: {np.mean(fitness_values):.4f} ± {np.std(fitness_values):.4f}")
        print(f"  延迟: {np.mean(delay_values):.2f} ± {np.std(delay_values):.2f} ms")
        print(f"  工作负载: {np.mean(workload_values):.2f} ± {np.std(workload_values):.2f}")
        
        # 检查异常值
        if np.max(fitness_values) > 10:
            print(f"  ⚠️  适应度异常值: {np.max(fitness_values)}")
        if np.max(delay_values) > 200:
            print(f"  ⚠️  延迟异常值: {np.max(delay_values)}")
        if np.max(workload_values) > 50000:
            print(f"  ⚠️  工作负载异常值: {np.max(workload_values)}")
    
    return all_results

if __name__ == "__main__":
    import sys
    experiment_dir = sys.argv[1] if len(sys.argv) > 1 else "experiments/bsnum"
    validate_experiment_results(experiment_dir)
```

### 4.2 完整图表生成脚本
创建 `generate_complete_figures.py`:

```python
#!/usr/bin/env python3
"""
生成完整的实验结果图表
包括图9、图10、图11和新增的多目标算法对比图
"""

import matplotlib.pyplot as plt
import numpy as np
import scipy.stats
import seaborn as sns
from collections import defaultdict
import os

# 设置图表样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_experiment_data(experiment_dir, experiment_type):
    """加载实验数据"""
    
    data = defaultdict(lambda: defaultdict(list))
    
    for file in os.listdir(experiment_dir):
        if file.endswith('.txt'):
            algorithm = file.split('_')[0]
            file_path = os.path.join(experiment_dir, file)
            
            with open(file_path, 'r') as f:
                for line in f:
                    if 'fitness:' in line:
                        # 解析参数
                        if experiment_type == 'bsnum':
                            n = int(line.split('N=')[1].split(',')[0])
                            key = n
                        elif experiment_type == 'budget':
                            budget = int(line.split('budget=')[1].split(',')[0])
                            key = budget
                        elif experiment_type == 'workload':
                            rate = float(line.split('rate=')[1].split()[0])
                            key = rate
                        
                        # 解析结果
                        fitness = float(line.split('fitness:')[1].split(',')[0])
                        delay = float(line.split('delay:')[1].split(',')[0])
                        workload = float(line.split('workload:')[1].split(',')[0])
                        
                        data[algorithm][key].append({
                            'fitness': fitness,
                            'delay': delay,
                            'workload': workload
                        })
    
    return data

def calculate_confidence_intervals(values, confidence=0.95):
    """计算置信区间"""
    if len(values) == 0:
        return 0, 0, 0, 0
    
    mean = np.mean(values)
    sem = scipy.stats.sem(values)
    interval = scipy.stats.norm.interval(confidence, loc=mean, scale=sem)
    
    return mean, sem, interval[0], interval[1]

def generate_figure_9():
    """生成图9：基站数量影响分析"""
    
    print("生成图9：基站数量影响分析")
    
    data = load_experiment_data('experiments/bsnum', 'bsnum')
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    algorithms = ['Top-K', 'Random', 'ACO', 'GA-PSO', 'NSGA-II', 'MOEA-D', 'DE']
    colors = plt.cm.tab10(np.linspace(0, 1, len(algorithms)))
    x_values = [300, 350, 400, 450, 500, 550, 600]
    
    # (a) 平均访问延迟
    ax1 = axes[0]
    for i, algorithm in enumerate(algorithms):
        if algorithm in data:
            means, errors, lowers, uppers = [], [], [], []
            
            for n in x_values:
                if n in data[algorithm]:
                    delays = [r['delay'] for r in data[algorithm][n]]
                    mean, error, lower, upper = calculate_confidence_intervals(delays)
                    means.append(mean)
                    errors.append(error)
                    lowers.append(lower)
                    uppers.append(upper)
                else:
                    means.append(np.nan)
                    errors.append(0)
                    lowers.append(np.nan)
                    uppers.append(np.nan)
            
            # 绘制线条和误差棒
            ax1.errorbar(x_values, means, yerr=errors, label=algorithm, 
                        color=colors[i], marker='o', capsize=3)
            ax1.fill_between(x_values, lowers, uppers, alpha=0.2, color=colors[i])
    
    ax1.set_xlabel('Number of Base Stations')
    ax1.set_ylabel('Average Access Delay (ms)')
    ax1.set_title('(a) Average Access Delay vs Base Station Number')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # (b) 工作负载标准差
    ax2 = axes[1]
    for i, algorithm in enumerate(algorithms):
        if algorithm in data:
            means, errors, lowers, uppers = [], [], [], []
            
            for n in x_values:
                if n in data[algorithm]:
                    workloads = [r['workload'] for r in data[algorithm][n]]
                    mean, error, lower, upper = calculate_confidence_intervals(workloads)
                    means.append(mean)
                    errors.append(error)
                    lowers.append(lower)
                    uppers.append(upper)
                else:
                    means.append(np.nan)
                    errors.append(0)
                    lowers.append(np.nan)
                    uppers.append(np.nan)
            
            ax2.errorbar(x_values, means, yerr=errors, label=algorithm, 
                        color=colors[i], marker='s', capsize=3)
            ax2.fill_between(x_values, lowers, uppers, alpha=0.2, color=colors[i])
    
    ax2.set_xlabel('Number of Base Stations')
    ax2.set_ylabel('Workload Standard Deviation')
    ax2.set_title('(b) Workload Balance vs Base Station Number')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # (c) 目标函数值F
    ax3 = axes[2]
    for i, algorithm in enumerate(algorithms):
        if algorithm in data:
            means, errors, lowers, uppers = [], [], [], []
            
            for n in x_values:
                if n in data[algorithm]:
                    fitness_values = [r['fitness'] for r in data[algorithm][n]]
                    mean, error, lower, upper = calculate_confidence_intervals(fitness_values)
                    means.append(mean)
                    errors.append(error)
                    lowers.append(lower)
                    uppers.append(upper)
                else:
                    means.append(np.nan)
                    errors.append(0)
                    lowers.append(np.nan)
                    uppers.append(np.nan)
            
            ax3.errorbar(x_values, means, yerr=errors, label=algorithm, 
                        color=colors[i], marker='^', capsize=3)
            ax3.fill_between(x_values, lowers, uppers, alpha=0.2, color=colors[i])
    
    ax3.set_xlabel('Number of Base Stations')
    ax3.set_ylabel('Objective Function Value F')
    ax3.set_title('(c) Overall Performance vs Base Station Number')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('result_fig/figure_9_base_station_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("图9已保存到 result_fig/figure_9_base_station_analysis.png")

if __name__ == "__main__":
    # 确保结果目录存在
    os.makedirs('result_fig', exist_ok=True)
    
    # 生成所有图表
    if os.path.exists('experiments/bsnum'):
        generate_figure_9()
    else:
        print("基站数量实验数据不存在，请先运行实验")
```

## 5. 完整实验运行时间表

### 5.1 第1周：环境准备和小规模验证
- 第1-2天：环境配置、依赖安装、代码修复
- 第3-4天：小规模实验验证、参数调优
- 第5-7天：实验脚本开发和测试

### 5.2 第2-3周：大规模实验运行
- 第8-11天：基站数量影响实验（图9）
- 第12-14天：预算约束影响实验（图10）
- 第15-16天：工作负载规模实验（图11）

### 5.3 第4周：结果分析和图表生成
- 第17-19天：数据验证和清理
- 第20-21天：图表生成和优化
- 第22-23天：统计分析和显著性检验
- 第24-25天：结果报告和论文更新

### 5.4 总计时间：约4周（28天）

## 6. 资源需求估算

### 6.1 计算资源
- CPU：至少4核心，推荐8核心
- 内存：至少16GB，推荐32GB
- 存储：至少50GB可用空间
- 运行时间：连续运行2-3周

### 6.2 人力资源
- 实验监控：每天检查1-2次
- 问题处理：及时处理运行错误
- 结果分析：专门的数据分析时间

这个完整的实验运行指南提供了从环境准备到结果分析的全流程指导，确保能够产生高质量的实验结果来回应审稿人的意见。
