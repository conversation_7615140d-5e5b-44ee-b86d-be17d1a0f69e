"""
简化的测试脚本，逐步验证各个组件
"""

def test_imports():
    """测试导入"""
    print("=== Testing Imports ===")
    
    try:
        from utils import DataUtils
        print("✓ DataUtils imported")
    except Exception as e:
        print(f"✗ DataUtils import failed: {e}")
        return False
    
    try:
        from generate_topology import generate_topology
        print("✓ generate_topology imported")
    except Exception as e:
        print(f"✗ generate_topology import failed: {e}")
        return False
    
    try:
        from algorithm import TopKServerPlacer, RandomServerPlacer
        print("✓ Traditional algorithms imported")
    except Exception as e:
        print(f"✗ Traditional algorithms import failed: {e}")
        return False
    
    try:
        from multi_objective_algorithms import NSGAIIServerPlacer
        print("✓ NSGA-II imported")
    except Exception as e:
        print(f"✗ NSGA-II import failed: {e}")
        return False
    
    return True

def test_data_loading():
    """测试数据加载"""
    print("\n=== Testing Data Loading ===")
    
    try:
        from utils import DataUtils
        data = DataUtils('./dataset/basestations.csv', './dataset/userdata.csv')
        print(f"✓ Data loaded: {len(data.base_stations)} base stations")
        return True, data
    except Exception as e:
        print(f"✗ Data loading failed: {e}")
        return False, None

def test_topology_generation():
    """测试拓扑生成"""
    print("\n=== Testing Topology Generation ===")
    
    try:
        from utils import DataUtils
        from generate_topology import generate_topology
        
        data = DataUtils('./dataset/basestations.csv', './dataset/userdata.csv')
        n = 20  # 小规模测试
        delay_matrix = generate_topology(data, n)
        print(f"✓ Topology generated for {n} base stations")
        return True, data, delay_matrix
    except Exception as e:
        print(f"✗ Topology generation failed: {e}")
        return False, None, None

def test_nsga2_simple():
    """测试NSGA-II算法"""
    print("\n=== Testing NSGA-II Algorithm ===")
    
    try:
        from utils import DataUtils
        from generate_topology import generate_topology
        from multi_objective_algorithms import NSGAIIServerPlacer
        
        # 加载数据
        data = DataUtils('./dataset/basestations.csv', './dataset/userdata.csv')
        n = 20
        delay_matrix = generate_topology(data, n)
        
        # 创建算法实例
        algorithm = NSGAIIServerPlacer(data.base_stations[:n], delay_matrix)
        
        # 设置参数
        budget = 5
        t_max = 50
        rate = 1.0
        initial_basestations = {2: 50000, 8: 50000}
        seed = 100
        
        print(f"Running NSGA-II with {n} base stations...")
        
        # 运行算法
        algorithm.place_server(
            base_station_num=n,
            budget=budget,
            t_max=t_max,
            rate=rate,
            initial_basestations=initial_basestations,
            cross_probability=0.8,
            mutation_probability=0.1,
            seed=seed
        )
        
        # 检查结果
        if hasattr(algorithm, 'edge_servers') and algorithm.edge_servers is not None:
            print(f"✓ NSGA-II completed: {len(algorithm.edge_servers)} edge servers")
            
            # 计算目标函数
            delay = algorithm.objective_latency()
            workload = algorithm.objective_workload()
            fitness = algorithm.objective_fitness()
            
            print(f"  - Delay: {delay:.2f} ms")
            print(f"  - Workload std: {workload:.2f}")
            print(f"  - Fitness: {fitness:.4f}")
            
            return True
        else:
            print("✗ NSGA-II failed - no edge servers")
            return False
            
    except Exception as e:
        print(f"✗ NSGA-II test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_traditional_algorithm():
    """测试传统算法"""
    print("\n=== Testing Traditional Algorithm ===")
    
    try:
        from utils import DataUtils
        from generate_topology import generate_topology
        from algorithm import TopKServerPlacer
        
        # 加载数据
        data = DataUtils('./dataset/basestations.csv', './dataset/userdata.csv')
        n = 20
        delay_matrix = generate_topology(data, n)
        
        # 创建算法实例
        algorithm = TopKServerPlacer(data.base_stations[:n], delay_matrix)
        
        # 设置参数
        budget = 5
        t_max = 50
        rate = 1.0
        initial_basestations = {2: 50000, 8: 50000}
        seed = 100
        
        print(f"Running TopFirst with {n} base stations...")
        
        # 运行算法（注意参数名拼写）
        algorithm.place_server(
            base_station_num=n,
            budget=budget,
            t_max=t_max,
            rate=rate,
            initial_basestations=initial_basestations,
            cross_proability=0.8,  # 注意拼写错误
            mutation_proability=0.1,  # 注意拼写错误
            seed=seed
        )
        
        # 检查结果
        if hasattr(algorithm, 'edge_servers') and algorithm.edge_servers is not None:
            print(f"✓ TopFirst completed: {len(algorithm.edge_servers)} edge servers")
            return True
        else:
            print("✗ TopFirst failed - no edge servers")
            return False
            
    except Exception as e:
        print(f"✗ TopFirst test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("GAPSO Simple Test Suite")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("Import test failed, stopping.")
        return
    
    # 测试数据加载
    success, data = test_data_loading()
    if not success:
        print("Data loading test failed, stopping.")
        return
    
    # 测试拓扑生成
    success, data, delay_matrix = test_topology_generation()
    if not success:
        print("Topology generation test failed, stopping.")
        return
    
    # 测试传统算法
    if test_traditional_algorithm():
        print("✓ Traditional algorithm test passed")
    else:
        print("✗ Traditional algorithm test failed")
    
    # 测试NSGA-II
    if test_nsga2_simple():
        print("✓ NSGA-II test passed")
    else:
        print("✗ NSGA-II test failed")
    
    print("\n" + "=" * 50)
    print("Simple test completed!")

if __name__ == "__main__":
    main()
