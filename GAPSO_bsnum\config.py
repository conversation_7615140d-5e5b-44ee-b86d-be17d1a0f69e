import random
# 选择topology参数
max_link = 20  # 初始化topology时，单个节点允许链接多少个相邻节点
max_dis = 5   # 可以接入的节点最远距离,单位km
alpha = 5  # 每km链路长度的延迟，单位ms
beta = 20  # 每跳的延迟，单位ms
# 选择初始参数
start_num = 500  # 开始基站数
end_num = 650  # 结束基站数
step = 50  # 步长
repetitions = 50  # 算法重复次数
seed = 100
# 算法参数
pop = 40  # 种群数
MAX_GENERATION = 500  # 最大迭代数
single_max_workload = 100000  # 单个服务器的工作负载上限
fix_cost = 0.75  # 租金的固定成本
distance_to_cost = -0.2   # 距离和租金的转换系数
install_cost = 1.25  # 单个服务器的设备成本
r_to_center = 1
th = 0.8  # 扩容阈值
sample_num = 1  # link_edge的抽样个数
nar = 1  # 缩小范围参数
c_max = 3  # 边缘服务器的上限
two_party_num_rate = 0.5
two_party_num_rate0 = 0
two_party_num_rate1 = 1
# two_party_num_rate2 = 0.8
# two_party_num_rate3 = 1
remote_delay = 100
t_max = 100
w0 = 1
w1 = 1
w2 = 1.5
w3 = 1
# 300 400 500 600 700 800 900
