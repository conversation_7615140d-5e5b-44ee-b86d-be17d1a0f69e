#!/usr/bin/env python3
"""
测试实验框架
验证实验脚本的正确性，运行小规模测试
"""

import os
import sys
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_single_algorithm(algorithm_name, n=100, budget=15, rate=1.0, seed=100):
    """测试单个算法"""
    
    try:
        # 导入必要模块
        from utils import DataUtils
        from generate_topology import generate_topology
        from algorithm import (
            TopKServerPlacer, RandomServerPlacer, ACOServerPlacer, 
            GAPSOServerPlacer, PSOServerPlacer
        )
        from multi_objective_algorithms import (
            NSGAIIServerPlacer, MOEADServerPlacer, DifferentialEvolutionServerPlacer
        )
        
        # 加载数据
        data = DataUtils('./dataset/basestations.csv', './dataset/userdata.csv')
        base_stations = data.base_stations
        delay_matrix = generate_topology(data, n)
        
        # 初始基站配置
        initial_basestations = {
            int(n*0.1): 100000, int(n*0.25): 100000, int(n*0.4): 100000,
            int(n*0.55): 100000, int(n*0.7): 100000, int(n*0.85): 100000
        }
        
        # 选择算法
        algorithms = {
            'Top-K': TopKServerPlacer,
            'Random': RandomServerPlacer,
            'ACO': ACOServerPlacer,
            'GA-PSO': GAPSOServerPlacer,
            'PSO': PSOServerPlacer,
            'NSGA-II': NSGAIIServerPlacer,
            'MOEA-D': MOEADServerPlacer,
            'DE': DifferentialEvolutionServerPlacer
        }
        
        if algorithm_name not in algorithms:
            return False, f"未知算法: {algorithm_name}"
            
        algorithm = algorithms[algorithm_name](base_stations[:n], delay_matrix)
        
        # 运行算法
        start_time = time.time()
        
        try:
            if algorithm_name in ['NSGA-II', 'MOEA-D', 'DE']:
                # 多目标算法
                algorithm.place_server(
                    base_station_num=n,
                    budget=budget,
                    t_max=100,
                    rate=rate,
                    initial_basestations=initial_basestations,
                    cross_probability=0.8,
                    mutation_probability=0.1,
                    seed=seed
                )
            else:
                # 传统算法
                algorithm.place_server(
                    base_station_num=n,
                    budget=budget,
                    t_max=100,
                    rate=rate,
                    initial_basestations=initial_basestations,
                    cross_proability=0.8,
                    mutation_proability=0.1,
                    seed=seed
                )
        except Exception as e:
            return False, f"算法运行失败: {str(e)}"
        
        end_time = time.time()
        runtime = end_time - start_time
        
        # 计算结果
        if hasattr(algorithm, 'edge_servers') and algorithm.edge_servers:
            try:
                # 获取性能指标
                delay_result = algorithm.objective_latency()
                workload_result = algorithm.objective_workload()
                fitness_result = algorithm.objective_fitness()
                
                # 处理不同算法的返回值格式
                delay = delay_result[0] if isinstance(delay_result, tuple) else delay_result
                workload = workload_result[0] if isinstance(workload_result, tuple) else workload_result
                fitness = fitness_result[0] if isinstance(fitness_result, tuple) else fitness_result
                
                num_servers = len(algorithm.edge_servers)
                
                result = {
                    'algorithm': algorithm_name,
                    'n': n,
                    'budget': budget,
                    'rate': rate,
                    'seed': seed,
                    'fitness': float(fitness),
                    'delay': float(delay),
                    'workload': float(workload),
                    'num_servers': num_servers,
                    'runtime': runtime
                }
                
                return True, result
                
            except Exception as e:
                return False, f"结果计算失败: {str(e)}"
        else:
            return False, "无边缘服务器生成"
            
    except Exception as e:
        return False, f"测试失败: {str(e)}"

def test_all_algorithms():
    """测试所有算法"""
    
    print("=" * 80)
    print("GAPSO实验框架测试")
    print("=" * 80)
    
    algorithms = ['Top-K', 'Random', 'ACO', 'GA-PSO', 'PSO', 'NSGA-II', 'MOEA-D', 'DE']
    
    print(f"测试参数: N=100, budget=15, rate=1.0, seed=100")
    print(f"测试算法: {', '.join(algorithms)}")
    print("-" * 80)
    
    results = {}
    total_start_time = time.time()
    
    for algorithm in algorithms:
        print(f"测试 {algorithm}...", end=' ')
        
        start_time = time.time()
        success, result = test_single_algorithm(algorithm)
        end_time = time.time()
        
        if success:
            results[algorithm] = result
            print(f"✅ 成功 ({end_time - start_time:.1f}s)")
            print(f"    fitness={result['fitness']:.4f}, delay={result['delay']:.2f}, "
                  f"workload={result['workload']:.2f}, servers={result['num_servers']}")
        else:
            print(f"❌ 失败: {result}")
    
    total_end_time = time.time()
    total_time = total_end_time - total_start_time
    
    print("-" * 80)
    print(f"测试完成，总用时: {total_time:.1f}s")
    print(f"成功率: {len(results)}/{len(algorithms)} ({len(results)/len(algorithms)*100:.1f}%)")
    
    if results:
        print("\n算法性能排名 (按适应度):")
        sorted_results = sorted(results.items(), key=lambda x: x[1]['fitness'])
        
        for i, (algorithm, result) in enumerate(sorted_results, 1):
            print(f"{i:2d}. {algorithm:<10} fitness={result['fitness']:.4f} "
                  f"delay={result['delay']:.2f} workload={result['workload']:.2f}")
    
    return results

def test_experiment_script():
    """测试实验脚本的小规模运行"""
    
    print("\n" + "=" * 80)
    print("测试实验脚本 (小规模)")
    print("=" * 80)
    
    # 创建测试目录
    test_dir = 'experiments/test'
    os.makedirs(test_dir, exist_ok=True)
    
    # 测试参数
    test_params = {
        'base_station_numbers': [100, 150],  # 只测试2个规模
        'algorithms': ['GA-PSO', 'NSGA-II'],  # 只测试2个算法
        'budget': 15,
        'rate': 1.0,
        'repetitions': 2  # 只重复2次
    }
    
    print(f"测试参数: {test_params}")
    
    # 模拟实验运行
    from run_bsnum_experiment import run_single_experiment
    
    experiment_params = []
    for n in test_params['base_station_numbers']:
        for algorithm_name in test_params['algorithms']:
            for seed in range(100, 100 + test_params['repetitions']):
                experiment_params.append((n, algorithm_name, seed, test_params['budget'], test_params['rate']))
    
    print(f"总测试实验数: {len(experiment_params)}")
    
    successful_count = 0
    start_time = time.time()
    
    for i, params in enumerate(experiment_params, 1):
        n, algorithm_name, seed, budget, rate = params
        print(f"运行测试 {i}/{len(experiment_params)}: {algorithm_name}, N={n}, seed={seed}")
        
        result = run_single_experiment(params)
        if result:
            successful_count += 1
            
            # 保存到测试目录
            result_file = f'{test_dir}/{algorithm_name}_N{n}.txt'
            with open(result_file, 'a', encoding='utf-8') as f:
                result_line = f"seed={seed},N={n},t_max=100,budget={budget},rate={rate} {algorithm_name}的fitness:{result['fitness']},delay:{result['delay']},workload:{result['workload']},runtime:{result['runtime']:.2f}\n"
                f.write(result_line)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n测试完成:")
    print(f"成功率: {successful_count}/{len(experiment_params)} ({successful_count/len(experiment_params)*100:.1f}%)")
    print(f"总用时: {total_time:.1f}s")
    print(f"平均用时: {total_time/len(experiment_params):.1f}s/实验")
    
    # 检查生成的文件
    print(f"\n生成的结果文件:")
    if os.path.exists(test_dir):
        for file in os.listdir(test_dir):
            if file.endswith('.txt'):
                file_path = os.path.join(test_dir, file)
                with open(file_path, 'r') as f:
                    line_count = len(f.readlines())
                print(f"  {file}: {line_count} 行")

def test_monitoring():
    """测试监控脚本"""
    
    print("\n" + "=" * 80)
    print("测试监控功能")
    print("=" * 80)
    
    test_dir = 'experiments/test'
    
    if not os.path.exists(test_dir):
        print("测试目录不存在，请先运行实验脚本测试")
        return
    
    # 简单的监控测试
    from monitor_experiment import parse_result_file
    
    total_experiments = 0
    for file in os.listdir(test_dir):
        if file.endswith('.txt'):
            file_path = os.path.join(test_dir, file)
            results = parse_result_file(file_path)
            total_experiments += len(results)
            print(f"{file}: {len(results)} 个实验结果")
    
    print(f"总计: {total_experiments} 个实验结果")

def main():
    """主测试函数"""
    
    print("GAPSO实验框架完整性测试")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 测试所有算法
    algorithm_results = test_all_algorithms()
    
    # 2. 测试实验脚本
    if algorithm_results:
        test_experiment_script()
        
        # 3. 测试监控功能
        test_monitoring()
    
    print("\n" + "=" * 80)
    print("测试完成!")
    print("=" * 80)
    
    if len(algorithm_results) >= 6:  # 至少6个算法成功
        print("✅ 实验框架测试通过，可以开始大规模实验")
        print("\n下一步操作:")
        print("1. 运行基站数量实验: python run_bsnum_experiment.py")
        print("2. 监控实验进度: python monitor_experiment.py --dir experiments/bsnum --type bsnum")
        print("3. 生成实验图表: python generate_complete_figures.py")
    else:
        print("❌ 实验框架测试未完全通过，请检查算法实现")
        print("需要修复的问题:")
        
        all_algorithms = ['Top-K', 'Random', 'ACO', 'GA-PSO', 'PSO', 'NSGA-II', 'MOEA-D', 'DE']
        failed_algorithms = [alg for alg in all_algorithms if alg not in algorithm_results]
        
        for alg in failed_algorithms:
            print(f"  - {alg} 算法运行失败")

if __name__ == "__main__":
    main()
