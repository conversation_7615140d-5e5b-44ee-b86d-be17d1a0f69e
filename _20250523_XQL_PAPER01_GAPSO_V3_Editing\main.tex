\documentclass{article}
\usepackage{amsmath}
\usepackage{color,pxfonts,fix-cm}
\usepackage{latexsym}
\usepackage[mathletters]{ucs}
\DeclareUnicodeCharacter{46}{\textperiodcentered}
\DeclareUnicodeCharacter{8854}{$\ominus$}
\DeclareUnicodeCharacter{8592}{$\leftarrow$}
\DeclareUnicodeCharacter{8709}{$\emptyset$}
\DeclareUnicodeCharacter{951}{$\eta$}
\DeclareUnicodeCharacter{215}{$\times$}
\DeclareUnicodeCharacter{931}{$\Sigma$}
\DeclareUnicodeCharacter{8704}{$\forall$}
\DeclareUnicodeCharacter{955}{$\lambda$}
\DeclareUnicodeCharacter{8968}{$\lceil$}
\DeclareUnicodeCharacter{957}{$\nu$}
\DeclareUnicodeCharacter{958}{$\xi$}
\DeclareUnicodeCharacter{8804}{$\leq$}
\DeclareUnicodeCharacter{8969}{$\rceil$}
\DeclareUnicodeCharacter{8805}{$\geq$}
\DeclareUnicodeCharacter{8242}{$\prime$}
\DeclareUnicodeCharacter{8220}{\textquotedblleft}
\DeclareUnicodeCharacter{58}{$\colon$}
\DeclareUnicodeCharacter{8221}{\textquotedblright}
\DeclareUnicodeCharacter{183}{$\cdot$}
\DeclareUnicodeCharacter{124}{\textbar}
\DeclareUnicodeCharacter{964}{$\tau$}
\DeclareUnicodeCharacter{8217}{\textquoteright}
\DeclareUnicodeCharacter{945}{$\alpha$}
\DeclareUnicodeCharacter{8712}{$\in$}
\DeclareUnicodeCharacter{60}{\textless}
\DeclareUnicodeCharacter{946}{$\beta$}
\DeclareUnicodeCharacter{969}{$\omega$}
\DeclareUnicodeCharacter{62}{\textgreater}
\DeclareUnicodeCharacter{948}{$\delta$}
\DeclareUnicodeCharacter{926}{$\Xi$}
\usepackage[T1]{fontenc}
\usepackage[utf8x]{inputenc}
\usepackage{pict2e}
\usepackage{wasysym}
\usepackage[english]{babel}
\usepackage{tikz}
\pagestyle{empty}
\usepackage[margin=0in,paperwidth=612pt,paperheight=792pt]{geometry}
\begin{document}
\definecolor{color_29791}{rgb}{0,0,0}
\definecolor{color_30046}{rgb}{0,0,1}
\definecolor{color_274846}{rgb}{1,0,0}
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE 1}
\put(76.08301,-66.664){\fontsize{23.9103}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Budget-Constrained Edge Serv er Expansion}
\put(55.88,-94.56){\fontsize{23.9103}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Deplo yment via Genetic Algorithm and P article}
\put(189.968,-122.455){\fontsize{23.9103}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Sw arm Optimization}
\put(54.349,-145.768){\fontsize{10.9589}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Qinglong Xu, Zhenguo Gao* Senior Member , IEEE , Y ang Jiang, Qiren Gan, Y unlong Zhao, Hsiao-Chun}
\put(249.017,-159.118){\fontsize{10.9589}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W u* F ellow , IEEE}
\put(43.927,-209.856){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{it}\selectfont\color{color_29791}Abstract —Mobile edge computing enhances the perf ormance}
\put(33.964,-219.8179){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}of lo w-capability end de vices by offloading tasks to nearby}
\put(33.964,-229.7809){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}edge ser v ers, enabling timel y r esponses f or delay-sensiti v e,}
\put(33.964,-239.744){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}computation-intensi v e tasks. Ho we v er , the rapid and continuous}
\put(33.964,-249.7059){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}gr o wth of such tasks may soon exceed the capacity of the}
\put(33.964,-259.6689){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}initially deploy ed edge ser v er system. This calls f or deploying new}
\put(33.964,-269.6309){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}ser v ers meanwhile r e-using deploy ed ones f or sa ving in v estment,}
\put(33.964,-279.5939){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}leading to the emer gence of a no v el paradigm named as Edge}
\put(33.964,-289.5569){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Ser v er Expansion Deployment (ESED) her e. F or this ESED}
\put(33.964,-299.519){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}paradigm, aiming to simultaneously minimize the a v erage access}
\put(33.964,-309.482){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}delay between end de vices and edge ser v ers and the w orkload}
\put(33.964,-319.445){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}de viation among ser v ers, we studied the Budget-Constrained}
\put(33.964,-329.407){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}ESED (BC-ESED) pr oblem under the condit ion of a specified}
\put(33.964,-339.37){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}b udget constraint. W e f ormulate the pr oblem as a multi-objecti v e}
\put(33.964,-349.333){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}optimization pr oblem and pr o v e its NP-hardness. W e then}
\put(33.964,-359.295){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}pr opose an algorithm, by combining Genetic Algorithm (GA)}
\put(33.964,-369.258){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}and P article Swarm Optimization (PSO), named as GA-PSO.}
\put(33.964,-379.221){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}GA-PSO utilizes a f our -step iteration framew or k of selection,}
\put(33.964,-389.183){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}cr osso v er , mutation, and corr ection, wher e a no v el thr ee-party}
\put(33.964,-399.1461){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}globalbest-localbest-indi vidual cr osso v er operation, inspir ed by}
\put(33.964,-409.1081){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}PSO, complements the traditional tw o-party cr osso v er operation}
\put(33.964,-419.0711){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}in t he cr osso v er s tep. The con v er ge nce and time complexity}
\put(33.964,-429.0341){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}of GA-PSO ar e established and analyzed. Simulation r esults,}
\put(33.964,-438.9961){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}based on r ealistic netw ork topologies and w orkload data fr om}
\put(33.964,-448.9591){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}the Shanghai T elecom base station dataset, demonstrate that}
\put(33.964,-458.9221){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}GA-PSO outp erf orms other benchmark algorithms in terms of}
\put(33.964,-468.8841){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}a v erage access delay and w orkload de viation.}
\put(43.92701,-484.9001){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{it}\selectfont\color{color_29791}Index T erms —mobile edge computing, edge ser v er expansion}
\put(33.964,-494.8621){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}deployment, Genetic Algorithm, P article Swarm Optimization,}
\put(33.964,-504.8251){\fontsize{8.9664}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}access delay , w orkload de viation}
\put(120.619,-537.3671){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}I}
\put(126.9253,-537.3671){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(132.4048,-537.3671){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}I N T R O D U C T I O N}
\put(33.964,-568.6941){\fontsize{28.4967}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}D}
\put(56.033,-555.5441){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}UE to the pre v alence and gro wing computational com-}
\put(56.033,-567.4991){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ple xity of applications on mobile end de vices, the de-}
\put(33.964,-579.4541){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}mand for e xternal computing resources is increasing contin-}
\put(33.964,-591.4091){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}uously [ 1 ]. When handling delay-sensiti v e and computation-}
\put(33.964,-603.3641){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}intensi v e tasks, such as autonomous dri ving [ 2 ], augmented}
\put(33.964,-615.3192){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}reality [ 3 ], and industrial automation [ 4 ], end de vices often}
\put(41.93401,-637.2532){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Q. L. Xu, Z. G. Gao, Y . Jiang and Q. R. Gan are with both the}
\put(33.964,-646.2191){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Department of Computer Science and T echnology in Huaqiao Uni v ersity ,}
\put(33.964,-655.1862){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and K e y Laboratory of Computer V ision and Machine Learning(Huaqiao}
\put(33.964,-664.1522){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Uni v ersity), Fujian Pro vince Uni v ersity , Xiamen, FJ, 361021, CHIN A. (e-mail:}
\put(33.964,-673.1182){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}<EMAIL> ); H.-C. W u is with the School of Electrical Engineering}
\put(33.964,-682.0851){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and Computer Science, Louisiana State Uni v ersity , Baton Rouge, LA 70803,}
\put(33.964,-691.0511){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}USA and also with the Inno v ation Cent er for AI Applications, Y uan Ze Uni-}
\put(33.964,-700.0181){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v ersity , Chungli 32003, T aiw an (e-mail: <EMAIL> ;}
\put(33.964,-708.9841){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Y . L. Zhao is with the School of Computer Science and T echnology , Nanjing}
\put(33.964,-717.9501){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Uni v ersity of Aeronautics and Astronautics, Nanjing, JS, 211100, CHIN A).}
\put(41.93401,-726.9172){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}This w ork w as supported by Natural Science F oundation of China under}
\put(33.964,-735.8832){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Grants 62372190.}
\put(296.978,-209.8562){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}struggle to pro vide timely responses due to computing re-}
\put(296.978,-221.8112){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}source limitations.}
\put(306.94,-233.7662){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T raditional cloud computing solutions [ 5 ] enable the of-}
\put(296.978,-245.7213){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}floading of comple x tasks from end de vices t o cloud serv ers}
\put(296.978,-257.6763){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}for quick er responses. Ho we v er , task aggre g ation at cloud data}
\put(296.978,-269.6323){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}centers can lead to netw ork congestion. Furtherm o r e, unstable}
\put(296.978,-281.5872){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}communication delay between end de vices and remote cloud}
\put(296.978,-293.5422){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}centers o v er the Internet can lead to violati on of service-}
\put(296.978,-305.4972){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}le v el agreements [ 6 ], [ 7 ], [ 8 ]. T o address these challenges, the}
\put(296.978,-317.4522){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Mobile Edge Computing (MEC) paradigm has emer ged, bring-}
\put(296.978,-329.4072){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ing po werful computing resources cl oser to end de vices [ 9 ].}
\put(296.978,-341.3632){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}By of floading tasks to nearby edge serv ers, end de vices can}
\put(296.978,-353.3182){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}achie v e timely responses to critical tasks.}
\put(306.94,-365.2732){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Deplo ying an adequate number of edge serv ers can ef fec-}
\put(296.978,-377.2281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ti v ely alle viate serv er o v erloading. Ho we v er , the deplo yable}
\put(296.978,-389.1831){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}number of edge serv ers is typically constrained by b udget}
\put(296.978,-401.1381){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}limitations. Consequently , the deplo yment of edge serv ers}
\put(296.978,-413.0941){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}plays a crucial role in promoting the perf ormances of the}
\put(296.978,-425.0491){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}entire}
\put(403.3985,-425.0491){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}}
\put(408.5193,-425.0491){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}Ho we v er , optimizing edge serv er}
\put(296.978,-437.0041){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}deplo yment is challenging. There ha v e been some w orks}
\put(296.978,-448.9591){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}focusing on this topic with v arious optimization objecti v es,}
\put(296.978,-460.9141){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}such as ener gy consumption [ 11 ], the a v erage access delay of}
\put(296.978,-472.869){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}the end de vices for accessing edge services [ 12 ], deplo yment}
\put(296.978,-484.825){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}profit function [ 13 ], or their combinations [ 14 ], [ 15 ], [ 16 ].}
\put(306.94,-496.78){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}Ho we v er , e xisting studies in the literature, such as [ 14 ], [ 17 ],}
\put(296.978,-508.735){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}[ 18 ], mainly focus on the optimal initial deplo yment of MEC}
\put(296.978,-520.69){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}systems from scratch. W ith the rapid gro wth in applications}
\put(296.978,-532.645){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}and heterogeneous demands [ 19 ], an initially deplo yed MEC}
\put(296.978,-544.601){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}system may quickly become incapable of satisfying the de-}
\put(296.978,-556.556){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}mands. T o tackle the conflict between the escalating demand}
\put(296.978,-568.511){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}and constrained capacity of e xisting MEC systems, a no v el}
\put(296.978,-580.466){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}paradigm denominated Edge Serv er Expansion Deplo yment}
\put(296.978,-592.421){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}(ESED) has emer ged. This in v olv es enhancing e xisting MEC}
\put(296.978,-604.376){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}systems by appending ne w resources to already-deplo yed}
\put(296.978,-616.332){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}edge serv ers or deplo ying additional edge serv ers, meanwhile}
\put(296.978,-628.287){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}preserving the original ones. This paradigm is particularly}
\put(296.978,-640.242){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}appealing to MEC service pro viders, as it enables incre-}
\put(296.978,-652.197){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}mental promotion of MEC systems while preserving prior}
\put(296.978,-664.152){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}in v estments. Gi v en the unique characteristics of ESED, the}
\put(296.978,-676.107){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}optimization of edge serv er deplo yment in the ESED paradigm}
\put(296.978,-688.063){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}is denominated as the ESED problem in this study .}
\put(306.94,-700.018){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Due to the strict coupling between edge serv er deplo yment}
\put(296.978,-711.973){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and w orkload allocation among serv ers, e xisting edge serv er}
\put(296.978,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}deplo yment algorithms for ne w MEC systems are not suitable}
\put(296.978,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}for}
\put(341.0027,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}}
\put(346.0239,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}As f ar as we kno w , there is no w ork focusing}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE 2}
\put(33.96402,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}on the ESED paradigm. T o address this g ap, we in v estig ated}
\put(33.96402,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}the Budget-Cons trained ESED (BC-ESED) problem with tw o}
\put(33.96402,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}primary optim izing objecti v es: minimi zing access delay and}
\put(33.96402,-90.30402){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}minimizing w orkload standard de viation among serv ers. By}
\put(33.96402,-102.259){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}incorporating b udget constraint, this problem formulation bet-}
\put(33.96402,-114.2141){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}ter aligns with real-w orld applications. W e formulate the BC-}
\put(33.96402,-126.1691){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}ESED problem and theoretically pro v e its NP-hardness. Then}
\put(33.96402,-138.1251){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}we combine Genetic Algorithm (GA) [ 20 ] and P article Sw arm}
\put(33.96402,-150.0801){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}Optimization (PSO) [ 21 ] to propose an algorithm named GA-}
\put(33.96402,-162.0351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}PSO to solv e BC-ESED. The con v er gence and time com-}
\put(33.96402,-173.9901){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}ple xity of GA-PSO are established and analyzed. Extensi v e}
\put(33.96402,-185.9451){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}simulations on realistic base station topology and w orkload}
\put(33.96402,-197.9001){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}statistics from the Shanghai T elecom base station dataset}
\put(33.96402,-209.8561){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}v alidate the superiority of GA-PSO o v er other benchmark}
\put(33.96402,-221.8112){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}algorithms.}
\put(43.92702,-233.7742){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}This paper’ s main contrib utions are summarized belo w:}
\put(33.96402,-247.7382){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(1)}
\put(45.58041,-247.7382){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(50.56171,-247.7382){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T o the best of our kno wledge, this is the first study to}
\put(50.56202,-259.6932){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}address the ESED paradigm. Specifically , we introduce}
\put(50.56202,-271.6492){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the BC-ESED problem and theoretically pro v e its NP-}
\put(50.56202,-283.6042){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}hardness.}
\put(33.96402,-295.5591){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(2)}
\put(45.58041,-295.5591){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(50.56171,-295.5591){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W e propose the GA-PSO algori thm to solv e the BC-ESED}
\put(50.56202,-307.5141){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}problem by combining GA and PSO. GA-PSO emplo ys}
\put(50.56202,-319.4691){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a four -step selection-crosso v er -mutation-correction frame-}
\put(50.56202,-331.4241){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}w ork. The con v er gence and time comple xity of GA-PSO}
\put(50.56202,-343.3801){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}are analyzed and e v aluated.}
\put(33.96402,-355.3351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(3)}
\put(45.58041,-355.3351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(50.56171,-355.3351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W e conduct simulation e xperiments on MEC systems}
\put(50.56202,-367.2901){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}with realistic topology and w orkload statistics from the}
\put(50.56202,-379.2451){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Shanghai T elecom base station dataset, and the results}
\put(50.56202,-391.2){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}demonstrate GA-PSO’ s superiority o v er other benchmark}
\put(50.56202,-403.156){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}algorithms in terms of a v erage access delay and w orkload}
\put(50.56202,-415.111){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}standard de viation.}
\put(43.92702,-429.075){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The}
\put(275.3881,-429.075){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(278.3868,-429.075){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}II}
\put(33.96402,-441.03){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}presents}
\put(217.5947,-441.03){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(221.3107,-441.03){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}III}
\put(231.2633,-441.03){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(234.9694,-441.03){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}pro vides the}
\put(33.96402,-452.985){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}system model, formalizes the BC-ESED problem, and pro v es}
\put(33.96402,-464.941){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}it}
\put(120.2899,-464.941){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(125.5402,-464.941){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}IV}
\put(136.0508,-464.941){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(141.3011,-464.941){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}pro vides a detailed description of}
\put(33.96402,-476.896){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}our GA-PSO algorithm, and analyzes its con v er gence and}
\put(33.96402,-488.851){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}time}
\put(122.0135,-488.851){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(125.4705,-488.851){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}V}
\put(132.6635,-488.851){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(136.1305,-488.851){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}presents and analyses the simulation}
\put(33.96402,-500.8059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}results.}
\put(116.3946,-500.8059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(119.8815,-500.8059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}VI}
\put(130.392,-500.8059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(133.8789,-500.8059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}concludes this paper .}
\put(116.545,-526.278){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}I}
\put(126.667,-526.278){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(132.1464,-526.278){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}R E L A T E D W O R K}
\put(33.964,-541.4189){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}A.}
\put(42.5418,-541.4189){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(47.5231,-541.4189){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Single-Objective Edg e Server Deployment Methods}
\put(43.92701,-556.5399){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Some studies ha v e e xplored edge serv er deplo yment prob-}
\put(33.964,-568.4949){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}lems by adopting a single optimization objecti v e based on}
\put(33.964,-580.45){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v arious metrics, such as the ener gy consumption of all edge}
\put(33.964,-592.405){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers, the a v erage access delay between users and edge}
\put(33.964,-604.361){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers, the required number of edge serv ers.}
\put(43.92701,-616.324){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W ith the purpose of minimizing the a v erage access delay}
\put(33.964,-628.2789){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}between end de vices and edge serv ers, Xu et al. [ 12 ] designed}
\put(33.964,-640.2339){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a heuristic algorithm and further propos ed an approximate}
\put(33.964,-652.189){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}algorithm for the special case where all serv ers e xhibit ho-}
\put(33.964,-664.1439){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}mogeneous processing capacities. Li et al. [ 22 ] introduced a}
\put(33.964,-676.0989){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}PSO-based algorithm to minimize the a v era g e access delay ,}
\put(33.964,-688.0549){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}where the deplo yment profit w as defined as a function of the}
\put(33.964,-700.0099){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}delay .}
\put(43.92701,-711.973){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Some studies ha v e focused on minimizing ener gy con-}
\put(33.964,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}sumption. F or instance, Li et al. [ 21 ] proposed a PSO-based}
\put(33.964,-735.8829){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}algorithm for ener gy-a w are edge serv er deplo yment, ensuring}
\put(296.978,-54.43896){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}that the access delay remains within a specified threshold.}
\put(296.978,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Y ang et al. [ 11 ] introduced a cloud placement algorithm using}
\put(296.978,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Benders decomposition, which enforces constraints on access}
\put(296.978,-90.30402){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}delay .}
\put(306.94,-102.527){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Aiming at minimizing the number of edge serv ers to be}
\put(296.978,-114.4821){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}deplo yed, Zeng et al. [ 23 ] constructed an algorithm com-}
\put(296.978,-126.4371){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}bining greedy heuristic and simulated annealing, considering}
\put(296.978,-138.3921){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}capacity-related constraints.}
\put(306.94,-150.6151){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W ith the obje cti v e of maximizing the co v erage area of edge}
\put(296.978,-162.5701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers, Cui et al. [ 24 ] considered potential f ailures of edge}
\put(296.978,-174.5251){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers and proposed tw o methods for dif ferent netw ork scales.}
\put(306.94,-186.7482){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W ith the aim of the total w orkload of edge serv ers, Huang}
\put(296.978,-198.7032){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}et al. [ 13 ] proposed a method by combining multi-armed}
\put(296.978,-210.6582){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}bandit algorithm and serv er coalition, ensuring that delay}
\put(296.978,-222.6132){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}requirements are satisfied.}
\put(306.94,-234.8362){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Aiming to maximize the number of successful tasks, Gao}
\put(296.978,-246.7913){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}et al. [ 25 ] considered a scenario where edge serv ers can be}
\put(296.978,-258.7463){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}deplo yed at both base stations and aggre g ation nodes to better}
\put(296.978,-270.7013){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}utilize computational resources, and proposed algorithms for}
\put(296.978,-282.6573){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}homogeneous and heterogeneous scenarios, respecti v ely .}
\put(296.978,-312.5763){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}B.}
\put(305.5558,-312.5763){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(310.5371,-312.5763){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Multi-Objective Edg e Server Deployment Methods}
\put(306.94,-328.6053){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T o jointly optimize the access delay between end de vices}
\put(296.978,-340.5612){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and edge serv ers and the w orkload de viation of edge serv ers,}
\put(296.978,-352.5162){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W ang et al. [ 14 ] proposed an algorithm by formulating the}
\put(296.978,-364.4712){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}problem as a mix ed-inte ger programming problem. Guo et}
\put(296.978,-376.4262){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}al. [ 26 ] combined K-means with mix ed-inte ger quadratic}
\put(296.978,-388.3812){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}programming, whereas Kasi et al . [ 15 ] e xploited GAs and}
\put(296.978,-400.3362){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}local search opt imization techniques. Guo et al. [ 16 ] designed}
\put(296.978,-412.2922){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a metaheuristic algorithm by combining K-means with an}
\put(296.978,-424.2472){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}impro v ed ant colon y algorithm.}
\put(306.94,-436.4692){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Aiming to jointly optimize access delay and ener gy con-}
\put(296.978,-448.4252){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}sumption, Y ada v et al. [ 27 ] proposed a dynamic computation}
\put(296.978,-460.3802){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of floading and resource allocation scheme for cloudlet nodes}
\put(296.978,-472.3351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}in v ehicular fog computing en vironments. Zhang et al. [ 28 ]}
\put(296.978,-484.2901){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}proposed a biogeograph y-based optimization algorithm. Later ,}
\put(296.978,-496.2451){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Asghari et al. [ 29 ] proposed an impro v ed trees social re-}
\put(296.978,-508.2001){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}lation algorithm by di viding the area into sub-re gions and}
\put(296.978,-520.1561){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}enabling inter -re gion cooperation to reduce comple xity . Xu}
\put(296.978,-532.1111){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}et al. [ 30 ] proposed a tw o-step approach: it initializes using}
\put(296.978,-544.0661){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Canop y and K-medoids clustering methods, and then e xploits}
\put(296.978,-556.0211){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}non-dominated sorting genetic algorithm to generate a final}
\put(296.978,-567.9761){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}solution.}
\put(306.94,-580.1991){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Cui et al. [ 31 ] focused on making an appropriate trade-}
\put(296.978,-592.1541){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of f between the rob ustness and user co v erage of the MEC}
\put(296.978,-604.1091){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}system, and proposed an inte ger programming method and an}
\put(296.978,-616.0641){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}approximate algorithm for small-scale and lar ge-scale prob-}
\put(296.978,-628.0201){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}lems, respecti v ely .}
\put(306.94,-640.2421){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Besides deplo ying edge serv ers, some studies also consider}
\put(296.978,-652.1971){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the deplo yment of services. F or e xample, Zhao et al. [ 32 ]}
\put(296.978,-664.1521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}proposed a tw o-stage method for deplo ying edge serv ers and}
\put(296.978,-676.1081){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}services. The fir st stage uses clustering to deplo y serv ers}
\put(296.978,-688.0631){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}for latenc y reduction and load balancing, while the second}
\put(296.978,-700.0181){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}stage applies a heuristic algorithm to deplo y services for}
\put(296.978,-711.9731){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}maximizing system profit. P an et al. [ 33 ] designed a three-}
\put(296.978,-723.9281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}step heuristic algorithm to impro v e system profitability and}
\put(296.978,-735.8841){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}resource utilization.}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE 3}
\put(55.19601,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}I}
\put(69.13368,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(74.61311,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}S Y S T E M M O D E L A N D P R O B L E M D E FI N I T I O N}
\put(33.964,-72.258){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}A.}
\put(42.5418,-72.258){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(47.5231,-72.258){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}System Model}
\put(43.92701,-88.94098){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W e consider an MEC system consists of end de vices, base}
\put(33.964,-100.896){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}stations, edge serv ers deplo yed at some base stations, and a}
\put(33.964,-112.851){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}remote cloud center in the Internet. Let V = \{ 1 , 2 , . . . , V \} be}
\put(33.96397,-124.807){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the set of base stations. The base stations are interconnected}
\put(33.96397,-136.762){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}through wire links such as optical fiber to form a connected}
\put(33.96397,-148.717){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}netw ork. Let G ( V , E ) denote the netw ork topology , where}
\put(33.96397,-160.6721){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}E ⊆V ×V is the set of wire links between base stations. Let}
\put(33.96396,-172.6271){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}h}
\put(39.70396,-174.1221){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(49.08697,-172.6271){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, l}
\put(56.48697,-174.1221){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(65.86997,-172.6271){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, ∀ i, j ∈V , denote the hop count and total length of}
\put(33.96397,-184.5821){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the shortest path between base stations i and j , respecti v ely .}
\put(33.96397,-196.5381){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The shortest paths between base stations are predetermined by}
\put(33.96397,-208.4931){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}using the Flo yd algorithm [ 34 ].}
\put(43.92698,-220.9021){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}An end de vice occasionally generates computation tasks,}
\put(33.96397,-232.8571){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}which are sent to its nearest base station. T o highlight the}
\put(33.96397,-244.8131){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}relationship between them, the end de vice is referred to as the}
\put(33.96397,-256.7681){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}base station’ s end de vice. Base stations collect the computation}
\put(33.96397,-268.7231){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}tasks of their end de vices and of fload them to proper edge}
\put(33.96397,-280.6781){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers or e v en the cloud serv er for a timely response to these}
\put(33.96397,-292.6331){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}tasks [ 25 ]. T o pro vide edge services, some edge serv ers are}
\put(33.96397,-304.5881){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}deplo yed at selected base stations. From a cost perspecti v e,}
\put(33.96397,-316.5441){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}we assume that edge serv ers can only be deplo yed at base}
\put(33.96397,-328.4991){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}stations, and call an edge serv er deplo yed at base station i as}
\put(33.96397,-340.4541){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}edge serv er i . W e also assume that an edge serv er can serv e}
\put(33.96397,-352.4091){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}multiple base stations, b ut each base station can of fload its}
\put(33.96397,-364.364){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}w orkload to only one edge serv er , whi ch is referred as its}
\put(33.96397,-376.319){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}master edge serv er . The process of assigning a base station to}
\put(33.96397,-388.275){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}its master edge serv er is called an "assignment". If an edge}
\put(33.96397,-400.23){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv er is deplo yed at base station i , then base station i must}
\put(33.96396,-412.185){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}use edge serv er i as its master edge serv er . In this scenario, a}
\put(33.96396,-424.14){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}base station serv es as the representati v e of its end de vic es for}
\put(33.96396,-436.095){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}utilizing the services of both the edge serv ers and the remote}
\put(33.96396,-448.051){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}cloud serv er . As such, we model the netw ork as a three-layer}
\put(33.96396,-460.006){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}hierarchical architecture consisting of user layer , edge layer ,}
\put(33.96396,-471.9609){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and cloud layer . The user layer comprises the base stations,}
\put(33.96396,-483.9159){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the edge layer consists of the edge serv ers, and the cloud layer}
\put(33.96396,-495.8709){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}corresponds to the remote cloud center . In this model, a base}
\put(33.96396,-507.8259){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}station aggre g ates all its end de vices. Consequently , the access}
\put(33.96396,-519.7819){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}delay in this paper is defined as the delay incurred when a base}
\put(33.96396,-531.7369){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}station accesses its master edge serv er .}
\put(43.92696,-544.1459){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}In ESED paradigm, some edge serv ers are already deplo yed.}
\put(33.96396,-556.1009){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Due to the cost or potential damage to ser v ers in transfer}
\put(33.96396,-568.0569){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}or reducing resources in e xisting serv ers, we assume that the}
\put(33.96396,-580.0119){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}already deplo yed edge serv ers cannot be reduced or remo v ed.}
\put(33.96396,-591.9669){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Furthermore, we assume that edge serv er resources can only}
\put(33.96396,-603.9219){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}be added in inte gral units, which we refer to as serv er units.}
\put(43.92696,-616.3319){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W e use an inte ger v ariable x}
\put(168.338,-617.8259){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(171.655,-616.3319){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}∈\{ 0 , 1 , 2 , . . . , x}
\put(234.91,-617.8259){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(250.11,-616.3319){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}\} to de-}
\put(33.96399,-628.2869){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}note the number of serv er units deplo yed at base station}
\put(33.96399,-640.2419){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i , where x}
\put(82.16499,-641.7359){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(103.487,-640.2419){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indicates the maximum number of serv er}
\put(33.96399,-652.1969){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}units that can be deplo yed at a base station. x}
\put(229.405,-653.6919){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(232.721,-652.1969){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=0 indicates}
\put(33.96399,-664.1519){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}that no edge serv er is deplo yed at base station i . W e refer}
\put(33.964,-676.1069){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the v ector x =[ x}
\put(102.036,-677.6019){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(106.506,-676.1069){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(116.627,-677.6019){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(121.097,-676.1069){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, . . . , x}
\put(148.93,-677.6019){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}V}
\put(155.848,-676.1069){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}] as an Edge Ser v er Deploy-}
\put(33.96397,-688.0629){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}ment (ESD) configuration . Similarly , an inte ger v ariable}
\put(33.96397,-700.0179){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}y}
\put(38.84797,-701.5119){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(42.16497,-700.0179){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}∈\{ 0 , 1 , 2 , . . . , V \} is used to indicate that base station i is}
\put(33.96399,-711.9729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}assigned to edge serv er y}
\put(141.556,-713.4669){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(144.873,-711.9729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}. Correspondingly , edge serv er y}
\put(281.706,-713.4669){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(33.96399,-723.9279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is called base station i ’ s master edge serv er . y}
\put(225.087,-725.4229){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(228.404,-723.9279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=0 is used to}
\put(33.96399,-735.8829){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}mean}
\put(300.115,-369.594){\includegraphics[width=244.7832pt,height=323.5211pt]{latexImage_ff6fb1662eacbc0b21924a85a95a6a1e.png}}
\put(296.978,-383.94){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig. 1. An e xample MEC system with an ESED solution.}
\put(296.978,-418.038){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The v ector y =[ y}
\put(365.952,-419.532){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(370.421,-418.038){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(379.734,-419.532){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(384.203,-418.038){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, . . . , y}
\put(411.226,-419.532){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}V}
\put(418.145,-418.038){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}] is referred as a Station-Edge}
\put(296.978,-429.993){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Assignment (SEA) configuration . A pair of (base station,}
\put(296.978,-441.948){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}edge serv er) is called an SEA pai r . x and y together form}
\put(296.9779,-453.904){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}an ESED solution. F or an ESED problem, let x}
\put(506.9399,-450.288){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(523.3669,-453.904){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and x}
\put(296.9779,-465.8589){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}be respecti v ely t he ESD configuration before and after edge}
\put(296.9779,-477.8139){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv er e xpansion, then there should be x}
\put(464.3989,-474.1989){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(475.5239,-477.8139){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}≤ x , which means}
\put(296.9779,-489.7689){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}x}
\put(302.6719,-486.1539){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(302.6719,-492.3619){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(313.7969,-489.769){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}≤ x}
\put(327.2389,-491.2629){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(330.5559,-489.769){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, ∀ i ∈V . Let S ( x ) be the edge serv er set in an ESD}
\put(296.9779,-501.7239){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}configuration x .}
\put(306.9399,-513.689){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig.}
\put(322.7206,-513.689){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(327.0145,-513.689){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}1}
\put(331.9958,-513.689){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(336.2897,-513.689){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}sho ws an e xample MEC system containing 6 base}
\put(296.9779,-525.6439){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}stations with tw o edge serv ers deplo yed at base stations}
\put(296.9779,-537.5989){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(537.9333,-537.5989){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(543.0541,-537.5989){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}1}
\put(296.9779,-549.5549){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}contains tw o parts. One is e xp a nd i ng edge serv er 3 from 1}
\put(296.9779,-561.5099){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv er unit to 2 serv er units, the other is deplo ying a ne w}
\put(296.9779,-573.4649){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}edge serv er with 1 serv er unit at base station 6. In other}
\put(296.9779,-585.4199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}w ords, the ESD configuration before and after e xpansion are}
\put(296.9779,-597.3749){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x}
\put(301.9589,-593.7599){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(313.0839,-597.3749){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=[0 , 2 , 1 , 0 , 0 , 0] and x =[0 , 2 , 2 , 0 , 0 , 1] , respecti v ely . The}
\put(296.9779,-609.3309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}corresponding}
\put(529.2958,-609.3309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(532.8524,-609.3309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}1}
\put(537.8337,-609.3309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(541.3904,-609.3309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is}
\put(296.9779,-621.2859){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y =[2 , 2 , 3 , 3 , 6 , 6] .}
\put(306.9398,-633.2509){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}F or eas y reference, the frequently-used symbols are sum-}
\put(296.9778,-645.2059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}marized}
\put(365.6799,-645.2059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(369.1668,-645.2059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}I .}
\put(296.9778,-672.9349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}B.}
\put(305.5556,-672.9349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(310.5369,-672.9349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Access Delay Model}
\put(306.9398,-688.0619){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W e assume that the access delay for base station i accessing}
\put(296.9778,-700.0179){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}edge serv er j depends on tw o f actors: the shortest path length}
\put(296.9778,-711.9729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}l}
\put(299.9498,-713.4669){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(312.3008,-711.9729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}from base station i to edge serv er j and the corresponding}
\put(296.9778,-723.9279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}hop count h}
\put(345.1188,-725.4219){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(354.5018,-723.9279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}. Specifically , we model the access delay t}
\put(523.8188,-725.4219){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(535.8618,-723.9279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}us-}
\put(296.9778,-735.8829){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ing Eq. ( 1 ), where α denotes transmission delay per kilometer}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE 4}
\put(140.504,-49.23804){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T ABLE I}
\put(129.297,-61.19305){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}S Y M B O L L I S T .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(38.984pt, -70.95599pt) -- (280.003pt, -70.95599pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -83.11102pt) -- (39.183pt, -71.15601pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(45.36,-79.52399){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Symbols}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -83.11102pt) -- (87.521pt, -71.15601pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-79.52399){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Description}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -83.11102pt) -- (279.803pt, -71.15601pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -83.31pt) -- (280.003pt, -83.31pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -95.46399pt) -- (39.183pt, -83.50897pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(53.663,-91.87799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}V , V}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -95.46399pt) -- (87.521pt, -83.50897pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-91.87799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}V denotes the base station set with size V .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -95.46399pt) -- (279.803pt, -83.50897pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -95.664pt) -- (280.003pt, -95.664pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -107.818pt) -- (39.183pt, -95.86298pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(60.861,-104.232){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -107.818pt) -- (87.521pt, -95.86298pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-104.232){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}An ESD configuration v ector .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -107.818pt) -- (279.803pt, -95.86298pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -108.017pt) -- (280.003pt, -108.017pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -120.172pt) -- (39.183pt, -108.217pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(60.861,-116.585){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -120.172pt) -- (87.521pt, -108.217pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-116.585){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}A SEA configuration v ector .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -120.172pt) -- (279.803pt, -108.217pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -120.371pt) -- (280.003pt, -120.371pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -144.481pt) -- (39.183pt, -120.571pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(53.597,-128.939){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}S ( x )}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -144.481pt) -- (87.521pt, -120.571pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-128.939){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Set of edges serv ers determined by the ESD}
\put(93.698,-140.894){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}configuration v ector x .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -144.481pt) -- (279.803pt, -120.571pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -144.68pt) -- (280.003pt, -144.68pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -156.834pt) -- (39.183pt, -144.879pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(58.842,-153.248){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}u}
\put(64.54501,-154.742){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -156.834pt) -- (87.521pt, -144.879pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-153.248){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W orkload of base station i .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -156.834pt) -- (279.803pt, -144.879pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -157.034pt) -- (280.003pt, -157.034pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -193.098pt) -- (39.183pt, -157.232pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(49.499,-165.601){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(56.631,-167.096){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(60.82899,-165.601){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, ω}
\put(73.008,-167.096){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -193.098pt) -- (87.521pt, -157.232pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-165.601){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(100.831,-167.096){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(109.533,-165.601){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}denotes the w orkload intensity of edge}
\put(93.69801,-177.557){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv er j , and ω}
\put(158.842,-179.051){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(167.441,-177.557){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}represents the normalized}
\put(93.69801,-189.512){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v ersion of w}
\put(145.284,-191.006){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(149.481,-189.512){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}.}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -193.098pt) -- (279.803pt, -157.232pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -193.298pt) -- (280.003pt, -193.298pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -229.362pt) -- (39.183pt, -193.496pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(47.004,-201.865){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(50.60201,-203.36){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(59.98401,-201.865){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, τ}
\put(70.31701,-203.36){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -229.362pt) -- (87.521pt, -193.496pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-201.865){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(97.296,-203.36){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(111.081,-201.865){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}denotes the access delay between base}
\put(93.698,-213.821){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}station i and edge serv er j , and τ}
\put(236.279,-215.315){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(249.846,-213.821){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}repre-}
\put(93.69798,-225.776){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}sents the normalized v ersion of t}
\put(229.13,-227.27){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(238.513,-225.776){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}.}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -229.362pt) -- (279.803pt, -193.496pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -229.562pt) -- (280.003pt, -229.562pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -253.671pt) -- (39.183pt, -229.761pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(56.339,-238.129){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(59.937,-239.624){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a vg}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -253.671pt) -- (87.521pt, -229.761pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-238.129){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}A v erage acces s delay between base stations}
\put(93.698,-250.085){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and their master edge serv ers.}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -253.671pt) -- (279.803pt, -229.761pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -253.87pt) -- (280.003pt, -253.87pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -277.98pt) -- (39.183pt, -254.07pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(55.468,-262.438){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(62.60001,-263.933){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}std}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -277.98pt) -- (87.521pt, -254.07pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-262.438){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W orkload standard de viation across all edge}
\put(93.698,-274.393){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers.}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -277.98pt) -- (279.803pt, -254.07pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -278.179pt) -- (280.003pt, -278.179pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -320.366pt) -- (39.183pt, -278.378pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(51.33,-286.747){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}C}
\put(58.451,-288.241){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(46.72001,-298.792){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(55.576,-295.177){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(66.701,-298.792){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x )}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -320.366pt) -- (87.521pt, -278.378pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-292.869){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T otal cost of an ESED solution with x}
\put(245.525,-289.254){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(259.241,-292.869){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and}
\put(93.698,-304.824){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x as the ESD configuration v ectors before}
\put(93.698,-316.779){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and after e xpansion.}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -320.366pt) -- (279.803pt, -278.378pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -320.565pt) -- (280.003pt, -320.565pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -356.63pt) -- (39.183pt, -320.764pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(48.387,-329.133){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}F ( x , y )}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -356.63pt) -- (87.521pt, -320.764pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-329.133){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The objecti v e function for an ESED solution}
\put(93.698,-341.088){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}formed by an ESD configuration v ector x}
\put(93.69801,-353.043){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and a SEA configuration v ector y .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -356.63pt) -- (279.803pt, -320.764pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -356.829pt) -- (280.003pt, -356.829pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -368.984pt) -- (39.183pt, -357.029pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(59.324,-365.397){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}B}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -368.984pt) -- (87.521pt, -357.029pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-365.397){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Budget constraint for the ESED problem.}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -368.984pt) -- (279.803pt, -357.029pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -369.183pt) -- (280.003pt, -369.183pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -381.337pt) -- (39.183pt, -369.382pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(53.953,-377.751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(57.551,-379.245){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -381.337pt) -- (87.521pt, -369.382pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-377.751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Maximum acceptable access delay .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -381.337pt) -- (279.803pt, -369.382pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -381.537pt) -- (280.003pt, -381.537pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -405.646pt) -- (39.183pt, -381.736pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(52.186,-390.104){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(59.31799,-391.599){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -405.646pt) -- (87.521pt, -381.736pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-390.104){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Maximum w orkload intensity that a serv er}
\put(93.698,-402.06){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}unit can sustain.}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -405.646pt) -- (279.803pt, -381.736pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -405.845pt) -- (280.003pt, -405.845pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -429.955pt) -- (39.183pt, -406.045pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(52.905,-414.413){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}x}
\put(58.599,-415.908){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -429.955pt) -- (87.521pt, -406.045pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-414.413){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Maximum number of serv er units that can}
\put(93.698,-426.368){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}be contained in an edge serv er .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -429.955pt) -- (279.803pt, -406.045pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -430.154pt) -- (280.003pt, -430.154pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -454.264pt) -- (39.183pt, -430.354pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(59.006,-438.722){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}r}
\put(63.501,-440.217){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -454.264pt) -- (87.521pt, -430.354pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-438.722){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The cost for renting a space at base station}
\put(93.698,-450.677){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j for deplo ying an edge serv er .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -454.264pt) -- (279.803pt, -430.354pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -454.463pt) -- (280.003pt, -454.463pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -466.618pt) -- (39.183pt, -454.663pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(58.839,-463.031){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}o}
\put(63.668,-464.525){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -466.618pt) -- (87.521pt, -454.663pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-463.031){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W orkload occupanc y rate of edge serv er j .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -466.618pt) -- (279.803pt, -454.663pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -466.817pt) -- (280.003pt, -466.817pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -478.971pt) -- (39.183pt, -467.016pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(60.147,-475.385){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}α}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -478.971pt) -- (87.521pt, -467.016pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-475.385){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T ransmission delay per kilometer .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -478.971pt) -- (279.803pt, -467.016pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -479.17pt) -- (280.003pt, -479.17pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -491.325pt) -- (39.183pt, -479.37pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(60.272,-487.738){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}β}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -491.325pt) -- (87.521pt, -479.37pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-487.738){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Processing delay for one-hop forw arding.}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -491.325pt) -- (279.803pt, -479.37pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -491.524pt) -- (280.003pt, -491.524pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -515.634pt) -- (39.183pt, -491.724pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(60.95,-500.092){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}δ}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -515.634pt) -- (87.521pt, -491.724pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-500.092){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Access delay from a base station to the}
\put(93.698,-512.047){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}remote cloud serv er .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -515.634pt) -- (279.803pt, -491.724pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -515.833pt) -- (280.003pt, -515.833pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -539.943pt) -- (39.183pt, -516.033pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(60.7,-524.401){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}η}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -539.943pt) -- (87.521pt, -516.033pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-524.401){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Cost for adding a serv er unit to an edge}
\put(93.698,-536.356){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv er .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -539.943pt) -- (279.803pt, -516.033pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -540.142pt) -- (280.003pt, -540.142pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -576.207pt) -- (39.183pt, -540.341pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(50.142,-548.71){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}p}
\put(55.154,-550.204){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -576.207pt) -- (87.521pt, -540.341pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-548.71){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Ratio of indi vidual pairs relati v e to the whole}
\put(93.698,-560.665){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}population size that will under go tw o-party}
\put(93.698,-572.62){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}normal crosso v er operation.}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -576.207pt) -- (279.803pt, -540.341pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -576.406pt) -- (280.003pt, -576.406pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -588.56pt) -- (39.183pt, -576.605pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(59.462,-584.974){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}P}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -588.56pt) -- (87.521pt, -576.605pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-584.974){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Population size of GA-PSO.}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -588.56pt) -- (279.803pt, -576.605pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -588.76pt) -- (280.003pt, -588.76pt)
;
\draw[color_29791,line width=0.398pt]
(39.183pt, -600.914pt) -- (39.183pt, -588.959pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(59.373,-597.327){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}T}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(87.521pt, -600.914pt) -- (87.521pt, -588.959pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(93.698,-597.327){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Maximum iteration number of GA-PSO.}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(279.803pt, -600.914pt) -- (279.803pt, -588.959pt)
;
\draw[color_29791,line width=0.398pt]
(38.984pt, -601.113pt) -- (280.003pt, -601.113pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-636.852){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and β denotes t he processing delay for one-hop forw arding.}
\put(33.964,-648.807){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W e reasonably assume that l}
\put(151.347,-650.302){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,i}
\put(159.848,-648.807){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=0 , h}
\put(182.746,-650.302){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,i}
\put(191.248,-648.807){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=0 for ∀ i ∈V .}
\put(85.46101,-667.487){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(89.05901,-668.982){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(98.44101,-667.487){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= α · l}
\put(118.34,-668.982){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(127.723,-667.487){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+ β · h}
\put(150.14,-668.982){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,j}
\put(159.523,-667.487){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, ∀ i ∈V , ∀ j ∈S ( x ) . (1)}
\put(43.92601,-688.062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}F or e xpression simplicity , the remote cloud serv er is some-}
\put(33.96401,-700.017){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}times referred as edge serv er 0. The access delay from base}
\put(33.96401,-711.973){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}station i to the remote cloud serv er (edge serv er 0) is assumed}
\put(33.96402,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}to be t}
\put(63.70502,-725.422){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i, 0}
\put(73.35902,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= δ for ∀ i ∈V , where δ is a constant. The a v erage}
\put(33.96402,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}access delay for base stations and their master edge s erv ers is}
\put(296.978,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}characterized by Eq. ( 2 ).}
\put(386.523,-78.19397){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(390.121,-79.68799){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a vg}
\put(402.64,-78.19397){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(411.584,-63.31995){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}P}
\put(422.1,-73.78094){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i ∈V}
\put(438.051,-70.79193){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(441.648,-72.28595){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,y}
\put(450.874,-73.28296){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(411.584pt, -75.703pt) -- (454.528pt, -75.703pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(429.043,-85.02802){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}V}
\put(455.723,-78.19403){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}. (2)}
\put(296.978,-109.653){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}C.}
\put(306.1137,-109.653){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(311.095,-109.653){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}W orkload Balancing Model}
\put(306.94,-124.979){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}All w orkload of the base stations should be of floaded to}
\put(296.978,-136.935){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}their master edge serv ers, and the w orkload of an edge serv er}
\put(296.978,-148.89){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is the sum of the w orkload from its associated base stations.}
\put(296.978,-160.845){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Considering that edge serv ers may dif fer in their number of}
\put(296.978,-172.8){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv er units, for a gi v en ESD configuration x , we define w}
\put(543.838,-174.295){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(296.978,-184.7551){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}via Eq. ( 3 ) as the w orkload intensity of edge serv er j . Here}
\put(296.9779,-196.7101){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}u}
\put(302.6809,-198.2051){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(309.4849,-196.7101){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is the w orkload of base station i .}
\put(348.0799,-221.0191){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(355.2129,-222.5141){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(359.4109,-221.0191){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(368.3549,-204.9301){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}P}
\put(378.8709,-215.3901){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i ∈\{ k | y}
\put(401.9509,-216.4481){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}k}
\put(406.3059,-215.3901){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= j ,k ∈V \}}
\put(439.6759,-212.4022){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}u}
\put(445.3789,-213.8962){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(368.355pt, -218.529pt) -- (448.696pt, -218.529pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(403.58,-227.853){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}x}
\put(409.273,-229.348){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(449.891,-221.019){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, ∀ j ∈S ( x ) . (3)}
\put(306.94,-244.249){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Let w}
\put(332.003,-245.744){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}std}
\put(347.821,-244.249){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}be the standard de viation of w orkload intensity}
\put(296.978,-256.205){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(referred as w orkload standard de viation) across all edge}
\put(296.978,-268.16){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers, as defined by Eq. ( 4 ). In this w ork, In this w ork,}
\put(296.978,-280.115){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}we use w}
\put(335.98,-281.609){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}std}
\put(350.635,-280.115){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}as a measure of the w orkload balance de gree.}
\put(364.422,-308.061){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(371.554,-309.556){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}std}
\put(382.722,-308.061){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(390.4709,-289.794){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}s}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(400.433pt, -289.594pt) -- (477.824pt, -289.594pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(401.629,-300.634){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Σ}
\put(408.824,-302.433){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j ∈S ( x )}
\put(433.504,-300.634){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( w}
\put(444.511,-302.128){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(450.922,-300.634){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}−}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(460.885pt, -294.95pt) -- (468.286pt, -294.95pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(460.885,-300.634){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w )}
\put(472.16,-297.756){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(401.629pt, -305.571pt) -- (476.629pt, -305.571pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(426.607,-314.895){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}|S ( x ) |}
\put(477.825,-308.061){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, (4)}
\put(296.978,-332.763){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}where}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(324.356pt, -327.078pt) -- (331.757pt, -327.078pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(324.356,-332.763){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w is the a v erage w orkload intensity of all edge serv ers,}
\put(296.978,-344.718){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}as defined by Eq. ( 5 ).}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(388.26pt, -364.005pt) -- (395.661pt, -364.005pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(388.26,-369.689){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w =}
\put(404.605,-353.599){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}P}
\put(415.121,-364.06){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j ∈S ( x )}
\put(441.461,-361.071){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(448.593,-362.566){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(404.605pt, -367.198pt) -- (452.791pt, -367.198pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(416.175,-376.523){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}|S ( x ) |}
\put(453.986,-369.689){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}. (5)}
\put(306.94,-392.559){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Let w}
\put(331.743,-394.054){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(351.334,-392.559){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}denote the maximum w orkload intensity that a}
\put(296.978,-404.515){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}single serv er unit can sustain. W e further define the w orkload}
\put(296.978,-416.47){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}occupanc y ratio o}
\put(370.974,-417.964){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(375.172,-416.47){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}:=}
\put(391.481,-411.417){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(397.244,-412.413){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(386.883pt, -413.979pt) -- (405.563pt, -413.979pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(386.883,-419.905){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(392.647,-420.902){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(410.987,-416.47){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}to quantify the relati v e w orkload}
\put(296.978,-428.425){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}intensity of edge serv er j with respect to this capacity limit.}
\put(296.978,-440.38){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}This metric is subsequently uti lized in the solution search}
\put(296.978,-452.335){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}process of our GA-PSO algorithm.}
\put(296.978,-480.547){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}D.}
\put(306.6617,-480.547){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(311.6429,-480.547){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Normalization of A ver a g e Access Delay and workload}
\put(311.643,-492.502){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Standar d De viation}
\put(306.94,-507.829){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Belonging to dif ferent ph ysical domains, the access delay}
\put(296.978,-519.7839){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(300.575,-521.278){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,y}
\put(309.8,-522.2739){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(316.174,-519.7839){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and w orkload intensity w}
\put(417.702,-521.278){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(424.62,-519.7839){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}usually e xhibit dif ferent orders}
\put(296.978,-531.739){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of magnitude. Therefore, it is necessary to normalize them}
\put(296.978,-543.694){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}before further processing. T o achie v e this, we normalize the}
\put(296.978,-555.6489){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}access delay and w orkload intensity respecti v ely using Eq. ( 6 ),}
\put(296.978,-567.6049){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}where t}
\put(328.401,-569.0989){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m}
\put(335.444,-567.6049){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=max \{ t}
\put(370.313,-569.0989){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,y}
\put(379.538,-570.095){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(383.193,-567.6049){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}| i ∈V \} , w}
\put(421.049,-569.0989){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m}
\put(428.0919,-567.6049){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= max \{ w}
\put(468.1569,-569.0989){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(472.3549,-567.6049){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}| j ∈S ( x ) \} .}
\put(382.99,-577.5779){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(}
\put(391.016,-587.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}τ}
\put(395.371,-589.314){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,y}
\put(404.596,-590.3109){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(408.25,-587.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= t}
\put(419.597,-589.314){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,y}
\put(428.822,-590.3109){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(432.476,-587.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}/t}
\put(441.055,-589.314){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m}
\put(448.098,-587.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791},}
\put(391.016,-602.166){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ω}
\put(397.217,-603.6599){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(401.415,-602.166){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= w}
\put(416.296,-603.6599){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(420.494,-602.166){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}/w}
\put(432.607,-603.6599){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m}
\put(439.65,-602.166){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}.}
\put(536.42,-594.614){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(6)}
\put(306.94,-620.614){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Subsequently , using Eq. ( 2 ) and Eq. ( 4 ) with the normalized}
\put(296.978,-632.569){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}access delay τ}
\put(354.684,-634.0629){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,y}
\put(363.909,-635.0599){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(370.728,-632.569){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and w orkload ω}
\put(435.173,-634.0629){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(439.37,-632.569){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, we obtain the normalized}
\put(296.978,-644.5239){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v alues of t}
\put(344.304,-646.0189){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a vg}
\put(361.936,-644.5239){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and w}
\put(388.567,-646.0189){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}std}
\put(399.735,-644.5239){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, respecti v ely . The y can be further}
\put(296.978,-656.4789){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}e xpressed as τ}
\put(360.952,-657.9739){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a vg}
\put(373.471,-656.4789){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) and ω}
\put(428.3839,-657.9739){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}std}
\put(439.5519,-656.4789){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) to emphasize their}
\put(296.9779,-668.434){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}dependencies on x and y .}
\put(296.9779,-696.6459){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}E.}
\put(305.5557,-696.6459){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(310.537,-696.6459){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Cost Model}
\put(306.9399,-711.973){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Solving an ESED problem requires e xpanding the edge}
\put(296.9779,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv er set by installing additional serv er units into e xisting}
\put(296.9779,-735.8829){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}edge serv ers and deplo ying ne w edge serv ers at some base}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE 5}
\put(33.96402,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}stations where no edge serv ers ha v e been deplo yed yet. The}
\put(33.96402,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}former incurs serv er unit purchase costs, whereas the latter}
\put(33.96402,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}incurs additional ph ysical space rental costs.}
\put(43.92702,-90.37402){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}No w , let x}
\put(90.28103,-86.758){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(106.63,-90.37402){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and x denote the ESD configuration v ec-}
\put(33.96401,-102.329){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}tors before and after the edge serv er e xpansion, respecti v ely .}
\put(33.96401,-114.2841){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Let S ( x}
\put(69.80202,-110.6691){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(80.92702,-114.2841){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}):= \{ i | x}
\put(112.193,-110.6691){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(112.193,-116.8771){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(123.318,-114.2841){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}> 0 , i ∈V \} , S ( x ):= \{ i | x}
\put(218.769,-115.7791){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(222.086,-114.2841){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}> 0 , i ∈V \} , and}
\put(33.96399,-126.2391){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}S}
\put(40.74399,-122.6241){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}expand}
\put(66.14199,-126.2391){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}:= S ( x ) \S ( x}
\put(116.785,-122.6241){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(127.91,-126.2391){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) . Thus, the rental cost C}
\put(231.079,-127.7341){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ren tal}
\put(251.758,-126.2391){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(260.614,-122.6241){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(271.739,-126.2391){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x )}
\put(33.96397,-138.1941){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}incurred by this ESED solution can be calculated using Eq. ( 7 ).}
\put(99.61098,-162.1751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}C}
\put(106.731,-163.6691){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ren tal}
\put(127.411,-162.1751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(136.266,-158.0611){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(147.391,-162.1751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x )=}
\put(180.974,-152.7101){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}X}
\put(170.084,-174.7661){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j ∈S}
\put(184.552,-172.7741){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}expand}
\put(207.916,-162.1751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}r}
\put(212.411,-163.6691){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(216.608,-162.1751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, (7)}
\put(33.96397,-190.5981){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}where r}
\put(66.57098,-192.0921){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(74.54298,-190.5981){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}denotes the cost for renting a space at base station}
\put(33.96398,-202.5531){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j for deplo ying an edge serv er . Here, the space rental cost at}
\put(33.96398,-214.5081){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a base station is a freely adjustable parameter , enabling the}
\put(33.96398,-226.4641){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}simulation of an y real-w orld geographical di strib ution of such}
\put(33.96398,-238.4191){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}costs. This can be achie v ed by appropriately setting the rental}
\put(33.96398,-250.3741){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}costs for each deplo yment location.}
\put(43.92698,-262.3992){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The cost of purc hasing serv er units is modele d using Eq. ( 8 ),}
\put(33.96398,-274.3542){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}where η represents the cost of adding one serv er unit to an}
\put(33.96398,-286.3091){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}edge serv er .}
\put(40.94298,-305.6521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}C}
\put(48.06298,-307.1461){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}device}
\put(70.02998,-305.6521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(78.88498,-301.5381){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(90.00998,-305.6521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x )=}
\put(122.414,-296.1871){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}X}
\put(112.703,-318.2431){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j ∈S ( x}
\put(133.771,-316.2511){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(143.402,-318.2431){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791})}
\put(148.176,-305.6521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}η · ( x}
\put(165.816,-307.1461){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(170.013,-305.6521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}− x}
\put(183.456,-301.5381){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(183.456,-308.1151){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(194.581,-305.6521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791})+}
\put(218.755,-296.1871){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}X}
\put(207.865,-318.2431){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j ∈S}
\put(222.332,-316.2511){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}expand}
\put(245.696,-305.6521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}η · x}
\put(259.462,-307.1461){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(263.66,-305.6521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}. (8)}
\put(43.927,-334.5861){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}By combining Eq. ( 7 ) and Eq. ( 8 ), the total cost C}
\put(268.1,-336.0801){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(33.964,-346.5411){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}incurred by the ESED solution is gi v en in Eq. ( 9 ).}
\put(59.99901,-365.2211){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}C}
\put(67.12001,-366.7151){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(84.04301,-365.2211){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(92.89802,-361.1071){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(104.023,-365.2211){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x )= C}
\put(132.176,-366.7151){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ren tal}
\put(152.856,-365.2211){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(161.711,-361.1071){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(172.836,-365.2211){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x )+ C}
\put(200.989,-366.7151){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}device}
\put(222.955,-365.2211){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(231.811,-361.1071){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(242.936,-365.2211){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x ) . (9)}
\put(43.92703,-383.9701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Notably , the cost solely depends on the tw o ESD config-}
\put(33.96403,-395.9261){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}uration v ectors x}
\put(105.237,-392.3101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(121.085,-395.9261){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and x , and is independent of the SEA}
\put(33.96402,-407.881){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}configuration v ector y . In f act, y influences the quality of an}
\put(33.96402,-419.836){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ESED solution by influencing the w orkload standard de viation}
\put(33.96402,-431.791){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}metric.}
\put(33.96402,-460.031){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}F}
\put(41.19687,-460.031){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(46.17817,-460.031){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Pr oblem Statement}
\put(43.92702,-475.369){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The BC-ESED problem addressed in this study requires}
\put(33.96402,-487.325){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}minimizing both the a v erage access delay and the w orkload}
\put(33.96402,-499.28){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}standard de viation. T o address this, we combine the tw o}
\put(33.96402,-511.235){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}objecti v es into a single weighted objecti v e, as defined in}
\put(33.96402,-523.1899){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Eq. ( 10 ), where λ is a weight parameter in [0,1]. The objecti v e}
\put(33.96401,-535.145){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}function e xplicitly models the dependencies of F on x and y .}
\put(44.24799,-565.85){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}F ( x , y )= λ · τ}
\put(94.86099,-567.345){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a vg}
\put(107.38,-565.85){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y )+(1 − λ ) · ω}
\put(172.527,-567.345){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}std}
\put(183.695,-565.85){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y )}
\put(74.17796,-591.49){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= λ ·}
\put(91.70095,-576.616){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}P}
\put(102.2169,-587.077){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i ∈ V}
\put(118.983,-584.089){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}τ}
\put(123.339,-585.583){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,y}
\put(132.564,-586.579){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(91.701pt, -589pt) -- (136.218pt, -589pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(109.947,-598.324){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}V}
\put(137.413,-591.49){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+(1 − λ ) ·}
\put(174.22,-573.222){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}s}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(184.183pt, -573.023pt) -- (255.374pt, -573.023pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(185.378,-584.063){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Σ}
\put(192.573,-585.861){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j ∈S ( x )}
\put(217.253,-584.063){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( ω}
\put(227.329,-585.557){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(231.526,-584.063){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}−}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(239.275pt, -578.378pt) -- (245.834pt, -578.378pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(239.275,-584.063){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ω )}
\put(249.708,-581.185){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(185.378pt, -589pt) -- (254.178pt, -589pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(207.255,-598.324){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}|S ( x ) |}
\put(255.373,-591.49){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}. (10)}
\put(43.92699,-616.261){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The BC-ESED problem can be formally stated as follo ws:}
\put(33.96399,-628.217){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Gi v en an MEC system with topology G ( V , E ) , w orkload}
\put(33.96397,-640.172){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}\{ u}
\put(44.64797,-641.666){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(47.96497,-640.172){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}| i ∈V \} , ESD configuration v ector bef or e expansion x}
\put(271.407,-636.556){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(282.532,-640.172){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791},}
\put(33.96399,-652.127){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}b udge B , and other additional r elated parameters, we}
\put(33.96399,-664.082){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}aim to determine an ESED solution consisting of an ESD}
\put(33.96399,-676.037){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}configuration v ector x and a SEA configuration v ector y}
\put(33.96399,-687.992){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}that minimizes F ( x , y ) while satisfying all constraints.}
\put(43.92698,-700.017){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The BC-ESED problem can be formulated as Eq. ( 11 ),}
\put(33.96398,-711.973){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}where Eq. ( 11b ) ensures that the b udget constraint B is}
\put(33.96399,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}satisfied, and Eq. ( 11c ) specifies the edge serv er e xpansion}
\put(33.96399,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}deplo yment restrictions. Eq. ( 11d ) requires that x}
\put(230.605,-737.377){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(236.433,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is an inte ger}
\put(296.978,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}in [0 , x}
\put(325.25,-55.93298){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(340.45,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}] . Eq. ( 11e ) ensures that base station i is assigned to}
\put(296.978,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}an edge serv er or the remote cloud serv er . Eq. ( 11f ) ensures}
\put(296.978,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}that the access delay is upper bounded by t}
\put(485.83,-79.84302){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(501.03,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}. Eq. ( 11g )}
\put(296.978,-90.30402){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}represents the w orkload intensity limit on an edge serv er .}
\put(296.978,-102.259){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Eq. ( 11h ) describes the dependenc y between x and y .}
\put(318.132,-120.939){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}min}
\put(321.764,-126.9171){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x , y}
\put(344.699,-120.939){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}F ( x , y ) , (11a)}
\put(311.435,-141.274){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}s . t . C}
\put(341.857,-142.7681){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(358.78,-141.274){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(367.635,-137.16){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(378.76,-141.274){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x ) ≤ B , (11b)}
\put(334.736,-156.806){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x ≥ x}
\put(352.4479,-152.692){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(363.5729,-156.806){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, (11c)}
\put(334.736,-171.75){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}x}
\put(340.43,-173.244){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(343.7469,-171.75){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}∈\{ 0 , 1 , 2 , . . . , x}
\put(407.0029,-173.244){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(422.2029,-171.75){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}\} , ∀ i ∈V , (11d)}
\put(334.7359,-186.694){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}y}
\put(339.6209,-188.188){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(342.9379,-186.694){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}∈S ( x ) ∪\{ 0 \} , ∀ i ∈V , (11e)}
\put(334.7358,-201.6379){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(338.3338,-203.132){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,y}
\put(347.5588,-204.1279){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(351.2128,-201.6379){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}≤ t}
\put(362.5598,-203.132){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(377.7598,-201.6379){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, ∀ i ∈V , (11f)}
\put(334.7358,-216.5819){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(341.8688,-218.0759){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\put(346.0668,-216.5819){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}≤ w}
\put(360.9478,-218.0759){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(376.1478,-216.5819){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, ∀ j ∈S ( x ) , (11g)}
\put(334.7358,-231.5259){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}x}
\put(340.4298,-233.0199){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}y}
\put(344.4708,-234.0159){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(348.1248,-231.5259){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}> 0 , ∀ i ∈V and y}
\put(493.1858,-233.0199){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(496.5028,-231.5259){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}> 0 , (11h)}
\put(306.9398,-250.0688){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Theor em 1. The BC-ESED problem is NP-hard.}
\put(306.9398,-267.8649){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{it}\selectfont\color{color_29791}Proof : The proof is pro vided in Appendix Sec. I. ■}
\put(308.1768,-291.0859){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}I}
\put(320.8891,-291.0859){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(326.3686,-291.0859){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G A - P S O F O R S O L V I N G T H E B C - E S E D P R O B L E M}
\put(306.9399,-306.0429){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T o solv e the NP-Hard BC-ESED problem ef ficiently , we}
\put(296.9779,-317.9979){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}propose a heuristic algorithm based on GA and PSO, which}
\put(296.9779,-329.9529){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is designated as GA-PSO in the follo wing te xt.}
\put(306.9399,-341.7719){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}GA is a heuristi c optimization method that emplo ys se-}
\put(296.9779,-353.7268){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}lection, crosso v er , and mutation to emulate the process of}
\put(296.9779,-365.6818){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}natural e v olution of a population. In GA, a candidate solution}
\put(296.9779,-377.6378){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is represented as a finite-length v ector , called a chromosome.}
\put(296.9779,-389.5928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Through the iterati v e application of genetic operators to a}
\put(296.9779,-401.5478){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}population of chromosomes, GA progressi v ely e v olv es candi-}
\put(296.9779,-413.5028){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}date solutions, with the theoretical e xpectation of con v er ging}
\put(296.9779,-425.4578){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}to w ard the global optimum.}
\put(306.9399,-437.2768){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}PSO addresses optimization problems through a population}
\put(296.9779,-449.2318){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of particles, col lecti v ely termed a sw arm. Inspired by social}
\put(296.9779,-461.1867){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}dynamics in biological s ystems (e.g., bird flocking or fish}
\put(296.9779,-473.1417){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}schooling), PSO models particles na vig ating a search space}
\put(296.9779,-485.0977){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}using iterati v e updates to their position and v elocity . Each}
\put(296.9779,-497.0527){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}particle’ s mo v ement is go v erned by mathematical equations}
\put(296.9779,-509.0077){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}that inte grate its personal best position (historical optimal}
\put(296.9779,-520.9626){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}solution found by the particle) and the global best position}
\put(296.9779,-532.9177){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(optimal solution disco v ered by the entire sw arm). F or nota-}
\put(296.9779,-544.8737){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}tional consistenc y in describing our GA-PSO h ybrid algorithm,}
\put(296.9779,-556.8287){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}we use the terms indi vidual and particle interchangeably ,}
\put(296.9779,-568.7837){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}recognizing their conceptual equi v al ence as basic units of the}
\put(296.9779,-580.7386){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}population.}
\put(306.9399,-592.5577){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Both GA and PSO ha v e indi vidual limitations: GA con-}
\put(296.9779,-604.5127){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v er ges slo wer , while PSO risks premature local trapping. Com-}
\put(296.9779,-616.4677){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}bining GA ’ s global search with PSO’ s local e xploitation, the}
\put(296.9779,-628.4227){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}h ybrid GA-PSO ef ficiently e xplores solution spaces, enhancing}
\put(296.9779,-640.3777){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}performance and escaping local optima.}
\put(306.9399,-652.1967){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Le v eraging GA ’ s rob ustness and PSO’ s history-guided up-}
\put(296.9779,-664.1517){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}dates, GA-PSO is e xpected to outperform s tandalone algo-}
\put(296.9779,-676.1067){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}rithms. Specifically , GA-PSO enhances the canonical GA}
\put(296.9779,-688.0627){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}frame w ork by inte grating a chromosome correction operator}
\put(296.9779,-700.0177){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}into i ts selection-crosso v er -mutation c ycle, forming a no v el}
\put(296.9779,-711.9727){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}four -stage iterati v e process: selection-crosso v er -mutation-}
\put(296.9779,-723.9277){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}correction. Additionally , during the crosso v er phase, GA-}
\put(296.9779,-735.8827){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}PSO int roduces a parallel three -party Globalbest-Localbest-}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE}
\put(90.452,-256.575){\includegraphics[width=138.0815pt,height=210.5063pt]{latexImage_af163d54d0878ca72db12fc7e0393ead.png}}
\put(33.964,-270.922){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig. 2. Outline of GA-PSO.}
\put(33.964,-305.336){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Indi vidual (G-L-I) crosso v er mechanism that complements the}
\put(33.964,-317.292){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}traditional tw o-party crosso v er operation. The flo wchart of}
\put(33.964,-329.247){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}GA-PSO}
\put(149.3907,-329.247){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(153.5351,-329.247){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}2 . In the follo wing subsections,}
\put(33.964,-341.202){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}we will describe the encoding scheme of an ESD solution as}
\put(33.964,-353.157){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}well as the w orkflo w of GA-PSO.}
\put(33.964,-381.896){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}A.}
\put(42.5418,-381.896){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(47.5231,-381.896){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Encoding Sc heme}
\put(43.927,-397.439){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}In GA-PSO, each indi vidual represents a ca nd i date solution}
\put(33.964,-409.394){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}to the BC-ESED problem with V base stations, comprising}
\put(33.964,-421.3489){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}tw o chromosome v ectors that encode the ESD configuration}
\put(33.964,-433.3039){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}v ector x and SEA configuration v ector y , respecti v ely . W e}
\put(33.964,-445.2589){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}denote the chromosomes of the ind -th indi vidual in gener -}
\put(33.96402,-457.2149){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}ation t as x}
\put(85.68903,-453.5989){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}t}
\put(85.68903,-460.0359){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}ind}
\put(103.052,-457.2149){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}(x-chromosome) and y}
\put(198.768,-453.5989){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}t}
\put(198.768,-460.0359){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}ind}
\put(216.131,-457.2149){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}(y-chromosome).}
\put(33.96402,-469.1699){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}Consequently , the indi vidual is formally represented as the}
\put(33.96402,-481.1249){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}chromosome tuple ( x}
\put(121.185,-477.5099){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}t}
\put(121.185,-483.9459){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}ind}
\put(133.574,-481.1249){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}, x}
\put(142.983,-477.5099){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}t}
\put(142.983,-483.9459){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}ind}
\put(155.372,-481.1249){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}) .}
\put(81.68101,-508.3359){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x}
\put(86.66301,-504.2229){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(86.66301,-510.7988){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(99.05201,-508.3358){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=[ x}
\put(115.262,-504.2228){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(115.262,-510.7988){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind, 1}
\put(133.988,-508.3358){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(144.11,-504.2228){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(144.11,-510.7988){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind, 2}
\put(162.836,-508.3358){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, . . . , x}
\put(190.669,-504.2228){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(190.669,-510.7988){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind,V}
\put(211.845,-508.3358){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}];}
\put(81.681,-523.5468){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y}
\put(86.663,-519.4337){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(86.663,-526.0677){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(99.052,-523.5468){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=[ y}
\put(114.81,-519.4337){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(114.452,-526.0098){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind, 1}
\put(133.179,-523.5468){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(142.849,-519.4337){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(142.491,-526.0098){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind, 2}
\put(161.218,-523.5468){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, . . . , y}
\put(188.599,-519.4337){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(188.241,-526.0098){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind,V}
\put(209.417,-523.5468){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}] .}
\put(268.425,-516.1398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(12)}
\put(33.96399,-552.5177){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}B.}
\put(42.54179,-552.5177){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(47.52309,-552.5177){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}GA-PSO Algorithm}
\put(43.92699,-568.0607){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}In this section, we describe the four steps in the selection-}
\put(33.96399,-580.0157){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v er -mutation-correction iteration of GA-PSO in turn.}
\put(43.92699,-592.1637){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}1)}
\put(52.22584,-592.1637){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(57.20714,-592.1637){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Selection Oper ation}
\put(43.92699,-604.2477){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}This step selects the indi vidual pairs for e x ecuting the tw o-}
\put(33.96399,-616.2028){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}party crosso v er operation and triplets under going the three-}
\put(33.96399,-628.1577){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}party G-L-I crosso v er mechanism. Let P represent the pop-}
\put(33.96399,-640.1127){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ulation size for a generation. W e define a parameter p}
\put(263.614,-641.6077){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(33.96399,-652.0677){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}to control the indi vidual pairs for e x ecuting the tw o-party}
\put(33.96399,-664.0237){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}normal crosso v er operation. Specifically , m}
\put(210.041,-665.5178){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(231.45,-664.0237){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= ⌈ p}
\put(248.639,-665.5178){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(270.047,-664.0237){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}· P ⌉}
\put(33.96397,-675.9788){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi vidual pairs will under go the tw o-party normal crosso v er}
\put(33.96397,-687.9337){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}operation. Here ⌈ x ⌉ means the minimum i nte ger not smaller}
\put(33.96398,-699.8887){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}than x . The remaining m}
\put(141.744,-701.3837){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3cross}
\put(163.152,-699.8887){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= P − m}
\put(195.177,-701.3837){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(221.036,-699.8887){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi viduals will}
\put(33.96396,-711.8438){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}under go the three-party G-L-I crosso v er operation.}
\put(43.92696,-723.9277){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The main purpose of our tw o-party normal crosso v er oper -}
\put(33.96396,-735.8828){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ation is to prioritize the inheritance of superior chromosomes}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.797pt]
(296.978pt, -45.87pt) -- (548.037pt, -45.87pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(301.959,-55.12598){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Algorithm 1: GA-PSO}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.797pt]
(296.978pt, -59.539pt) -- (548.037pt, -59.539pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(306.94,-68.79401){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Input: BS set V , b udget B , population size P , ESD}
\put(337.555,-80.75){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}configuration before e xpansion x}
\put(471.821,-77.13397){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(482.946,-80.75){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, iteration}
\put(337.555,-92.70502){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}number T , and other parameters;}
\put(306.94,-104.672){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Output:}
\put(341.2612,-104.672){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}}
\put(344.7481,-104.672){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x}
\put(349.729,-101.057){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(356.392,-104.672){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, y}
\put(367.351,-101.057){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(374.013,-104.672){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791};}
\put(298.472,-116.64){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}1 Initialize population P , x}
\put(410.658,-113.024){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(417.321,-116.64){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, y}
\put(428.2799,-113.024){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(434.9419,-116.64){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, t =0 ,}
\put(311.9219,-129.779){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}\{ v}
\put(321.8839,-126.164){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0}
\put(321.8839,-132.601){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(334.2729,-129.779){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(343.6819,-124.992){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0 , L}
\put(343.6818,-132.785){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(356.0708,-129.7791){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(365.4808,-124.9921){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0 , L}
\put(365.4808,-132.7851){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(377.8698,-129.7791){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}| p}
\put(385.6488,-126.1641){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0}
\put(385.6488,-132.6011){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(398.0378,-129.7791){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}∈P \} ;}
\put(298.4719,-141.7341){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}2 Sort all indi viduals p ∈P in ascending order of F ( p ) ;}
\put(298.4719,-153.6901){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}3 while t< T do}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(312.121pt, -397.975pt) -- (312.121pt, -157.276pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(298.87,-165.645){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}4 Select m}
\put(358.865,-167.139){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(383.761,-165.645){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi vidual pairs and m}
\put(476.881,-167.139){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3cross}
\put(327.264,-177.6){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi viduals;}
\put(298.87,-189.5551){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}5 Perform the tw o-party normal crosso v er operation}
\put(327.2639,-201.5101){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}with parameter p}
\put(396.7939,-203.0051){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v er}
\put(432.1019,-201.5101){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}for the m}
\put(471.6139,-203.0051){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(327.2639,-213.4651){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi vidual pairs;}
\put(298.8699,-225.4211){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}6 Perform the three-party G-L-I crosso v er operation}
\put(327.2639,-237.3761){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}with parameter p}
\put(396.7939,-238.8701){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v er}
\put(432.1019,-237.3761){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}for the m}
\put(471.6139,-238.8701){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3cross}
\put(327.2639,-249.3311){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi viduals;}
\put(298.8708,-261.2861){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}7 Perform mutation operation with parameter}
\put(327.2638,-273.2411){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}p}
\put(332.2758,-274.7361){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m utation}
\put(367.8168,-273.2411){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}for the 2 m}
\put(412.3098,-274.7361){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(433.7188,-273.2411){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+ m}
\put(450.2148,-274.7361){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3cross}
\put(475.1108,-273.2411){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of fspring}
\put(327.2639,-285.1961){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi viduals;}
\put(298.8708,-297.1521){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}8}
\put(499.7566,-297.1521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(503.2435,-297.1521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}2 ;}
\put(298.8708,-309.1071){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}9 Calculate F for all of fspring indi viduals using ( 10 );}
\put(295.3838,-322.2461){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}10 Update x}
\put(359.5228,-317.4591){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(359.5228,-325.2521){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(371.9118,-322.2461){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, y}
\put(382.8708,-317.4591){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(382.8708,-325.2521){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(398.7468,-322.2461){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of all of fspring indi viduals;}
\put(295.3838,-334.2141){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}11 Update x}
\put(359.5228,-330.5981){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(366.1858,-334.2141){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, y}
\put(377.1447,-330.5981){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(383.8067,-334.2141){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791};}
\put(295.3837,-346.1691){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}12 Combine the of fspring indi viduals with the parent}
\put(327.2637,-358.1241){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi viduals into a ne w population P}
\put(474.5557,-359.619){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(479.0257,-358.1241){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791};}
\put(295.3837,-370.079){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}13 Sort P}
\put(349.3067,-371.574){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(357.2627,-370.079){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}in ascending order of F ;}
\put(295.3837,-382.035){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}14 P = P}
\put(344.7096,-383.529){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(349.1786,-382.035){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[ : P ] ;}
\put(295.3836,-393.99){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}15 t = t +1 ;}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(312.32pt, -397.775pt) -- (317.301pt, -397.775pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(294.985,-409.942){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}16 r etur n x}
\put(342.746,-406.327){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(349.408,-409.942){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, y}
\put(360.3669,-406.327){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(367.0299,-409.942){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791};}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.797pt]
(296.978pt, -415.92pt) -- (548.037pt, -415.92pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.978,-449.186){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}by of fspring indi viduals, thereby enhancing the propag ation of}
\put(296.978,-461.141){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}high-quality genetic material. T o achie v e this, we restrict the}
\put(296.978,-473.097){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}pairs eligible for crosso v er . Specifically , we classify indi vidu-}
\put(296.978,-485.052){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}als in the population into tw o cate gories: elite and normal.}
\put(296.978,-497.007){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Elite indi viduals are the top 30\% of the population, while}
\put(296.978,-508.9619){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the remaining indi viduals are considered normal. W e restrict}
\put(296.978,-520.9169){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}that the tw o-party crosso v er operation can only occur between}
\put(296.978,-532.8729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}globalOpt-elite pairs and elite-normal pairs. Here a globalOpt-}
\put(296.978,-544.8279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}elite pair consists of the global optimal indi vidual and an}
\put(296.978,-556.783){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}elite indi vidual, while an elite-norm al pair contains an elite}
\put(296.978,-568.7379){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi vidual and a normal indi vidual.}
\put(306.94,-580.6359){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}F or the three-party G-L-I crosso v er operation, an indi vidual}
\put(296.978,-592.5919){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is selected to perform crosso v er with both its local-best}
\put(296.978,-604.5469){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi vidual and the global-best indi vi dual. This means that only}
\put(296.978,-616.502){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}one indi vidual needs to be chosen for each three-party G-L-}
\put(296.978,-628.4569){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}I crosso v er operation. Each selected indi vidual, together with}
\put(296.978,-640.4119){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}its local-best indi vidual and the global-best indi vidual, forms}
\put(296.978,-652.3669){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a three-tuple. The three-party G-L-I crosso v er operation will}
\put(296.978,-664.3229){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}be applied to all such tuples.}
\put(306.94,-676.1639){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}2)}
\put(315.2389,-676.1639){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(320.2202,-676.1639){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}T wo-party Normal Cr osso ver Oper ation}
\put(306.94,-688.062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}F or each of the m}
\put(388.984,-689.5569){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(415.613,-688.062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi vidual pairs selected in the}
\put(296.978,-700.0179){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}pre vious step, say ( x}
\put(381.882,-696.4019){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(381.882,-702.8389){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1}
\put(398.2419,-700.0179){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(407.6509,-696.4019){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(407.6509,-702.8389){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1}
\put(424.0109,-700.0169){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) and ( x}
\put(457.9049,-696.4019){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(457.9049,-702.8389){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2}
\put(474.2649,-700.0169){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(483.6739,-696.4019){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(483.6739,-702.8389){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2}
\put(500.0349,-700.0169){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) , a random}
\put(296.9779,-711.9729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}number r}
\put(335.2179,-713.4669){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(342.9979,-711.9729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is selected uniformly from [0 , 1] ( in later text, the}
\put(296.9778,-723.9279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}random v ariables ar e all assume d to f ollo w a unif orm}
\put(296.9778,-735.8829){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}distrib ution ). The crosso v er probability of an indi vidual pair}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE 7}
\put(33.96402,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}in the tw o-party normal crosso v er operation is controlled by}
\put(33.96402,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a param eter p}
\put(89.59602,-67.888){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v er}
\put(121.418,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}. If r}
\put(141.405,-67.888){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(145.874,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}≤ p}
\put(158.635,-67.888){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v e r}
\put(190.456,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, the pair ( x}
\put(239.018,-62.77795){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(239.018,-69.21497){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1}
\put(255.379,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(264.788,-62.77795){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(264.788,-69.21497){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1}
\put(281.148,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791})}
\put(33.96397,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and ( x}
\put(60.69298,-74.73297){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(60.69298,-81.16998){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2}
\put(77.05298,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(86.46198,-74.73297){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(86.46198,-81.16998){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2}
\put(102.822,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) will under go the crosso v er operation.}
\put(43.92698,-91.57703){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T o perform t he crosso v er operation, we create an inte ger}
\put(33.96398,-103.532){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v alue set S}
\put(83.46998,-105.027){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}cp}
\put(97.44798,-103.532){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of length ⌈}
\put(148.112,-99.61005){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}V}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(147.351pt, -101.042pt) -- (155.293pt, -101.042pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(147.351,-106.968){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}50}
\put(156.489,-103.532){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}⌉ , with the inte gers randomly}
\put(33.96399,-115.488){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}selected from [1 , V ] . The v alues in S}
\put(201.554,-116.9821){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}cp}
\put(216.027,-115.488){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}are u s ed as the}
\put(33.96397,-127.4431){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v er positions for e xchanging the e lements of the tw o x-}
\put(33.96397,-139.3981){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}chromosomes as well as the tw o y-chromosomes in the pair . T o}
\put(33.96397,-151.3531){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}be specific, the ne w indi viduals after the crosso v er operation,}
\put(33.96397,-163.3081){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}denoted as (}
\put(86.60098,-162.9451){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}¯}
\put(86.60098,-163.3081){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x}
\put(91.58198,-159.6931){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(91.58198,-166.1301){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1}
\put(107.943,-163.3081){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791},}
\put(112.371,-162.9451){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}¯}
\put(112.371,-163.3081){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y}
\put(117.352,-159.6931){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(117.352,-166.1301){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1}
\put(133.712,-163.3081){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) , are obtained with elements deter -}
\put(33.96397,-175.2641){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}mined using Eq. ( 13 ). (}
\put(136.016,-174.9001){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}¯}
\put(136.016,-175.2641){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x}
\put(140.997,-171.6481){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(140.997,-178.0851){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2}
\put(157.358,-175.2641){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791},}
\put(161.785,-174.9001){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}¯}
\put(161.785,-175.2641){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y}
\put(166.767,-171.6481){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(166.767,-178.0851){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2}
\put(183.127,-175.2631){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) are obtained similarly .}
\put(33.96396,-187.2191){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}In this w ay , m}
\put(98.86496,-188.7131){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(125.313,-187.2191){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi vidual pairs will lead to 2 m}
\put(263.6139,-188.7131){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(33.96393,-199.1741){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of fspring indi viduals.}
\put(85.99493,-225.4901){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}¯ x}
\put(91.05492,-221.3771){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(91.05492,-227.9531){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1 ,j}
\put(116.2489,-225.4901){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(126.7649,-208.454){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(}
\put(138.2769,-218.696){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}x}
\put(143.9709,-215.08){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(143.9709,-221.517){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2 ,j}
\put(166.3969,-218.696){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, if j ∈S}
\put(206.5489,-220.1901){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}cp}
\put(214.9889,-218.696){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791};}
\put(138.2769,-233.0421){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}x}
\put(143.9709,-229.426){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(143.9709,-235.863){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1 ,j}
\put(166.397,-233.0421){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, if j ∈V −S}
\put(221.221,-234.5361){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}cp}
\put(229.662,-233.0421){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}.}
\put(264.002,-225.4901){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(13a)}
\put(86.85495,-262.103){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}¯ y}
\put(91.41295,-257.9901){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(91.05595,-264.566){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1 ,j}
\put(116.2489,-262.103){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(126.765,-245.067){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(}
\put(138.278,-255.309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}y}
\put(143.519,-251.693){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(143.162,-258.13){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2 ,j}
\put(165.588,-255.309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, if j ∈S}
\put(205.739,-256.803){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}cp}
\put(214.1799,-255.309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791};}
\put(138.278,-269.655){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}y}
\put(143.519,-266.039){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(143.162,-272.476){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1 ,j}
\put(165.588,-269.655){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, if j ∈V −S}
\put(220.412,-271.149){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}cp}
\put(228.853,-269.655){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}.}
\put(263.4439,-262.103){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(13b)}
\put(43.92694,-289.309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig.}
\put(59.7077,-289.309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(63.57319,-289.309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}3}
\put(68.55449,-289.309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(72.41998,-289.309){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}illustrates the tw o-party normal crosso v er operation}
\put(33.96394,-301.265){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of tw o indi vidual pairs ( x}
\put(142.2549,-297.649){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(142.2549,-304.086){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1}
\put(158.6149,-301.264){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(168.0239,-297.649){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(168.0239,-304.0861){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1}
\put(184.3839,-301.2641){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) and ( x}
\put(219.8829,-297.6491){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(219.8829,-304.0861){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2}
\put(236.2439,-301.2641){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(245.6529,-297.6491){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(245.6529,-304.0861){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2}
\put(262.0129,-301.2641){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) that}
\put(33.96391,-313.2201){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v er at positions in S}
\put(142.7509,-314.7141){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}cp}
\put(151.1919,-313.2201){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(46.518,-422.716){\includegraphics[width=225.9499pt,height=89.50261pt]{latexImage_7c3b39a913a01fe86409fade8ec61db4.png}}
\put(33.964,-437.062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig. 3. Illustration of tw o-party normal crosso v er operation.}
\put(43.927,-465.222){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}3)}
\put(52.22585,-465.222){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(57.20715,-465.222){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Thr ee-party G-L-I Cr osso ver Oper ation}
\put(43.927,-478.45){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The normal crosso v er operation introduces high indi vidual}
\put(33.964,-490.405){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}di v ersity , which enhances global search capability b ut may}
\put(33.964,-502.361){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}hinder con v er gence. Inspired by PSO, we propose a three-}
\put(33.964,-514.316){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}party G-L-I crosso v er that uses global-best and local-best}
\put(33.964,-526.271){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}entities to guide optimization. Although these entities may not}
\put(33.964,-538.226){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}be current population members, the y are historical solutions}
\put(33.964,-550.181){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}found before, and we continue to term them "indi viduals" for}
\put(33.964,-562.136){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}terminological consistenc y .}
\put(43.927,-575.365){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}When applying the three-party G-L-I crosso v er operation}
\put(33.964,-587.32){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}to the three-tuple corresponding to an indi vidual, the three}
\put(33.964,-599.275){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi viduals in the set \{the indi vidual itself, the indi vidual’ s}
\put(33.964,-611.231){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}local best solut ion, the global best indi vidual\} are randomly}
\put(33.964,-623.186){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}fused follo wing a probability distrib ution determined by the}
\put(33.964,-635.141){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi viduals’ objecti v e function v alues. Specifically , let F}
\put(274.017,-631.526){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(272.633,-637.962){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(33.96399,-647.096){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}denote the objecti v e function v alue of the ind -th indi vidual in}
\put(33.96399,-659.051){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}generation t , F}
\put(96.89099,-654.264){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(95.50799,-662.057){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(111.395,-659.051){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}denote the objecti v e v al ue of its local-best}
\put(33.96399,-671.006){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}solution, and F}
\put(100.237,-667.391){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(111.647,-671.006){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}denote the objecti v e v alue of the global-}
\put(33.96399,-682.962){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}best solution found thus f ar . The corresponding probabilities}
\put(33.96399,-694.917){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of the three indi viduals, denoted as p}
\put(181.624,-691.301){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(181.624,-697.738){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(194.013,-694.917){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, p}
\put(204.166,-690.129){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(204.166,-697.922){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(219.204,-694.917){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and p}
\put(241.251,-691.301){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(247.914,-694.917){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, are then}
\put(33.96397,-706.872){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}determined using the sub-equations in Eq. ( 14 ).}
\put(94.79398,-730.398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}p}
\put(99.80598,-726.285){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(99.80598,-732.861){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(112.195,-730.398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(155.475,-723.658){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1 /F}
\put(173.228,-720.043){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(171.844,-726.48){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(121.14pt, -727.907pt) -- (218.569pt, -727.907pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(121.14,-738.855){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1 /F}
\put(138.893,-735.424){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(137.509,-741.861){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(149.898,-738.855){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+1 /F}
\put(175.4,-734.068){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(174.016,-741.861){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(186.405,-738.855){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+1 /F}
\put(211.907,-735.977){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(219.765,-730.398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, (14a)}
\put(357.808,-58.927){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}p}
\put(362.82,-54.13898){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(362.82,-61.93201){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(375.209,-58.927){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(418.489,-52.03601){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1 /F}
\put(436.242,-47.24902){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(434.858,-55.04205){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(384.153pt, -56.43597pt) -- (481.582pt, -56.43597pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(384.153,-67.38397){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1 /F}
\put(401.906,-63.95197){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(400.523,-70.38898){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(412.912,-67.38397){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+1 /F}
\put(438.413,-62.59595){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(437.03,-70.38898){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(449.419,-67.38397){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+1 /F}
\put(474.92,-64.50598){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(482.778,-58.927){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, (14b)}
\put(361.501,-92.23499){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}p}
\put(366.514,-88.12097){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(373.176,-92.23499){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(418.627,-85.495){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1 /F}
\put(436.38,-81.88){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(382.12pt, -89.74402pt) -- (479.549pt, -89.74402pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(382.12,-100.692){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1 /F}
\put(399.873,-97.26001){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(398.49,-103.697){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(410.879,-100.692){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+1 /F}
\put(436.38,-95.90399){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(434.997,-103.697){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(447.386,-100.692){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+1 /F}
\put(472.887,-97.81403){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(480.745,-92.23505){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}. (14c)}
\put(306.94,-118.194){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}PSO is typically used to optimize continuous v ariables.}
\put(296.978,-130.149){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Ho we v er , the optimization v ariables in BC-ESED are dis-}
\put(296.978,-142.1041){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crete. T o perform the three-party G-L-I crosso v er operation,}
\put(296.978,-154.0591){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}we redesign the v elocity and position update rules for the}
\put(296.978,-166.0141){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi viduals. F or e xpression bre vity , we introduce the follo wing}
\put(296.978,-177.9691){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v ector -related symbols: F or a v ector a , let a}
\put(496.467,-179.4641){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(505.923,-177.9691){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}denote its}
\put(296.978,-189.9251){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i -th element. If a has length N , then a =[ a}
\put(489.1839,-191.4191){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(493.6529,-189.9251){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, a}
\put(503.3469,-191.4191){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(507.8169,-189.9251){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, . . . , a}
\put(535.2219,-191.4191){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}N}
\put(542.778,-189.9251){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}] .}
\put(296.978,-201.8801){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W e hereby introduce three definitions: the operation ⊖ , and}
\put(296.978,-213.8351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the functions of V ecCombSel ( · ) and V ecCombProb ( · ) , as}
\put(296.978,-225.7902){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}follo ws.}
\put(306.94,-244.7402){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{it}\selectfont\color{color_29791}Definition 1. Operation ⊖ : F or tw o v ectors a and b with}
\put(296.978,-256.6952){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}length N , a ⊖ b generates a v ector with length N whose}
\put(296.978,-268.6502){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}elements represent the results of el ement-wise equality checks}
\put(296.978,-280.6062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}between the tw o input v ectors. Let z =[ z}
\put(462.846,-282.1002){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(467.315,-280.6062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, z}
\put(476.376,-282.1002){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(480.846,-280.6062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, . . . , z}
\put(507.618,-282.1002){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}N}
\put(515.174,-280.6062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}] denote}
\put(296.978,-292.5612){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}this result v ector , such that z = a ⊖ b . The i -th element z}
\put(524.8439,-294.0552){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(531.7369,-292.5612){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of z}
\put(296.9779,-304.5162){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is then defined by Eq. ( 15 ).}
\put(378.4569,-330.8322){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}z}
\put(383.0899,-332.3272){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(386.4069,-330.8322){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(395.8159,-313.7962){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(}
\put(407.3289,-324.0382){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1 , if a}
\put(440.3799,-325.5322){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(443.6969,-324.0382){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}̸ = b}
\put(455.7209,-325.5322){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(459.0378,-324.0382){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791},}
\put(407.3289,-338.3842){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0 , otherwise .}
\put(531.4379,-330.8322){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(15)}
\put(306.9399,-358.5002){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}F or e xample, if a =[1 , 0 , 2] and b =[1 , 0 , 1] , then}
\put(296.9778,-370.4552){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}z = a ⊖ b =[0 , 0 , 1] .}
\put(306.9398,-389.4052){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{it}\selectfont\color{color_29791}Definition 2. Function V ecCombSel ( a , b ; v ) : F or tw o v ec-}
\put(296.9778,-401.3602){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}tors a and b with length N , and a binary v ector v alsl}
\put(296.9777,-413.3152){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}with length N , calling the function V ecCombSel ( a , b ; v ) will}
\put(296.9777,-425.2701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}combine the v ectors a and b into a ne w length- N v ector}
\put(296.9778,-437.2251){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}according to the elements in v . Let z represents this result}
\put(296.9778,-449.1811){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v ector , such that z = V ecCombSel ( a , b ; v ) . The i -th element}
\put(296.9777,-461.1361){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}z}
\put(301.6107,-462.6301){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(308.4147,-461.1361){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of z is then determined using Eq. ( 16 ).}
\put(381.7017,-487.4521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}z}
\put(386.3347,-488.9461){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(389.6517,-487.4521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(399.0607,-470.4161){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(}
\put(410.5737,-480.6581){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}a}
\put(415.8397,-482.1521){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(419.1567,-480.6581){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, if v}
\put(446.7887,-482.1521){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(450.1057,-480.6581){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=0 ,}
\put(410.5737,-495.0041){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}b}
\put(414.8487,-496.4981){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(418.1657,-495.0041){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, if v}
\put(446.7887,-496.4981){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(450.1057,-495.0041){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=1 .}
\put(531.4387,-487.4521){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(16)}
\put(306.9397,-515.1191){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}F or e xample, if a =[1 , 0 , 2] , b =[1 , 0 , 1] , and v =[0 , 0 , 1] , then}
\put(296.9777,-527.0751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}z = V ecCombSel ( a , b ; v )=[1 , 0 , 1] .}
\put(306.9396,-546.0241){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{it}\selectfont\color{color_29791}Definition 3. Function V ecCombProb ( a , b , c ; p ) : F or three}
\put(296.9777,-557.9801){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}equal-length v ectors a , b , and c , and a probability distrib ution}
\put(296.9777,-569.9351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v ector p =[ p}
\put(347.2827,-571.4291){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(351.7527,-569.9351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(361.1927,-571.4291){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(365.6617,-569.9351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(375.1017,-571.4291){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3}
\put(379.5717,-569.9351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}] with p}
\put(414.0457,-571.4291){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(417.3626,-569.9351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}∈ [0 , 1] and}
\put(467.2966,-562.4631){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}P}
\put(477.8126,-564.9261){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3}
\put(477.8126,-572.9241){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i =1}
\put(492.8776,-569.9351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}p}
\put(497.8896,-571.4291){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(501.2067,-569.9351){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=1 , calling}
\put(296.9777,-581.8901){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the function V ecCombProb ( a , b , c ; p ) will combine the ele-}
\put(296.9777,-593.8451){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ments from the v ectors a , b , and c according to the probability}
\put(296.9777,-605.8001){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}distrib ution e xpressed as p . Let z denote the result v ector , such}
\put(296.9777,-617.7561){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}that z = V ecCombProb ( a , b , c ; p ) . The i -th element z}
\put(522.3537,-619.2501){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(530.4917,-617.7561){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of z}
\put(296.9777,-629.7111){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is then determined using Eq. ( 17 ), where r and is a random}
\put(296.9777,-641.6661){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}number .}
\put(355.2776,-665.7751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}z}
\put(359.9106,-667.2701){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(363.2276,-665.7751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(372.6366,-642.3631){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(372.6366,-651.3291){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(372.6366,-654.3181){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(372.6366,-672.2511){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(372.6366,-675.2401){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(384.9786,-651.8081){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}a}
\put(390.2456,-653.3021){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(393.5616,-651.8081){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, if 0 ≤ r and ≤ p}
\put(463.0596,-653.3021){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(467.5296,-651.8081){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791},}
\put(384.9786,-666.1541){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}b}
\put(389.2546,-667.6481){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(392.5716,-666.1541){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, if p}
\put(421.3776,-667.6481){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(425.8476,-666.1541){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}<r and ≤ p}
\put(467.5606,-667.6481){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(472.0296,-666.1541){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+ p}
\put(484.7906,-667.6481){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(489.2606,-666.1541){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791},}
\put(384.9796,-680.5001){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}c}
\put(389.2906,-681.9941){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(392.6075,-680.5001){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, if p}
\put(421.3775,-681.9941){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(425.8475,-680.5001){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+ p}
\put(438.6085,-681.9941){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(443.0775,-680.5001){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}<r and ≤ 1 .}
\put(531.4385,-665.7751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(17)}
\put(306.9405,-700.0181){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}F or e xample, assume a =[1 , 0 , 2] , b =[1 , 0 , 1] ,}
\put(296.9775,-711.9731){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}c =[1 , 0 , 0] , and p =[0 . 25 , 0 . 35 , 0 . 4] , calling the function}
\put(296.9775,-723.9281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}V ecCombProb ( a , b , c ; p ) will generate a v ector z . Let’ s}
\put(296.9775,-735.8831){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}assume the three randomly generated numbers during}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE}
\put(40.388,-194.41){\includegraphics[width=501.228pt,height=148.3396pt]{latexImage_e950299cc83c85b488b572365db0e72c.png}}
\put(33.964,-208.756){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig. 4. Illustration of three-party G-L-I crosso v er operation. It contains 4 steps illustrated in the four}
\put(33.964,-220.711){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}parts ① , ② , ③ , and ④ , where part ① sho ws the process of x}
\put(361.073,-217.096){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(361.073,-223.533){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(373.462,-220.711){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}⊖ x}
\put(386.192,-215.924){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(386.192,-223.717){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(408.144,-220.7111){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and x}
\put(437.075,-217.0961){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(437.075,-223.5331){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(449.464,-220.7111){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}⊖ x}
\put(462.1939,-217.0961){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(468.8559,-220.7111){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, part ② sho ws}
\put(33.9639,-233.2701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the process of V ecCombProb ( v}
\put(197.2759,-229.6541){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(197.2759,-236.0911){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(209.6649,-233.2701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(219.0739,-229.6541){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(219.0739,-236.0911){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(231.4629,-233.2701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}⊖ x}
\put(244.1929,-228.4821){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(244.1929,-236.2751){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(256.5819,-233.2701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(265.9909,-229.6541){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(265.9909,-236.0911){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(278.3799,-233.2701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}⊖ x}
\put(291.1099,-229.6541){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(297.7729,-233.2701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}; [ p}
\put(309.9799,-229.6541){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(309.9799,-236.0911){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(322.3699,-233.2701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(331.8099,-228.4821){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(331.8099,-236.2751){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(344.1989,-233.2701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(353.6389,-229.6541){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(360.3019,-233.2701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}]) , part ③ sho ws the process of}
\put(33.9639,-245.8281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}V ecCombProb ( x}
\put(106.7989,-242.2131){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(106.7989,-248.6501){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(119.1879,-245.8281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(128.5969,-241.04){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(128.5969,-248.8331){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(140.9859,-245.8281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(150.3949,-242.2131){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(157.0579,-245.8281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}; [ p}
\put(169.2659,-242.2131){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(169.2659,-248.6501){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(181.6549,-245.8281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(191.0949,-241.04){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(191.0949,-248.8331){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(203.4839,-245.8281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(212.9239,-242.2131){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(219.5869,-245.8281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}]) , part ④ sho ws the process of V ecCombSel ( x}
\put(417.6689,-242.2131){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(417.6689,-248.6501){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(430.0579,-245.8281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(439.4669,-241.04){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t,tmp}
\put(439.4669,-248.8331){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(459.5269,-245.8281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}; v}
\put(468.9359,-241.566){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t +1}
\put(468.9359,-248.8331){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(482.5309,-245.8281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) .}
\put(33.96387,-279.9001){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}this process are 0 . 2 , 0 . 5 , and 0 . 9 , respecti v ely , then}
\put(33.96388,-291.855){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}z =[ z}
\put(53.53689,-293.35){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(58.00589,-291.855){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, z}
\put(67.06689,-293.35){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(71.53589,-291.855){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, z}
\put(80.59689,-293.35){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3}
\put(85.06689,-291.855){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}]=[ a}
\put(103.6159,-293.35){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\put(108.0859,-291.855){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, b}
\put(116.7889,-293.35){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(121.2579,-291.855){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, c}
\put(129.9979,-293.35){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3}
\put(134.4669,-291.855){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}]=[1 , 0 , 0] .}
\put(43.92685,-314.07){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W ith the pre ceding definitions, we no w detail the up-}
\put(33.96385,-326.025){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}date rules for indi vidual v elocity and position. Recall that}
\put(33.96385,-337.98){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(42.81985,-334.365){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(42.81985,-340.802){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(55.20885,-337.98){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(64.61785,-334.3651){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(64.61785,-340.8021){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(77.00685,-337.9801){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) represents the solution of the ind -th indi vidual}
\put(33.96387,-350.3551){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}in generation t . Let ( x}
\put(137.4239,-345.5671){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(137.4239,-353.3601){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(149.8129,-350.3551){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(159.2219,-345.5671){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(159.2219,-353.3601){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(171.6109,-350.3551){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) represent the local best}
\put(33.96387,-362.3101){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}solution of the ind -th indi vidual in the t -th generation, and}
\put(33.96388,-374.265){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}let ( x}
\put(55.89088,-370.6501){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(62.55389,-374.265){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(71.96289,-370.6501){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(78.62489,-374.265){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) represent the global best solution. In addition, let}
\put(33.96389,-386.22){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}v}
\put(38.94489,-382.605){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(38.94489,-389.0421){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(51.33389,-386.2201){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=[ v}
\put(67.03689,-382.6051){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(66.67989,-389.0421){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind, 1}
\put(85.40589,-386.2201){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, v}
\put(95.01989,-382.6051){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(94.66289,-389.0421){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind, 2}
\put(113.3889,-386.2201){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, . . . , v}
\put(140.7149,-382.6051){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(140.3579,-389.0421){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind,V}
\put(161.5329,-386.2202){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}] be the v elocity of the ind -th}
\put(33.96385,-399.1541){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi vidual in generation t . Then, v}
\put(175.4899,-394.8922){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t +1}
\put(175.4899,-402.1591){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(192.5919,-399.1541){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is updated in GA-PSO}
\put(33.96385,-411.1091){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}using Eq. ( 18 ).}
\put(49.98185,-429.5441){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}v}
\put(54.96285,-425.2821){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t +1}
\put(54.96285,-432.5491){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(68.55785,-429.5441){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= V ecCombProb ( v}
\put(149.1418,-425.4301){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(149.1418,-432.0071){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(161.5309,-429.5441){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(170.9398,-425.4301){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(170.9398,-432.0071){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(183.3289,-429.5441){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}⊖ x}
\put(196.0598,-424.7561){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(196.0598,-432.5491){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(208.4489,-429.5441){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(217.8578,-425.4301){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(217.8578,-432.007){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(230.2469,-429.544){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}⊖ x}
\put(242.9769,-425.43){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(249.6389,-429.544){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791};}
\put(184.8969,-445.672){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[ p}
\put(192.6758,-441.559){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(192.6758,-448.135){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(205.0648,-445.672){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(214.5058,-440.885){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(214.5058,-448.678){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(226.8948,-445.672){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(236.3348,-441.559){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(242.9978,-445.672){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}]) .}
\put(268.4248,-437.115){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(18)}
\put(43.92683,-467.184){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The x-chromosome and the y-chromosome of the ind -}
\put(33.96381,-479.139){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}th indi vidual’ s of fspring are generate d using Eq. ( 19 ) and}
\put(33.96381,-491.094){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Eq. ( 20 ), respecti v el y , where x}
\put(155.3688,-486.307){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t,tmp}
\put(155.3688,-494.1){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(177.8918,-491.094){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and y}
\put(199.7228,-486.307){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t,tmp}
\put(199.7228,-494.1){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(222.2458,-491.094){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}denote interme-}
\put(33.96379,-503.049){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}diate v ectors deri v ed from Eq. ( 21 ) and Eq. ( 22 ), respecti v ely .}
\put(77.4838,-521.7289){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x}
\put(82.4648,-517.468){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t +1}
\put(82.4648,-524.735){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(96.0598,-521.7289){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= V ecCombSel ( x}
\put(169.9988,-517.616){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(169.9988,-524.1919){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(182.3878,-521.7289){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(191.7968,-516.9419){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t,tmp}
\put(191.7968,-524.7349){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(211.8568,-521.7289){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}; v}
\put(221.2658,-517.4679){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t +1}
\put(221.2658,-524.7349){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(234.8608,-521.7289){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) , (19)}
\put(77.48378,-540.5359){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y}
\put(82.46478,-536.2749){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t +1}
\put(82.46478,-543.5419){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(96.05978,-540.5359){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= V ecCombSel ( y}
\put(169.9988,-536.4229){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(169.9988,-543.0569){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(182.3878,-540.5359){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(191.7968,-535.7489){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t,tmp}
\put(191.7968,-543.5419){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(211.8568,-540.5359){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}; v}
\put(221.2658,-536.2749){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t +1}
\put(221.2658,-543.5419){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(234.8608,-540.5359){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) , (20)}
\put(44.75177,-559.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x}
\put(49.73277,-555.0319){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t,tmp}
\put(49.73277,-562.825){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(69.79277,-559.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= V ecCombProb ( x}
\put(150.3768,-555.7059){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(150.3768,-562.2829){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(162.7658,-559.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(172.1748,-555.0319){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(172.1748,-562.825){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(184.5638,-559.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x}
\put(193.9738,-555.7059){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(200.6358,-559.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}; [ p}
\put(212.8438,-555.7059){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(212.8438,-562.2829){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(225.2328,-559.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(234.6728,-555.0319){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(234.6728,-562.825){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(247.0618,-559.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(256.5018,-555.7059){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(263.1648,-559.8199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}]) ,}
\put(268.4248,-571.7749){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(21)}
\put(45.5818,-583.7299){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y}
\put(50.5638,-578.9419){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t,tmp}
\put(50.5638,-586.7349){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(70.6228,-583.7299){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= V ecCombProb ( y}
\put(151.2068,-579.6169){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(151.2068,-586.2509){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(163.5958,-583.7299){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(173.0058,-578.9419){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(173.0058,-586.7349){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(185.3948,-583.7299){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(194.8038,-579.6169){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(201.4658,-583.7299){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}; [ p}
\put(213.6738,-579.6169){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(213.6738,-586.1929){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(226.0628,-583.7299){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(235.5028,-578.9419){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(235.5028,-586.7349){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(247.8918,-583.7299){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, p}
\put(257.3328,-579.6169){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(263.9948,-583.7299){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}]) .}
\put(268.4248,-595.6849){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(22)}
\put(43.92677,-611.9219){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Each three-party G-L-I crosso v er operation will generate}
\put(33.96377,-623.877){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}one of fspring. As m}
\put(125.1068,-625.3719){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3cross}
\put(152.5738,-623.877){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi viduals are selected in the}
\put(33.96376,-635.8329){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}selection step for the three-party G-L-I cross o v er operation,}
\put(33.96376,-647.7879){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}this results in m}
\put(101.3308,-649.2819){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3cross}
\put(126.2258,-647.7879){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of fspring.}
\put(43.92677,-664.0249){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig.}
\put(59.70753,-664.0249){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(65.29655,-664.0249){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}4}
\put(70.27785,-664.0249){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(75.87683,-664.0249){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}illustrates the three-party G-L-I crosso v er opera-}
\put(33.96376,-675.9799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}tion. In part ① , the operation ⊖ is first e x ecuted to obtain}
\put(33.96376,-687.9349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x}
\put(38.94476,-684.3199){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(38.94476,-690.7569){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(51.33376,-687.9349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}⊖ x}
\put(64.06376,-683.1479){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t, L}
\put(64.06376,-690.9399){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(76.45276,-687.9349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, and x}
\put(107.3678,-684.3199){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(107.3678,-690.7569){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(119.7568,-687.9349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}⊖ x}
\put(132.4868,-684.3199){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G}
\put(139.1488,-687.9349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, follo wed by the e x ecution of the}
\put(33.96376,-699.8899){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}function V ecCombProb in parts ② and ③ to obtain v}
\put(271.4278,-695.6279){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t +1}
\put(271.0698,-702.8959){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(33.96379,-711.9729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and x}
\put(58.00479,-707.1849){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t,tmp}
\put(58.00479,-714.9779){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(78.06479,-711.9729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}. Final ly , x}
\put(124.2478,-707.7109){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t +1}
\put(124.2478,-714.9779){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind}
\put(141.8028,-711.9729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is obtained by calling the function}
\put(33.9638,-723.9279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}V ecCombSel in part ④ . The y-chromosome is generated}
\put(33.96381,-735.8829){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}similarly .}
\put(306.9398,-279.8999){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}4)}
\put(315.2387,-279.8999){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(320.22,-279.8999){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Mutation Oper ation}
\put(306.9398,-292.7839){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The mutation operation enhances genetic di v ersity among}
\put(296.9778,-304.7389){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of fspring, mitig ating the risk of con v er gence to local op-}
\put(296.9778,-316.6949){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}tima. In this step, the 2 · m}
\put(402.3208,-318.1889){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(423.7298,-316.6949){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+ m}
\put(440.2258,-318.1889){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3cross}
\put(464.6808,-316.6949){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of fspring indi viduals}
\put(296.9778,-328.6499){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}generated by the crosso v er operations in the pre vious step}
\put(296.9778,-340.6049){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}under go random mutation. of fspring generated by the pre vious}
\put(296.9778,-352.5598){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v er operations under go stochastic mutation. Both the}
\put(296.9778,-364.5148){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}x-chromosome and y-chromosome are subject to mutation,}
\put(296.9778,-376.4698){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}controlled by a parameter p}
\put(410.5518,-377.9648){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m utation}
\put(442.6058,-376.4698){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}.}
\put(306.9398,-389.3538){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}F or each of fspring indi vidual, tw o random v alues r}
\put(524.6308,-390.8488){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(533.6498,-389.3538){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and}
\put(296.9778,-401.3088){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}r}
\put(301.4718,-402.8038){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3}
\put(309.4058,-401.3088){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}are independently sampled from[0,1]. If r}
\put(480.6227,-402.8038){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(485.0917,-401.3088){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}≤ p}
\put(497.8527,-402.8038){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m utation}
\put(529.9067,-401.3088){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, the}
\put(296.9777,-413.2648){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}indi vidual’ s x-chromosome is mutated; if r}
\put(472.0037,-414.7588){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3}
\put(476.4727,-413.2648){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}≤ p}
\put(489.2337,-414.7588){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m utation}
\put(521.2877,-413.2648){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, its y-}
\put(296.9777,-425.2198){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}chromosome is mutated; otherwise, both chromosomes remain}
\put(296.9777,-437.1748){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}unchanged. Du r ing the mutati on of the x-chromosome and y-}
\put(296.9777,-449.1298){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}chromosome, tw o positions}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(413.375pt, -443.446pt) -- (418.146pt, -443.446pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(413.375,-449.13){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}r}
\put(418.147,-450.624){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(426.487,-449.13){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(444.744pt, -443.446pt) -- (449.515pt, -443.446pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(444.744,-449.13){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}r}
\put(449.515,-450.624){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3}
\put(457.855,-449.13){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}are random ly selected}
\put(296.978,-461.085){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}from the set V as the mutation posi tion. Then, the}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(527.727pt, -455.401pt) -- (532.498pt, -455.401pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(527.727,-461.085){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}r}
\put(532.498,-462.58){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(536.968,-461.085){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}-th}
\put(296.978,-473.04){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}element of the x-chromosome will change to a v alid random}
\put(296.978,-484.995){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v alue in set \{ 0 , 1 , . . . , x}
\put(399.304,-486.49){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(414.504,-484.995){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}\} . The}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(447.45pt, -479.311pt) -- (452.221pt, -479.311pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(447.45,-484.995){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}r}
\put(452.221,-486.49){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3}
\put(456.691,-484.995){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}-th element of the y-}
\put(296.978,-496.951){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}chromosome}
\put(309.531,-560.038){\includegraphics[width=225.9474pt,height=44.70618pt]{latexImage_d2c743eafb88862aee27772a4c40347c.png}}
\put(296.978,-574.385){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig. 5. Illustration of mutation operation.}
\put(306.94,-601.336){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig.}
\put(322.7208,-601.336){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(328.5987,-601.336){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}5}
\put(333.58,-601.336){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(339.4579,-601.336){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}illustrates the mutation o pe ration, where x}
\put(525.338,-597.721){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(525.338,-604.158){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1 , 1}
\put(296.978,-613.292){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}changes from 1 to 2 , indicating that the number of serv er}
\put(296.978,-625.247){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}units deplo yed at base station 1 changes from 1 to 2 . y}
\put(525.6961,-621.631){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(525.3391,-628.068){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1 , 8}
\put(296.9781,-637.202){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}changes from 5 to 1 , indicating that base station 8 is reassigned}
\put(296.9781,-649.157){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}from edge serv er 5 to edge serv er 1 . In addition, x}
\put(507.3831,-645.542){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(507.3831,-651.979){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2 , 5}
\put(533.6501,-649.157){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and}
\put(296.9781,-661.239){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}y}
\put(302.2202,-657.624){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(301.8622,-664.061){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 2 , 7}
\put(328.0471,-661.239){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}are also mutated.}
\put(306.9402,-675.179){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}5)}
\put(315.239,-675.179){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(320.2203,-675.179){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Chr omosome Corr ection}
\put(306.9402,-688.062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The y-chromosome of an indi vidual depends on its x-}
\put(296.9781,-700.017){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}chromosome. Ho we v er , in the crosso v er and mutation oper -}
\put(296.9781,-711.973){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ations in the pre vious steps , the tw o chromosomes change}
\put(296.9781,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}independently , thus possibly resulting in violations of the}
\put(296.9781,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}constraints in Eq. ( 11 ).}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE 9}
\put(43.927,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}F or an indi vidual with x-chromosome x and y-chromosome}
\put(33.964,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y , let S}
\put(71.016,-67.888){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(86.236,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) denote the set of positions in the y-}
\put(33.964,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}chromosome that in v olv e violations. S}
\put(197.091,-79.84302){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(217.248,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}can then be e x-}
\put(33.964,-90.30402){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}pressed as in Eq. ( 23 ).}
\put(42.454,-110.54){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}S}
\put(48.487,-112.0341){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(63.707,-110.54){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ):= S}
\put(102.395,-112.0341){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio1}
\put(121.102,-110.54){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}∪S}
\put(133.777,-112.0341){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio2}
\put(152.483,-110.54){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}∪S}
\put(165.158,-112.0341){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio3}
\put(85.84601,-125.4841){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}:= \{ i | y}
\put(112.428,-126.9781){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(115.744,-125.4841){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= k > 0 , x}
\put(151.845,-126.9781){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}k}
\put(156.747,-125.4841){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}> 0 , y}
\put(178.789,-126.9781){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}k}
\put(183.691,-125.4841){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}̸ = k , i ∈V \}}
\put(96.36195,-140.4281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}∪\{ i | y}
\put(119.069,-141.9221){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(122.386,-140.4281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= k > 0 , x}
\put(158.4869,-141.9221){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}k}
\put(163.3889,-140.4281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}> 0 , y}
\put(185.4309,-141.9221){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}k}
\put(190.3329,-140.4281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= k , w}
\put(215.1419,-141.9221){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}k}
\put(220.0439,-140.4281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}>w}
\put(234.9249,-141.9221){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(250.1249,-140.4281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, i ∈V \}}
\put(96.36189,-155.3721){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}∪\{ i | y}
\put(119.0689,-156.8661){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(122.3859,-155.3721){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= k > 0 , x}
\put(158.4869,-156.8661){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}k}
\put(163.3889,-155.3721){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=0 , i ∈V \} . (23)}
\put(43.92686,-175.6071){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The violations in S}
\put(124.0249,-177.1021){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(139.2449,-175.6071){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) can be resolv ed by assigning}
\put(33.96387,-187.5621){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v alid v alues to the corresponding y-chromosome elements.}
\put(33.96387,-199.5181){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig.}
\put(49.74463,-199.5181){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(52.32494,-199.5181){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}6}
\put(57.30624,-199.5181){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(59.87659,-199.5181){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}sho ws an e xample with chromosome pair ( x}
\put(236.5278,-195.902){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(236.5278,-202.3391){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1}
\put(252.8878,-199.5181){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, y}
\put(262.2968,-195.902){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(262.2968,-202.3391){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1}
\put(278.6568,-199.5181){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) ,}
\put(33.96384,-211.4731){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}where y}
\put(66.19583,-207.8571){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(65.83883,-214.2941){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1 , 3}
\put(88.53583,-211.4731){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=4 whereas x}
\put(144.9008,-207.8571){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(144.9008,-214.2941){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1 , 3}
\put(167.5988,-211.4731){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=0}
\put(245.2949,-211.4731){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(245.285,-211.4731){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}23}
\put(255.2476,-211.4731){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(255.2575,-211.4731){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) yields}
\put(33.96382,-223.4281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}S}
\put(39.99682,-224.9221){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(55.21682,-223.4281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(}
\put(177.269,-223.4281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(180.8555,-223.4281){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}6 , this violation is solv ed}
\put(33.96384,-235.3831){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}by setting y}
\put(81.98783,-231.7681){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(81.62983,-238.2051){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1 , 3}
\put(104.3278,-235.3831){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=1 , where the corresponding x}
\put(231.2488,-231.7681){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(231.2488,-238.2051){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ind 1 , 1}
\put(253.9468,-235.3831){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=2}
\put(46.518,-340.389){\includegraphics[width=225.9589pt,height=84.57678pt]{latexImage_d57633f048666343a23158784394bd74.png}}
\put(33.964,-354.735){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig. 6. Illustration of chromosome correction operation.}
\put(43.927,-379.705){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}A k e y operation to resolv e a violation is to select a proper}
\put(33.964,-391.66){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}SEA pair of (base station, edge serv er) from the set of v alid}
\put(33.964,-403.615){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}SEA pairs prioritized using their weights. The weight ξ}
\put(265.735,-405.11){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}ij}
\put(276.724,-403.615){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}of}
\put(33.96404,-415.571){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}the SEA pair of (base station i , edge serv er j ) is calculated}
\put(33.96405,-427.5259){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}using Eq. ( 24 ) where µ is a coef ficient in [0,1]. Here, when}
\put(33.96405,-439.4809){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}edge serv er j ’ s o}
\put(103.585,-440.9749){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}j}
\put(111.274,-439.4809){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}e xceeds a constant threshold th , it implies}
\put(33.96405,-451.4359){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}that the b udget is suf ficient to add a serv er unit to the edge}
\put(33.96405,-463.3909){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}serv er j . Then, the SEA pair of (base station i , edge serv er j )}
\put(33.96405,-475.3469){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}with the smallest weight ξ}
\put(142.218,-476.8409){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}ij}
\put(152.792,-475.3469){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}is selected. In other w ords, base}
\put(33.96404,-487.3019){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}station i is assigned to the edge serv er j . Consequently , y is}
\put(33.96405,-499.2569){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}updated with y}
\put(94.50205,-500.7509){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}i}
\put(97.81905,-499.2569){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}= j . W e call this k e y operation of selecting an}
\put(33.96405,-511.2119){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}SEA pair as SEA P air Selection (SEA-PS) operation.}
\put(58.79105,-532.9608){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}ξ}
\put(63.15005,-534.4558){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}ij}
\put(70.16605,-532.9608){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}= µ ·}
\put(91.97205,-526.2208){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}t}
\put(95.57005,-527.7158){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}ij}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_274846,line width=0.398pt]
(87.881pt, -530.471pt) -- (106.679pt, -530.471pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(87.881,-539.795){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}t}
\put(91.478,-541.289){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}max}
\put(107.874,-532.961){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}+(1 − µ ) ·}
\put(151.568,-526.221){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}w}
\put(158.701,-527.716){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}j}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_274846,line width=0.398pt]
(146.067pt, -530.471pt) -- (168.399pt, -530.471pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(146.067,-539.795){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}w}
\put(153.2,-541.289){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}max}
\put(169.595,-532.961){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}, ∀ i ∈V , ∀ j ∈S ( x ) . (24)}
\put(43.92699,-564.535){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}In GA-PSO, violations in an indi vidual’ s S}
\put(230.426,-566.03){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(245.646,-564.535){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) are}
\put(33.96397,-576.491){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}resolv ed via the chromosome correction algorithm described}
\put(33.96397,-588.446){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}in}
\put(62.01865,-588.446){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(64.89784,-588.446){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}2 , whose process can be outlined as the follo wing four}
\put(33.96397,-600.401){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}steps.}
\put(33.96397,-616.331){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Step}
\put(62.91528,-616.331){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(67.89658,-616.331){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The algorithm calculates sets S}
\put(198.853,-612.716){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}e xpand}
\put(223.316,-616.331){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and S}
\put(247.663,-617.826){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(262.883,-616.331){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y )}
\put(67.89595,-628.286){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}based on the current solution represented by chromo-}
\put(67.89595,-640.242){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}some pair ( x , y ) . According to Eq. ( 11 ), if the set}
\put(67.89594,-652.197){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}S}
\put(73.92893,-653.691){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(89.14893,-652.197){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) contains a base station i in which some}
\put(67.89595,-664.152){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}edge serv ers ha v e been de p l o yed, then y is set to y}
\put(268.034,-665.646){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(271.351,-664.152){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}= i .}
\put(33.96396,-676.107){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Step}
\put(62.91527,-676.107){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(67.89657,-676.107){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The algorithm computes the total cost using Eq. ( 9 ).}
\put(67.89596,-688.062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}If the computed cost surpasses the b udget B , the}
\put(67.89595,-700.017){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}procedure adv ances t o Step 3; otherwise, it proceeds}
\put(67.89595,-711.973){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}to Step 4.}
\put(33.96395,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Step}
\put(62.91527,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(67.89657,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The algorithm randomly deletes an edge serv er from}
\put(67.89595,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}set S}
\put(90.87595,-732.268){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}e xpand}
\put(111.4099,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, updates x and y accordingly , and then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.797pt]
(296.978pt, -45.87pt) -- (548.037pt, -45.87pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(301.959,-55.12598){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Algorithm 2: Chromosome correction}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.797pt]
(296.978pt, -59.539pt) -- (548.037pt, -59.539pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(306.94,-70.388){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Input: ESD configuration before e xpansion x}
\put(494.127,-66.77301){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(505.252,-70.388){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, ESD}
\put(337.5549,-82.34399){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}configuration x and SEA configuration y , and}
\put(337.5549,-94.29901){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}other parameters;}
\put(306.9399,-106.254){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}Output:}
\put(341.2611,-106.254){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}}
\put(344.748,-106.254){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x , y}
\put(298.4719,-118.209){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}1 Calculate S}
\put(354.8359,-114.5941){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}expand}
\put(380.2329,-118.209){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, S ( x ) , S}
\put(417.7319,-119.704){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(432.9519,-118.209){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) , C}
\put(468.1888,-119.704){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(485.1118,-118.209){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(493.9668,-114.5941){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(505.0918,-118.209){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x ) ;}
\put(298.4718,-130.1641){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}2 f or i ∈S}
\put(339.0068,-131.6591){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(354.2268,-130.1641){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) do}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(312.121pt, -162.045pt) -- (312.121pt, -133.751pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(298.87,-142.12){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}3 if x [ i ] > 0 then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(327.463pt, -158.06pt) -- (327.463pt, -145.706pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(299.269,-154.075){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}4 y [ i ]= i}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(327.662pt, -157.86pt) -- (332.643pt, -157.86pt)
;
\draw[color_29791,line width=0.398pt]
(312.32pt, -161.846pt) -- (317.301pt, -161.846pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(298.472,-174.09){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}5 if C}
\put(323.635,-175.584){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(340.557,-174.09){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(349.413,-170.474){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(360.538,-174.09){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x ) >B then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(312.121pt, -460.142pt) -- (312.121pt, -177.677pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(298.87,-186.135){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}6 while C}
\put(355.585,-187.629){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(372.507,-186.135){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(381.363,-182.519){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(392.488,-186.135){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x ) >B do}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(327.463pt, -456.157pt) -- (327.463pt, -189.722pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(299.269,-198.179){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}7 randomly pick k ∈S}
\put(418.863,-194.564){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}expand}
\put(444.261,-198.179){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791};}
\put(299.269,-210.224){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}8 S}
\put(344.405,-206.609){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}expand}
\put(369.803,-210.224){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}←S}
\put(386.546,-206.609){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}expand}
\put(411.944,-210.224){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}\\{ k \} , x [ k ]=0 ;}
\put(299.269,-222.179){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}9 f or i ← 1 to V do}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(342.806pt, -254.06pt) -- (342.806pt, -225.766pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.181,-234.135){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}10 if y [ i ] is k then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(358.148pt, -250.075pt) -- (358.148pt, -237.721pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.579,-246.09){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}11 S}
\put(374.343,-247.584){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(389.563,-246.09){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) ←S}
\put(427.698,-247.584){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(442.917,-246.09){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) ∪\{ i \} ;}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(358.347pt, -249.876pt) -- (363.328pt, -249.876pt)
;
\draw[color_29791,line width=0.398pt]
(343.005pt, -253.861pt) -- (347.986pt, -253.861pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(295.782,-266.015){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}12}
\put(337.625,-265.652){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(337.625,-266.015){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x = x ,}
\put(361.314,-265.652){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(361.314,-266.015){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y = y , Ξ ←∅ ;}
\put(295.7819,-277.97){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}13 f or i ∈S}
\put(369.6919,-279.465){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(384.9119,-277.97){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) do}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(342.806pt, -424.186pt) -- (342.806pt, -281.556pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.181,-289.925){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}14 f or j ∈S ( x ) do}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(358.148pt, -323.625pt) -- (358.148pt, -293.512pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.579,-301.881){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}15 if BS i and ES j meet constraints then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(373.491pt, -319.64pt) -- (373.491pt, -305.467pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.978,-314.81){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}16 ξ}
\put(388.011,-316.304){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ij}
\put(395.028,-314.81){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(407.241,-309.757){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(410.25,-310.753){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ij}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(403.972pt, -312.319pt) -- (419.899pt, -312.319pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(403.972,-318.245){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(406.982,-319.242){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(421.094,-314.81){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+}
\put(434.636,-309.757){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(440.399,-310.753){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(430.038pt, -312.319pt) -- (448.718pt, -312.319pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(430.038,-318.245){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(435.802,-319.242){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(449.914,-314.81){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, Ξ ← Ξ ∪\{ ξ}
\put(495.12,-316.304){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ij}
\put(502.136,-314.81){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}\} ;}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(373.69pt, -319.441pt) -- (378.671pt, -319.441pt)
;
\draw[color_29791,line width=0.398pt]
(358.347pt, -323.426pt) -- (363.328pt, -323.426pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.181,-335.58){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}17 if Ξ ̸ = ∅ then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(358.148pt, -392.306pt) -- (358.148pt, -339.167pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.579,-347.535){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}18 connect BS i to ES j with smallest ξ}
\put(521.585,-349.03){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ij}
\put(528.601,-347.535){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791};}
\put(296.579,-359.491){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}19 if}
\put(383.677,-354.437){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(389.441,-355.434){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(379.08pt, -357pt) -- (397.76pt, -357pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(379.08,-362.926){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(384.843,-363.922){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(398.956,-359.491){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}>th and}
\put(437.402,-359.128){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(437.402,-359.491){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x [ j ] < x}
\put(465.321,-360.985){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(484.008,-359.491){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and}
\put(373.291,-372.381){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}C}
\put(380.412,-373.875){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(397.335,-372.381){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(406.1899,-368.765){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(417.3149,-372.381){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791},}
\put(421.743,-372.018){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(421.743,-372.381){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x )+ η ≤ B then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(373.491pt, -388.321pt) -- (373.491pt, -375.967pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.978,-384.336){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}20}
\put(383.653,-383.973){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(383.653,-384.336){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x [ j ]=}
\put(406.59,-383.973){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(406.59,-384.336){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x [ j ]+1 ;}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(373.69pt, -388.122pt) -- (378.671pt, -388.122pt)
;
\draw[color_29791,line width=0.398pt]
(358.347pt, -392.107pt) -- (363.328pt, -392.107pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.181,-404.261){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}21 else}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(358.148pt, -420.201pt) -- (358.148pt, -407.847pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.579,-416.216){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}22}
\put(368.31,-415.853){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(368.31,-416.216){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y [ i ]=0 ;}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(358.347pt, -420.002pt) -- (363.328pt, -420.002pt)
;
\draw[color_29791,line width=0.398pt]
(343.005pt, -423.987pt) -- (347.986pt, -423.987pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(295.782,-436.231){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}23 if C}
\put(354.32,-437.726){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(371.242,-436.231){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(380.098,-432.616){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(391.223,-436.231){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791},}
\put(395.651,-435.868){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(395.651,-436.231){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x ) ≤ B then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(342.806pt, -452.172pt) -- (342.806pt, -439.818pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.181,-448.186){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}24 x =}
\put(365.698,-447.823){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(365.698,-448.186){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x , y =}
\put(389.3869,-447.823){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(389.3869,-448.186){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y , break;}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(343.005pt, -451.972pt) -- (347.986pt, -451.972pt)
;
\draw[color_29791,line width=0.398pt]
(327.662pt, -455.957pt) -- (332.643pt, -455.957pt)
;
\draw[color_29791,line width=0.398pt]
(312.32pt, -459.942pt) -- (317.301pt, -459.942pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(294.985,-472.097){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}25 else}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(312.121pt, -726.179pt) -- (312.121pt, -475.684pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(295.384,-484.142){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}26 while C}
\put(355.585,-485.636){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(372.507,-484.142){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(381.363,-480.526){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(392.488,-484.142){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x ) ≤ B do}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(327.463pt, -722.194pt) -- (327.463pt, -487.729pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(295.782,-496.097){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}27}
\put(337.625,-495.734){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(337.625,-496.097){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x = x ,}
\put(361.314,-495.734){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(361.314,-496.097){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y = y , Ξ ←∅ ;}
\put(295.7819,-508.052){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}28 f or i ∈S}
\put(369.6919,-509.546){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(384.9119,-508.052){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x , y ) do}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(342.806pt, -654.268pt) -- (342.806pt, -511.638pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.181,-520.007){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}29 f or j ∈S ( x ) do}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(358.148pt, -553.707pt) -- (358.148pt, -523.594pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.579,-531.962){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}30 if BS i and ES j meet constraints then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(373.491pt, -549.722pt) -- (373.491pt, -535.549pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.978,-544.892){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}31 ξ}
\put(388.011,-546.386){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ij}
\put(395.028,-544.892){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(407.241,-539.838){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(410.25,-540.835){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ij}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(403.972pt, -542.401pt) -- (419.899pt, -542.401pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(403.972,-548.327){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(406.982,-549.323){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(421.094,-544.892){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+}
\put(434.636,-539.838){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(440.399,-540.835){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(430.038pt, -542.401pt) -- (448.718pt, -542.401pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(430.038,-548.327){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(435.802,-549.323){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(449.914,-544.892){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, Ξ ← Ξ ∪\{ ξ}
\put(495.12,-546.386){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ij}
\put(502.136,-544.892){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}\} ;}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(373.69pt, -549.523pt) -- (378.671pt, -549.523pt)
;
\draw[color_29791,line width=0.398pt]
(358.347pt, -553.508pt) -- (363.328pt, -553.508pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.181,-565.662){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}32 if Ξ ̸ = ∅ then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(358.148pt, -622.388pt) -- (358.148pt, -569.249pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.579,-577.617){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}33 connect BS i to ES j with smallest ξ}
\put(521.585,-579.112){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ij}
\put(528.601,-577.617){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791};}
\put(296.579,-589.572){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}34 if}
\put(383.677,-584.519){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(389.441,-585.515){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}j}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(379.08pt, -587.082pt) -- (397.76pt, -587.082pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(379.08,-593.008){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(384.843,-594.004){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(398.956,-589.572){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}>th and}
\put(437.402,-589.209){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(437.402,-589.572){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x [ j ] < x}
\put(465.321,-591.067){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\put(484.008,-589.572){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and}
\put(373.291,-602.462){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}C}
\put(380.412,-603.957){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(397.335,-602.462){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(406.1899,-598.847){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(417.3149,-602.462){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791},}
\put(421.743,-602.099){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(421.743,-602.462){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x )+ η ≤ B then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(373.491pt, -618.403pt) -- (373.491pt, -606.049pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.978,-614.418){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}35}
\put(383.653,-614.055){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(383.653,-614.418){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x [ j ]=}
\put(406.59,-614.055){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(406.59,-614.418){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x [ j ]+1 ;}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(373.69pt, -618.204pt) -- (378.671pt, -618.204pt)
;
\draw[color_29791,line width=0.398pt]
(358.347pt, -622.189pt) -- (363.328pt, -622.189pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.181,-634.343){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}36 else}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(358.148pt, -650.283pt) -- (358.148pt, -637.929pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.579,-646.298){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}37}
\put(368.31,-645.935){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(368.31,-646.298){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y [ i ]=0 ;}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(358.347pt, -650.084pt) -- (363.328pt, -650.084pt)
;
\draw[color_29791,line width=0.398pt]
(343.005pt, -654.069pt) -- (347.986pt, -654.069pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(295.782,-666.223){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}38 x =}
\put(350.355,-665.86){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(350.355,-666.223){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x , y =}
\put(374.0439,-665.86){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(374.0439,-666.223){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y ;}
\put(295.7819,-678.178){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}39 randomly pick k ∈V \S ( x ) ;}
\put(295.7819,-690.223){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}40 S}
\put(344.4049,-686.608){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}expand}
\put(369.8029,-690.223){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}←S}
\put(386.5459,-686.608){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}expand}
\put(411.9439,-690.223){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}∪\{ k \} , x [ k ]=1 , y [ k ]= k ;}
\put(295.7819,-702.268){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}41 if C}
\put(354.3199,-703.763){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}total}
\put(371.2419,-702.268){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( x}
\put(380.0979,-698.653){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}old}
\put(391.2229,-702.268){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}, x ) >B then}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(342.806pt, -718.209pt) -- (342.806pt, -705.855pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.181,-714.223){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}42 x =}
\put(365.698,-713.86){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(365.698,-714.223){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}x , y =}
\put(389.3869,-713.86){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ˆ}
\put(389.3869,-714.223){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}y , break;}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(343.005pt, -718.009pt) -- (347.986pt, -718.009pt)
;
\draw[color_29791,line width=0.398pt]
(327.662pt, -721.994pt) -- (332.643pt, -721.994pt)
;
\draw[color_29791,line width=0.398pt]
(312.32pt, -725.979pt) -- (317.301pt, -725.979pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(294.985,-738.134){\fontsize{6.9738}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}43 r etur n x , y}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.797pt]
(296.978pt, -744.111pt) -- (548.037pt, -744.111pt)
;
\end{tikzpicture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE 10}
\put(67.89603,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}conducts an SEA-PS operation. This step is repeated}
\put(67.89603,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}until the total cost f alls belo w the b udget constraint.}
\put(33.96403,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Step}
\put(62.91534,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(67.89664,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The algorithm randomly selects a base station where}
\put(67.89603,-90.30402){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}no edge serv er has been deplo yed yet, deplo ys an edge}
\put(67.89603,-102.259){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv er there, updates x and y accordingly , and then}
\put(67.89602,-114.2141){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}performs an SEA-PS operation. This step is repeated}
\put(67.89602,-126.1691){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}until the total cost e xceeds the b udget constraint.}
\put(43.92702,-139.6671){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The 2 m}
\put(76.20302,-141.1611){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(97.61102,-139.6671){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+ m}
\put(114.107,-141.1611){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3cross}
\put(138.572,-139.6671){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of fspring indi viduals generated after}
\put(33.96402,-151.6221){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v er , mutation, and correction operations, along with the}
\put(33.96402,-163.5771){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}P indi viduals from generation t , compete with each other .}
\put(33.96402,-175.5321){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Among them, the best P indi viduals are selected to form}
\put(33.96402,-187.4871){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}generation t +1 . The ne w generation then under goes selection,}
\put(33.96401,-199.4431){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v er , mutation, and correction operations to produce the}
\put(33.96401,-211.3981){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ne xt generation. This iterati v e process continues until certain}
\put(33.96401,-223.3531){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}e xit conditions are met.}
\put(33.96401,-248.7631){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}C.}
\put(43.09972,-248.7631){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(48.08102,-248.7631){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Analyses of GA-PSO}
\put(43.92701,-263.7161){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Let T represent the maximum iteration number of GA-PSO.}
\put(33.96401,-275.6711){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Recall that V is the number of base stations, 2 m}
\put(244.736,-277.1661){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(270.636,-275.6711){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and}
\put(33.96399,-287.6271){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}m}
\put(42.71099,-289.1211){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3cross}
\put(67.23999,-287.6271){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}are the numbers of of fspring indi viduals generated by}
\put(33.96399,-299.5821){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the tw o-party normal crosso v er operation and the three-party}
\put(33.96399,-311.5371){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G-L-I crosso v er operation, respecti v ely . Then some analyses}
\put(33.96399,-323.4921){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of GA-PSO yield the follo wing tw o lemmas:}
\put(43.92699,-341.2851){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{it}\selectfont\color{color_29791}Lemma 1. The time comple xity of GA-PSO is}
\put(33.96399,-353.2401){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}O ( T · V}
\put(64.46499,-349.6251){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2}
\put(68.93499,-353.2401){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}· (2 m}
\put(89.30499,-354.735){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(110.714,-353.2401){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+ m}
\put(127.21,-354.735){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3cross}
\put(148.619,-353.2401){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791})) .}
\put(43.92696,-371.0331){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{it}\selectfont\color{color_29791}Proof : The proof is pro vided in Appendix Section II. ■}
\put(43.92696,-388.826){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{it}\selectfont\color{color_29791}Lemma 2. GA-PSO is con v er gent.}
\put(43.92696,-406.619){\fontsize{9.9626}{1}\usefont{T1}{cmr}{b}{it}\selectfont\color{color_29791}Proof : The proof is pro vided in Appendix Section III. ■}
\put(90.77196,-429.787){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}V}
\put(99.66856,-429.787){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(105.148,-429.787){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}P E R F O R M A N C E E V A L U A T I O N}
\put(43.92696,-444.7411){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}In this s ection, we perform simulations to e v aluate the}
\put(33.96395,-456.696){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}performance of the proposed GA-PSO al gorithm and compare}
\put(33.96395,-468.651){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}it with other representati v e algorithms.}
\put(33.96395,-494.061){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}A.}
\put(42.54176,-494.061){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(47.52306,-494.061){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Experiment Setup}
\put(43.92696,-509.014){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The e xperiment w as conducted by simulating edge serv er}
\put(33.96395,-520.97){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}placement in Shanghai. W e used the Shanghai T elecom base}
\put(33.96395,-532.925){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}station dataset [ 22 ] with real w orkload logs to construct}
\put(33.96395,-544.88){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}netw ork configurations that closely resemble real-w orld situa-}
\put(33.96395,-556.835){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}tions. The dataset contains the locations of 3233 base stations}
\put(33.96395,-568.79){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}in Shanghai city . In our e xperiments, the def ault edge netw ork}
\put(33.96395,-580.746){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is defined as an area from ( 31}
\put(163.569,-577.13){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}◦}
\put(168.149,-580.746){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}18}
\put(178.112,-577.13){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}′}
\put(180.907,-580.746){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3 . 3” N , 121}
\put(226.528,-577.13){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}◦}
\put(231.108,-580.746){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}24}
\put(241.07,-577.13){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}′}
\put(243.866,-580.746){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}5 . 9” E ) to}
\put(33.96394,-592.701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}( 31}
\put(47.24394,-589.085){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}◦}
\put(51.82394,-592.701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}09}
\put(61.78694,-589.085){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}′}
\put(64.58194,-592.701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}39 . 3” N , 121}
\put(115.1839,-589.085){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}◦}
\put(119.7639,-592.701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}36}
\put(129.7269,-589.085){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}′}
\put(132.5219,-592.701){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}5 . 9” E ). Here the points are in the}
\put(33.96394,-604.656){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}longitude-latitude coordinate system. By remo ving missed or}
\put(33.96394,-616.611){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}in v alid data, 1071 v alid base station entries in the re gion}
\put(33.96394,-628.566){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}were}
\put(184.9571,-628.566){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(188.8425,-628.566){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}II}
\put(195.4776,-628.566){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(199.363,-628.566){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is an e xample of the}
\put(33.96394,-640.5211){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}processed data.}
\put(43.92694,-652.337){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T o simulate w orkload v ariations, we introduce a parameter}
\put(33.96394,-664.2921){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}called w orkload scale ν}
\put(129.8819,-665.786){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}LoadScale}
\put(165.3609,-664.2921){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}. In the simulation, the w ork-}
\put(33.96394,-676.2471){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}load of a base station is determined by multiplying its actual}
\put(33.96394,-688.202){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}w orkload by ν}
\put(94.02694,-689.697){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}LoadScale}
\put(129.5049,-688.202){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}. W e randomly select V base stations}
\put(33.96393,-700.157){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}from the 1071 base stations in the area and use the Flo yd}
\put(33.96393,-712.113){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}algorithm [ 34 ] to generate the topology for simulation.}
\put(43.92693,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}In the access delay model in Eq. ( 1 ), the transmission}
\put(33.96393,-735.8831){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}medium is opt ical fiber , α is fix ed at 5 µ s [ 35 ], and the}
\put(401.8589,-49.23804){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T ABLE II}
\put(325.2779,-61.19305){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}I N F O R M A T I O N O F A P A R T O F B A S E S T A T I O N S .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.312828pt]
(309.531pt, -70.91321pt) -- (535.4832pt, -70.91321pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(315.753,-78.30554){\fontsize{7.830604}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}ID User number Latitude Lon gitude W orkl oad(min)}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.312828pt]
(309.531pt, -81.56354pt) -- (535.4832pt, -81.56354pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(318.1448,-88.95508){\fontsize{7.830604}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}7 90 31.271472 121.494721 4642}
\put(314.2297,-99.29175){\fontsize{7.830604}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}151 226 31.231700 121.546506 10689}
\put(314.2297,-109.6285){\fontsize{7.830604}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}237 513 31.295347 121.515155 30512}
\put(314.2297,-119.9651){\fontsize{7.830604}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}653 1544 31.19382 121.49524 60019}
\put(314.2297,-130.301){\fontsize{7.830604}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}720 726 31.234259 121.52572 28849}
\put(314.2297,-140.6378){\fontsize{7.830604}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}879 304 31.24708 121.419525 11946}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.312828pt]
(309.531pt, -143.8949pt) -- (535.4832pt, -143.8949pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(400.201,-161.183){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T ABLE III}
\put(363.693,-173.138){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}S I M U L A T I O N P A R A M E T E R S .}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(325.221pt, -182.8744pt) -- (519.7912pt, -182.8744pt)
;
\draw[color_29791,line width=0.343761pt]
(325.3937pt, -194.4051pt) -- (325.3937pt, -183.0463pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(330.7289,-190.9977){\fontsize{8.604897}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}P arameters}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(378.503pt, -194.4051pt) -- (378.503pt, -183.0463pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(394.5855,-190.9977){\fontsize{8.604897}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}V alue}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(431.3886pt, -194.4051pt) -- (431.3886pt, -183.0463pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(436.7238,-190.9977){\fontsize{8.604897}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}P arameters}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(484.4979pt, -194.4051pt) -- (484.4979pt, -183.0463pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(491.6987,-190.9977){\fontsize{8.604897}{1}\usefont{T1}{cmr}{b}{n}\selectfont\color{color_29791}V alue}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(519.6193pt, -194.4051pt) -- (519.6193pt, -183.0463pt)
;
\draw[color_29791,line width=0.343761pt]
(325.221pt, -194.577pt) -- (519.7912pt, -194.577pt)
;
\draw[color_29791,line width=0.343761pt]
(325.3937pt, -206.1076pt) -- (325.3937pt, -194.7488pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(348.4819,-202.7003){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}V}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(378.503pt, -206.1076pt) -- (378.503pt, -194.7488pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(398.4921,-202.7003){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}450}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(431.3886pt, -206.1076pt) -- (431.3886pt, -194.7488pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(454.4637,-202.7003){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}B}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(484.4979pt, -206.1076pt) -- (484.4979pt, -194.7488pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(490.328,-202.7003){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3 × 10}
\put(509.9284,-199.5779){\fontsize{6.023411}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}5}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(519.6193pt, -206.1076pt) -- (519.6193pt, -194.7488pt)
;
\draw[color_29791,line width=0.343761pt]
(325.221pt, -206.2804pt) -- (519.7912pt, -206.2804pt)
;
\draw[color_29791,line width=0.343761pt]
(325.3937pt, -217.811pt) -- (325.3937pt, -206.4523pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(349.1797,-214.4028){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}α}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(378.503pt, -217.811pt) -- (378.503pt, -206.4523pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(397.0229,-214.4028){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}5 µ s}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(431.3886pt, -217.811pt) -- (431.3886pt, -206.4523pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(455.2825,-214.4028){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}β}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(484.4979pt, -217.811pt) -- (484.4979pt, -206.4523pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(491.9838,-214.4028){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}20 µ s}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(519.6193pt, -217.811pt) -- (519.6193pt, -206.4523pt)
;
\draw[color_29791,line width=0.343761pt]
(325.221pt, -217.9829pt) -- (519.7912pt, -217.9829pt)
;
\draw[color_29791,line width=0.343761pt]
(325.3937pt, -229.5135pt) -- (325.3937pt, -218.1548pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(349.6574,-226.1063){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}η}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(378.503pt, -229.5135pt) -- (378.503pt, -218.1548pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(387.7181,-226.1063){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1 . 25 × 10}
\put(418.3136,-222.983){\fontsize{6.023411}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}4}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(431.3886pt, -229.5135pt) -- (431.3886pt, -218.1548pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(440.4965,-226.1063){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}ν}
\put(444.7469,-227.3967){\fontsize{6.023411}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}LoadScale}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(484.4979pt, -229.5135pt) -- (484.4979pt, -218.1548pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(496.6807,-226.1063){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1.0}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(519.6193pt, -229.5135pt) -- (519.6193pt, -218.1548pt)
;
\draw[color_29791,line width=0.343761pt]
(325.221pt, -229.6855pt) -- (519.7912pt, -229.6855pt)
;
\draw[color_29791,line width=0.343761pt]
(325.3937pt, -241.2161pt) -- (325.3937pt, -229.8574pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(343.8298,-237.8087){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(346.9375,-239.0992){\fontsize{6.023411}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(378.503pt, -241.2161pt) -- (378.503pt, -229.8574pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(392.7198,-237.8087){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}100 µ s}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(431.3886pt, -241.2161pt) -- (431.3886pt, -229.8574pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(455.8682,-237.8087){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}δ}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(484.4979pt, -241.2161pt) -- (484.4979pt, -229.8574pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(489.8331,-237.8087){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}100 µ s}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(519.6193pt, -241.2161pt) -- (519.6193pt, -229.8574pt)
;
\draw[color_29791,line width=0.343761pt]
(325.221pt, -241.388pt) -- (519.7912pt, -241.388pt)
;
\draw[color_29791,line width=0.343761pt]
(325.3937pt, -252.9186pt) -- (325.3937pt, -241.5599pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(342.3036,-249.5113){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}w}
\put(348.4637,-250.8017){\fontsize{6.023411}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(378.503pt, -252.9186pt) -- (378.503pt, -241.5599pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(383.8382,-249.5113){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}100000 min}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(431.3886pt, -252.9186pt) -- (431.3886pt, -241.5599pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(453.911,-249.5113){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}th}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(484.4979pt, -252.9186pt) -- (484.4979pt, -241.5599pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(496.6807,-249.5113){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.8}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(519.6193pt, -252.9186pt) -- (519.6193pt, -241.5599pt)
;
\draw[color_29791,line width=0.343761pt]
(325.221pt, -253.0914pt) -- (519.7912pt, -253.0914pt)
;
\draw[color_29791,line width=0.343761pt]
(325.3937pt, -264.6221pt) -- (325.3937pt, -253.2633pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(340.5382,-261.2139){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}p}
\put(344.8672,-262.5051){\fontsize{6.023411}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(378.503pt, -264.6221pt) -- (378.503pt, -253.2633pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(399.5674,-261.2139){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.5}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(431.3886pt, -264.6221pt) -- (431.3886pt, -253.2633pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(448.9196,-261.2139){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}x}
\put(453.8376,-262.5051){\fontsize{6.023411}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(484.4979pt, -264.6221pt) -- (484.4979pt, -253.2633pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(499.9075,-261.2139){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(519.6193pt, -264.6221pt) -- (519.6193pt, -253.2633pt)
;
\draw[color_29791,line width=0.343761pt]
(325.221pt, -264.7939pt) -- (519.7912pt, -264.7939pt)
;
\draw[color_29791,line width=0.343761pt]
(325.3937pt, -276.3246pt) -- (325.3937pt, -264.9658pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(348.5881,-272.9172){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}P}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(378.503pt, -276.3246pt) -- (378.503pt, -264.9658pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(400.6436,-272.9172){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}40}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(431.3886pt, -276.3246pt) -- (431.3886pt, -264.9658pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(454.5061,-272.9172){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}T}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(484.4979pt, -276.3246pt) -- (484.4979pt, -264.9658pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(495.6044,-272.9172){\fontsize{8.604897}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}500}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.343761pt]
(519.6193pt, -276.3246pt) -- (519.6193pt, -264.9658pt)
;
\draw[color_29791,line width=0.343761pt]
(325.221pt, -276.4965pt) -- (519.7912pt, -276.4965pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(296.978,-307.369){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}processing delay per hop through t h e router is fix ed at 20}
\put(296.978,-319.324){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}µ s [ 35 ], [ 36 ]. W e choose a Dell Po werEdge R250 with a}
\put(296.978,-331.28){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}price of 1 . 25 × 10}
\put(367.6,-327.664){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}4}
\put(375.562,-331.28){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}CNY as the edge serv er , which has 32GB}
\put(296.978,-343.235){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}RAM and an Intel Xeon E-2378G CPU (3.2 GHz clock speed).}
\put(296.978,-355.1899){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}The main simulation parameters and their def ault v alues are}
\put(296.978,-367.1449){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}summarized}
\put(382.2878,-367.1449){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(385.7747,-367.1449){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}III .}
\put(306.94,-379.1809){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}W e inspect the impacts of the parameter on the algo-}
\put(296.978,-391.1359){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}rithms via simulation e xperiments with multiple configura-}
\put(296.978,-403.0909){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}tions, where configurations dif fer only in the v alue of the}
\put(296.978,-415.0459){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}parameter being inspected, while all other parameters are}
\put(296.978,-427.0009){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}fix ed at their def ault v alues. W e in v estig ate the impact of}
\put(296.978,-438.9569){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}k e y parameters on algorithm performance by adjusting the}
\put(296.978,-450.9119){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}number of base stations, the b udget, and t he w orkload scale.}
\put(296.978,-462.8669){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}In the e xperiments, 50 simula tions are conducted for each}
\put(296.978,-474.8218){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}configuration. The performance metrics of the simulations}
\put(296.978,-486.7768){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}for each configuration are coll ected and a v eraged to deri v e}
\put(296.978,-498.7318){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}the performance metric v alue . Additionally , the corresponding}
\put(296.978,-510.6878){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}95\% confidence interv als of these metrics are calculated and}
\put(296.978,-522.6428){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}presented in subsequent figures.}
\put(296.978,-550.9728){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}B.}
\put(305.5558,-550.9728){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(310.5371,-550.9728){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Benc hmark Algorithms}
\put(306.94,-566.3478){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W e compare the proposed GA-PSO with other benchmark}
\put(296.978,-578.3028){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}algorithms in terms of a v erage access delay τ}
\put(491.034,-579.7978){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a vg}
\put(503.553,-578.3028){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, w orkload}
\put(296.978,-590.2588){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}standard de viation ω}
\put(382.042,-591.7528){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}std}
\put(393.21,-590.2588){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}, and the objecti v e function v alue F .}
\put(296.978,-602.2138){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The benchmark algorithms are listed as follo ws.}
\put(296.978,-616.3308){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(1)}
\put(308.5944,-616.3308){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(313.5757,-616.3308){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T opFirst: This algorithm iterati v ely and greedily selects}
\put(313.575,-628.2868){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}base stations with the highest w orkload to deplo y edge}
\put(313.575,-640.2418){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers, and then assigns the base stations to the edge}
\put(313.575,-652.1968){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers based on their weights determined using Eq. ( 24 ).}
\put(296.978,-664.1518){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(2)}
\put(308.5944,-664.1518){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(313.5757,-664.1518){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Random: It randomly sele cts the base stations to deplo y}
\put(313.575,-676.1068){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}edge serv ers, and then as signs the base stations to the edge}
\put(313.575,-688.0618){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers based on their weights determined using Eq. ( 24 ).}
\put(296.978,-700.0178){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(3)}
\put(308.5944,-700.0178){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(313.5757,-700.0178){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}GA: It only uses the tw o-party normal crosso v er in the}
\put(313.575,-711.9728){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v er step.}
\put(296.978,-723.9278){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(4)}
\put(308.5944,-723.9278){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(313.5757,-723.9278){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}A CO+T aboo [ 16 ]: It considers the w orkload of edge}
\put(313.575,-735.8828){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers as pheromone concentration, iterati v ely adds edge}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE 11}
\put(50.56201,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv ers with high pheromone concentration to a taboo list,}
\put(50.56201,-66.39398){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and then uses the taboo list to as sign base stations to the}
\put(50.56201,-78.349){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}edge serv ers.}
\put(33.96401,-90.30402){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(5)}
\put(45.5804,-90.30402){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(50.5617,-90.30402){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}QPSO [ 22 ]: It is a v ariation of PSO that uses a weight}
\put(50.56201,-102.259){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}f actor q :=}
\put(96.24401,-98.17401){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(99.25401,-99.17102){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}max}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(96.244pt, -99.76801pt) -- (112.171pt, -99.76801pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(96.586,-105.695){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}t}
\put(99.595,-106.691){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i,y}
\put(108.175,-108.295){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(113.366,-102.259){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+}
\put(129.371,-98.17395){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}u}
\put(134.082,-99.17096){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(122.311pt, -99.76801pt) -- (144.299pt, -99.76801pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(122.311,-105.695){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}W ( y}
\put(138.029,-106.691){\fontsize{4.9813}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(141.185,-105.695){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791})}
\put(151.527,-102.259){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}for generating initial solutions,}
\put(50.56201,-115.767){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}where W ( y}
\put(100.647,-117.261){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(103.964,-115.767){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}) denotes the total w orkload of all base}
\put(50.56203,-127.722){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}stations within the co v erage area of edge serv er y}
\put(254.967,-129.2161){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}i}
\put(258.2831,-127.722){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}.}
\put(33.96405,-157.022){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}C.}
\put(43.09975,-157.022){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(48.08105,-157.022){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Ef fectiveness of Chr omosome Corr ection}
\put(43.92705,-172.796){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Chromosome correction in the iteration process is essential}
\put(33.96405,-184.751){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}for ensuring solution feasibilit y . W ithout correction, man y}
\put(33.96405,-196.7061){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}chromosomes will violate constraints, resulting in infeasible}
\put(33.96405,-208.6611){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}solutions. T o e v aluate its ef fecti v eness, we analyze the propor -}
\put(33.96405,-220.6161){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}tion of chromosomes requiring correction and the distrib ution}
\put(33.96405,-232.5711){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of viol ation types. W e denote the proportion of chromosomes}
\put(33.96405,-244.5271){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}requiring correction and the proportion of violations within}
\put(33.96405,-256.4821){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a chromosome, gi v en by Eq. ( 25 ) and Eq. ( 26 ). N}
\put(266.816,-257.9761){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}c}
\put(277.272,-256.4821){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}in}
\put(33.96404,-268.4371){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Eq. ( 25 ) represents the number of chromosomes that require}
\put(33.96404,-280.3921){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}corrections.}
\put(204.6035,-280.3921){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(208.0904,-280.3921){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}7 .}
\put(112.091,-306.0891){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}R}
\put(119.655,-307.5841){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}c}
\put(123.714,-306.0891){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(163.147,-299.3501){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}N}
\put(171.152,-300.8441){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}c}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(132.658pt, -303.599pt) -- (205.7pt, -303.599pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(132.658,-312.923){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2 m}
\put(146.387,-314.418){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2cross}
\put(167.795,-312.923){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}+ m}
\put(184.291,-314.418){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3cross}
\put(268.425,-306.089){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(25)}
\put(134.536,-335.504){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}R}
\put(142.1,-336.998){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}n}
\put(147.523,-335.504){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=}
\put(156.467,-328.614){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}|S}
\put(165.268,-330.109){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio}
\put(180.488,-328.614){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}|}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.398pt]
(156.467pt, -333.013pt) -- (183.255pt, -333.013pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(165.849,-342.338){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}V}
\put(268.425,-335.504){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(26)}
\put(43.92699,-356.065){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig.}
\put(59.70775,-356.065){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(64.98793,-356.065){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}7 ( a ) sho ws that R}
\put(144.031,-357.559){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}c}
\put(153.369,-356.065){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}drops sharply at the be ginning}
\put(33.96397,-368.02){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of the iterations, indicating that a lar ge proportion of chro-}
\put(33.96397,-379.975){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}mosomes require correction initially . This is due to the high}
\put(33.96397,-391.93){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}randomness in the population generation process, leading to}
\put(33.96397,-403.8849){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}man y constraint violations after crosso v er and mutation. As}
\put(33.96397,-415.8409){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the iterations progress, the need for chromosome correction}
\put(33.96397,-427.7959){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}gradually decre ases, suggesting an impro v ement in population}
\put(33.96397,-439.7509){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}quality}
\put(190.0182,-439.7509){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(193.3656,-439.7509){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}7 ( b ), the proportion of}
\put(33.96397,-451.7059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}constraint violations is dominated by S}
\put(195.839,-453.2009){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio1}
\put(218.261,-451.7059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and S}
\put(242.396,-453.2009){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}y-vio3}
\put(261.103,-451.7059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}. This}
\put(33.96396,-463.6609){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is mainly because, during the thre e-party crosso v er operation,}
\put(33.96396,-475.6169){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}both the x-chromosome and the y-chromosome ha v e parts}
\put(33.96396,-487.5719){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}from the global optimal solutions and local optimal solutions.}
\put(33.96396,-499.5269){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The e xchange of chromosome se gments dis rupts the depen-}
\put(33.96396,-511.4818){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}denc y between the x-chromosome and y-chromosome, leading}
\put(33.96396,-523.4368){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}to a higher occurrence of violations.}
\put(33.96396,-552.7368){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}D.}
\put(43.64761,-552.7368){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(48.62891,-552.7368){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Impacts of P ar ameter µ}
\put(43.92696,-568.5109){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Recall that, in each SEA-PS operation, GA-PSO greedily}
\put(33.96395,-580.4658){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}selects the SEA pair with the highest weight ξ , which is}
\put(33.96396,-592.4208){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}defined in Eq. ( 24 ). An SEA pair’ s weight is influenced}
\put(33.96396,-604.3759){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}by both the access delay for the base station to access}
\put(33.96396,-616.3308){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the corresponding edge serv er and the w orkload intensity}
\put(33.96396,-628.2869){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of the edge serv er . The parameter µ in Eq. ( 24 ) enables}
\put(33.96396,-640.2418){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}a tradeof f between the relati v e importance of access delay}
\put(33.96396,-652.1968){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and w orkload intensity . µ> 0 . 5 lays more emphasis on access}
\put(33.96396,-664.1519){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}delay , otherwise w orkload intensity is more emphasized. T o}
\put(33.96396,-676.1068){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}inspect the impact of µ on the objecti v e function v alue F}
\put(33.96396,-688.0618){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of the solutions returned by the algorithms, we conducted an}
\put(33.96396,-700.0178){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}e xperiment containing simulations with µ increases from 0 to 1}
\put(33.96394,-711.9728){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}with}
\put(227.3181,-711.9728){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(230.2272,-711.9728){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}8 . The results}
\put(33.96394,-723.9279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}sho w that, although the T opFirst and Random algorithms both}
\put(33.96394,-735.8829){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}utilize}
\put(306.841,-197.028){\includegraphics[width=231.3296pt,height=150.9583pt]{latexImage_9b8d0dba866b58e0748a37fbd3108e72.png}}
\put(306.841,-209.979){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(a) Ev olutions of R}
\put(379.331,-210.976){\fontsize{5.9776}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}c}
\put(386.31,-209.979){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and R}
\put(409.368,-210.976){\fontsize{5.9776}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}n}
\put(417.605,-209.979){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}as}
\put(306.841,-372.993){\includegraphics[width=231.3407pt,height=160.0263pt]{latexImage_4eaae2cfb6c085c1a4ac3b5026aa1d28.png}}
\put(306.841,-385.944){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(b) Ev olutions of ratios of violation types as ite ration progresses.}
\put(296.978,-403.279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig. 7. Results about the ef fecti v eness of chromosome cor -}
\put(296.978,-415.234){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}rection.}
\put(306.841,-611.9709){\includegraphics[width=231.3308pt,height=175.64pt]{latexImage_2c9c1e369d2a5f9782e4af545956528c.png}}
\put(296.978,-626.317){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig. 8. Impacts of parameter µ on objecti v e function v alue}
\put(296.978,-638.273){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}F .}
\put(296.978,-676.107){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}still lags behind the other algorithms. This is primarily because}
\put(296.978,-688.062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}that the deplo yment locations of edge serv ers dif fer between}
\put(296.978,-700.017){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the tw o algori thms. The T opFirst algorithm selects the base}
\put(296.978,-711.973){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}station with the highest w orkloa d , while the Random algorithm}
\put(296.978,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}selects base stations randomly . These deplo yment methods}
\put(296.978,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}often result in suboptimal edge serv er deplo yment locations.}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE}
\put(33.964,-160.208){\includegraphics[width=154.2182pt,height=112.8685pt]{latexImage_bb2a05549cd1fd9ebf37633c13ed62a7.png}}
\put(64.617,-173.159){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(a)}
\put(207.464,-160.208){\includegraphics[width=161.9336pt,height=113.2085pt]{latexImage_9cad751a42ee822af642ff015917ebdb.png}}
\put(228.043,-173.159){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(b)}
\put(388.675,-160.208){\includegraphics[width=159.3654pt,height=114.1388pt]{latexImage_0c6fb722eb114be3bf2c8d240e42f576.png}}
\put(410.608,-173.159){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(c) Objecti v e function v alue F .}
\put(33.96402,-190.494){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig. 9. Impacts of the number of base stations V on the performance metrics of the algorithms.}
\put(33.96402,-224.566){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}When the base station selects the nearest edge serv er ( µ =1) or}
\put(33.964,-236.522){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the least b usy serv er ( µ =0) , the performance of each algorithm}
\put(33.964,-248.4771){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}is significantly lo wer than those of the algorithms using an}
\put(33.964,-260.4321){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}appropriate µ . When µ ∈ [0.3,0.5], the objecti v e function v alues}
\put(33.96401,-272.3871){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of GA and GA-PSO are similar , while the T opFirst algorithm}
\put(33.96401,-284.342){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}still sho ws a do wnw ard trend. Additionally , when µ e xceeds}
\put(33.96402,-296.297){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.5, the objecti v e function v alue of the Random algorithm}
\put(33.96402,-308.253){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}be gins to increase. Based on these results, we set µ =0 . 5 in}
\put(33.964,-320.208){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}subsequent e xperiments to achie v e better performance.}
\put(33.964,-345.424){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}E.}
\put(42.5418,-345.424){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(47.5231,-345.424){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Impacts of Cr osso ver and Mutation Pr obabilities}
\put(43.92701,-360.366){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W e in v estig ated the impacts of crosso v er probability}
\put(33.964,-372.321){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}p}
\put(38.97601,-373.815){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v e r}
\put(78.04301,-372.321){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and mutation probability p}
\put(198.329,-373.815){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m utation}
\put(237.628,-372.321){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}by v arying}
\put(33.964,-384.276){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}p}
\put(38.97601,-385.77){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v e r}
\put(75.408,-384.276){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}from 0 to 1 (step size 0.2) and p}
\put(219.4,-385.77){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m utation}
\put(256.064,-384.276){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}from 0}
\put(33.96402,-396.231){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}to}
\put(155.5675,-396.231){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(159.8514,-396.231){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}IV}
\put(170.362,-396.231){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(174.6459,-396.231){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}presents the corresponding}
\put(33.96402,-408.1859){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}objecti v e function F v alues of the solutions obtained. Under}
\put(33.96402,-420.1419){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}dif ferent probability combinations, GA-PSO e xhibited notable}
\put(33.96402,-432.0969){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}fluctuations in F v alues, indicating that within a constrained}
\put(33.96402,-444.0519){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}iteration b udget, the algorithm’ s e xploration may be insuf-}
\put(33.96402,-456.0069){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ficient to ensure con v er gence to the global optimum. This}
\put(33.96402,-467.9619){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}suggests that while GA-PSO theoretically possesses global}
\put(33.96402,-479.9169){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}con v er gence capabilities, practical iteration limits hinder e x-}
\put(33.96402,-491.8729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}hausti v e e xploration of the solution space, thereby influencing}
\put(33.96402,-503.8279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the quality of final results. Notably , the algorithm achie v ed}
\put(33.96402,-515.7828){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}optimal performance when p}
\put(151.517,-517.2769){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v e r}
\put(183.338,-515.7828){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=0 . 8 and p}
\put(229.999,-517.2769){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m utation}
\put(262.053,-515.7828){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}=0 . 1 ,}
\put(33.96402,-527.7379){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and these parameter settings were adopted in all subsequent}
\put(33.96402,-539.6929){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}e xperiments.}
\put(136.908,-557.9568){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T ABLE IV}
\put(39.67902,-569.9119){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}I M P A C T S O F C R O S S O V E R A N D M U T A T I O N P R O B A B I L I T I E S}
\put(92.54002,-581.8668){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}O N P E R F O R M A N C E O F G A - P S O}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(49.654pt, -591.5654pt) -- (269.3341pt, -591.5654pt)
;
\draw[color_29791,line width=0.269199pt]
(49.7886pt, -621.1672pt) -- (49.7886pt, -591.7007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(53.9666,-618.4989){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}p}
\put(57.35661,-619.5094){\fontsize{4.716939}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v e r}
\put(53.9666,-597.9268){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}F p}
\put(75.2144,-598.9373){\fontsize{4.716939}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}m utation}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269528pt]
(49.9232pt, -600.5955pt) -- (100.9377pt, -621.1672pt)
;
\draw[color_29791,line width=0.269528pt]
(59.2355pt, -591.7006pt) -- (100.9377pt, -621.1672pt)
;
\draw[color_29791,line width=0.269199pt]
(101.0724pt, -621.1672pt) -- (101.0724pt, -591.7007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(116.201,-608.1185){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(134.698pt, -621.1672pt) -- (134.698pt, -591.7007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(145.614,-608.1185){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.05}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(168.3235pt, -621.1672pt) -- (168.3235pt, -591.7007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(180.9245,-608.1185){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.1}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(201.9484pt, -621.1672pt) -- (201.9484pt, -591.7007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(212.8652,-608.1185){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.15}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(235.5739pt, -621.1672pt) -- (235.5739pt, -591.7007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(248.1749,-608.1185){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.2}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(269.1988pt, -621.1672pt) -- (269.1988pt, -591.7007pt)
;
\draw[color_29791,line width=0.269199pt]
(49.654pt, -621.3018pt) -- (269.3341pt, -621.3018pt)
;
\draw[color_29791,line width=0.269199pt]
(49.7886pt, -630.3315pt) -- (49.7886pt, -621.4364pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(73.74599,-627.6632){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(101.0724pt, -630.3315pt) -- (101.0724pt, -621.4364pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(105.2504,-627.6632){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.371332}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(134.698pt, -630.3315pt) -- (134.698pt, -621.4364pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(138.876,-627.6632){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.336793}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(168.3235pt, -630.3315pt) -- (168.3235pt, -621.4364pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(172.5008,-627.6632){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.336602}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(201.9484pt, -630.3315pt) -- (201.9484pt, -621.4364pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(206.1264,-627.6632){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335596}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(235.5739pt, -630.3315pt) -- (235.5739pt, -621.4364pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(239.7519,-627.6632){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335316}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(269.1988pt, -630.3315pt) -- (269.1988pt, -621.4364pt)
;
\draw[color_29791,line width=0.269199pt]
(49.654pt, -630.4661pt) -- (269.3341pt, -630.4661pt)
;
\draw[color_29791,line width=0.269199pt]
(49.7886pt, -639.4958pt) -- (49.7886pt, -630.6007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(71.21902,-636.8275){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.2}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(101.0724pt, -639.4958pt) -- (101.0724pt, -630.6007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(105.2504,-636.8275){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.361348}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(134.698pt, -639.4958pt) -- (134.698pt, -630.6007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(138.876,-636.8275){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.337069}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(168.3235pt, -639.4958pt) -- (168.3235pt, -630.6007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(172.5008,-636.8275){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335494}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(201.9484pt, -639.4958pt) -- (201.9484pt, -630.6007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(206.1264,-636.8275){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335578}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(235.5739pt, -639.4958pt) -- (235.5739pt, -630.6007pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(239.7519,-636.8275){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.337569}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(269.1988pt, -639.4958pt) -- (269.1988pt, -630.6007pt)
;
\draw[color_29791,line width=0.269199pt]
(49.654pt, -639.6304pt) -- (269.3341pt, -639.6304pt)
;
\draw[color_29791,line width=0.269199pt]
(49.7886pt, -648.6607pt) -- (49.7886pt, -639.7656pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(71.21902,-645.9917){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.4}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(101.0724pt, -648.6607pt) -- (101.0724pt, -639.7656pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(105.2504,-645.9917){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.351454}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(134.698pt, -648.6607pt) -- (134.698pt, -639.7656pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(138.876,-645.9917){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.337224}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(168.3235pt, -648.6607pt) -- (168.3235pt, -639.7656pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(172.5008,-645.9917){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335983}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(201.9484pt, -648.6607pt) -- (201.9484pt, -639.7656pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(206.1264,-645.9917){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.336053}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(235.5739pt, -648.6607pt) -- (235.5739pt, -639.7656pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(239.7519,-645.9917){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.336428}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(269.1988pt, -648.6607pt) -- (269.1988pt, -639.7656pt)
;
\draw[color_29791,line width=0.269199pt]
(49.654pt, -648.7953pt) -- (269.3341pt, -648.7953pt)
;
\draw[color_29791,line width=0.269199pt]
(49.7886pt, -657.825pt) -- (49.7886pt, -648.9299pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(71.21902,-655.156){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.6}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(101.0724pt, -657.825pt) -- (101.0724pt, -648.9299pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(105.2504,-655.156){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.350918}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(134.698pt, -657.825pt) -- (134.698pt, -648.9299pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(138.876,-655.156){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.336788}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(168.3235pt, -657.825pt) -- (168.3235pt, -648.9299pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(172.5008,-655.156){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335640}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(201.9484pt, -657.825pt) -- (201.9484pt, -648.9299pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(206.1264,-655.156){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.336995}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(235.5739pt, -657.825pt) -- (235.5739pt, -648.9299pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(239.7519,-655.156){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335793}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(269.1988pt, -657.825pt) -- (269.1988pt, -648.9299pt)
;
\draw[color_29791,line width=0.269199pt]
(49.654pt, -657.9596pt) -- (269.3341pt, -657.9596pt)
;
\draw[color_29791,line width=0.269199pt]
(49.7886pt, -666.9893pt) -- (49.7886pt, -658.0942pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(71.21902,-664.3209){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.8}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(101.0724pt, -666.9893pt) -- (101.0724pt, -658.0942pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(105.2504,-664.3209){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.350277}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(134.698pt, -666.9893pt) -- (134.698pt, -658.0942pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(138.876,-664.3209){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.336215}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(168.3235pt, -666.9893pt) -- (168.3235pt, -658.0942pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(172.5008,-664.3209){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}0.335272}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(201.9484pt, -666.9893pt) -- (201.9484pt, -658.0942pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(206.1264,-664.3209){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335815}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(235.5739pt, -666.9893pt) -- (235.5739pt, -658.0942pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(239.7519,-664.3209){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335675}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(269.1988pt, -666.9893pt) -- (269.1988pt, -658.0942pt)
;
\draw[color_29791,line width=0.269199pt]
(49.654pt, -667.1238pt) -- (269.3341pt, -667.1238pt)
;
\draw[color_29791,line width=0.269199pt]
(49.7886pt, -676.1536pt) -- (49.7886pt, -667.2585pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(73.74599,-673.4852){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(101.0724pt, -676.1536pt) -- (101.0724pt, -667.2585pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(105.2504,-673.4852){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.356788}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(134.698pt, -676.1536pt) -- (134.698pt, -667.2585pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(138.876,-673.4852){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335439}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(168.3235pt, -676.1536pt) -- (168.3235pt, -667.2585pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(172.5008,-673.4852){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.336040}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(201.9484pt, -676.1536pt) -- (201.9484pt, -667.2585pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(206.1264,-673.4852){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335531}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(235.5739pt, -676.1536pt) -- (235.5739pt, -667.2585pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(239.7519,-673.4852){\fontsize{6.738503}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}0.335848}
\end{picture}
\begin{tikzpicture}[overlay]
\path(0pt,0pt);
\draw[color_29791,line width=0.269199pt]
(269.1988pt, -676.1536pt) -- (269.1988pt, -667.2585pt)
;
\draw[color_29791,line width=0.269199pt]
(49.654pt, -676.2881pt) -- (269.3341pt, -676.2881pt)
;
\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-708.986){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}F}
\put(41.19685,-708.986){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}}
\put(46.17815,-708.986){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}Impacts of Number of Base Stations}
\put(43.927,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}W e in v estig ated the impacts of base station number V on}
\put(33.964,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}algorithm performance by letting V v ary from 300 to 600 in}
\put(296.978,-224.566){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}increments}
\put(493.2412,-224.566){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(496.8477,-224.566){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}9 , where the}
\put(296.978,-236.522){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}sub-figures depict the a v erage access delay , w orkload standard}
\put(296.978,-248.4771){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}de viation, and objecti v e function v alue F .}
\put(306.94,-260.587){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}The}
\put(385.0468,-260.587){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(389.4005,-260.587){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}9}
\put(444.3143,-260.587){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(448.668,-260.587){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}9 ( b ) sho w that, in most}
\put(296.978,-272.542){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}cases, the a v erage access delay of GA-PSO is shorter than}
\put(296.978,-284.497){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}that of the other algorithms. When V =600 , the a v erage access}
\put(296.978,-296.452){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}delay of A CO-T aboo and QPSO is almost the same as that}
\put(296.978,-308.408){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}of GA-PSO, whe reas the w orkload standard de viation of GA-}
\put(296.978,-320.363){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}PSO is significantly lo wer than that of the tw o algorithms. This}
\put(296.978,-332.318){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}is because GA-PSO considers both the a v erage access delay}
\put(296.978,-344.2729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}and w orkload standard de viation by introducing the ξ v alue}
\put(296.978,-356.2279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}during base station allocation. W ith the continuous gro wth of}
\put(296.978,-368.1839){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}the number of base stations, the a v erage access delay of the}
\put(296.978,-380.1389){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}algorithms all gradually increases. This is primarily because}
\put(296.978,-392.0939){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}the edge serv ers closest to the base stations gradually lack}
\put(296.978,-404.0489){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}computational resources, forcing the base stations to prefer}
\put(296.978,-416.0039){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}edge serv ers a little f ar a w ay yet ha v e relati v ely suf ficient}
\put(296.978,-427.9589){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}resources, thereby leading to increased access delay . Moreo v er ,}
\put(296.978,-439.9149){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}both the T opFirst and Random algorithms e xhibit poorer per -}
\put(296.978,-451.8698){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}formance. Although both algorithms introduce the ξ parameter}
\put(296.978,-463.8248){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}during edge serv er allocation to optim ize resource distrib ution,}
\put(296.978,-475.7798){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}the y dif fer significantly in their s erv er selection mechanisms.}
\put(296.978,-487.7348){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}Specifically , T opFirst adopts a priority-based selection strat-}
\put(296.978,-499.6898){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}e gy , while Random selects serv ers randomly . These selection}
\put(296.978,-511.6458){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}criteria lead to suboptimal geographical distrib ution of the}
\put(296.978,-523.6008){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}chosen edge serv ers, thereby compromising the o v erall system}
\put(296.978,-535.5558){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}performance.}
\put(306.94,-547.6658){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}Fig.}
\put(322.7208,-547.6658){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(326.6161,-547.6658){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}9 ( c ) demonstrates that the objecti v e function v alue F}
\put(296.978,-559.6208){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}of GA-PSO consistently outperforms those of other algorithms}
\put(296.978,-571.5758){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}across all scenarios. Specifically , when V =600 , the F v alue}
\put(296.978,-583.5317){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}of GA-PSO is 0.3762, e xhibiting impro v ements of 27 . 56\% ,}
\put(296.978,-595.4868){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}28 . 43\% , 14 . 50\% , 11 . 67\% , and 2 . 22\% compared to T opFirst,}
\put(296.978,-607.4418){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}Random, A CO-T aboo, QPSO, and GA, respecti v ely .}
\put(296.978,-636.4058){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}G.}
\put(306.6617,-636.4058){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}}
\put(311.643,-636.4058){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}Impacts of Budg et}
\put(306.94,-652.0417){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}W e in v estig ated the impacts of b udget B on algorithm}
\put(296.978,-663.9968){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}performance by letting B v ary from 1 . 5 × 10}
\put(481.458,-660.3818){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}5}
\put(489.826,-663.9968){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}to 4 . 5 × 10}
\put(531.9171,-660.3818){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}5}
\put(540.285,-663.9968){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}in}
\put(296.978,-675.9518){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}increments of 0 . 5 × 10}
\put(386.407,-672.3368){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}5}
\put(390.877,-675.9518){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}.}
\put(517.2526,-675.9518){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(520.7396,-675.9518){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}10 .}
\put(306.94,-688.0618){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}The}
\put(381.1016,-688.0618){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(384.1501,-688.0618){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}10}
\put(422.5958,-688.0618){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(425.6344,-688.0618){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}10 ( b ) sho w that, as the b udget}
\put(296.978,-700.0178){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}increases, the a v erage access delay of dif ferent algorithms}
\put(296.978,-711.9728){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}e xhibits a do wnw ard trend. This is because a higher b udget al-}
\put(296.978,-723.9278){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}lo ws for the deplo yment of more edge serv ers and serv er units,}
\put(296.978,-735.8828){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}thereby reducing the a v erage access delay . Correspondingly ,}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE}
\put(33.964,-159.277){\includegraphics[width=154.2293pt,height=112.9472pt]{latexImage_662b34ed9da64aa3ce94b1371c211474.png}}
\put(64.617,-172.229){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(a)}
\put(207.464,-159.2769){\includegraphics[width=161.9336pt,height=113.2085pt]{latexImage_12a89687a3a653aca671a848afd410dc.png}}
\put(228.043,-172.229){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(b)}
\put(388.675,-159.277){\includegraphics[width=159.3685pt,height=112.2867pt]{latexImage_8a172ed679440ee7dcc198f42dada482.png}}
\put(410.608,-172.229){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(c) Objecti v e function v alue F .}
\put(33.96402,-189.564){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig.}
\put(33.964,-320.23){\includegraphics[width=154.2293pt,height=113.333pt]{latexImage_de6fdb313424a5e70969ac73c340f3c8.png}}
\put(64.617,-333.182){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(a)}
\put(207.464,-320.23){\includegraphics[width=161.9336pt,height=113.2085pt]{latexImage_570422efe2901db30ce7a6b2e67170b7.png}}
\put(228.043,-333.182){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(b)}
\put(388.675,-320.23){\includegraphics[width=159.3685pt,height=112.2867pt]{latexImage_9e35a662e8b17b7477bc53347ab5ab6c.png}}
\put(410.608,-333.182){\fontsize{8.9664}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}(c) Objecti v e function v alue F .}
\put(33.96402,-350.517){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig. 11. Impacts of the w orkload scale ν}
\put(207.462,-352.011){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}LoadScale}
\put(246.427,-350.517){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}on the performance metrics of the algorithms.}
\put(33.96404,-384.589){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}with b udget gro wth, the w orkload standard de viation of GA-}
\put(33.96404,-396.544){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}PSO sho ws a gro wing trend, whereas that of other algorithms}
\put(33.96404,-408.499){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}remains relati v ely stable. This is primarily due to the f act that}
\put(33.96404,-420.4539){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}increased b udget leads to a corresponding rise in the number}
\put(33.96404,-432.4099){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}of edge serv ers, thus more edge serv ers can be connected to}
\put(33.96404,-444.3649){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}base stations. That is, there are probably closer edge serv ers}
\put(33.96404,-456.3199){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}a v ailable. In this case, guided by the ξ v alue, base stations}
\put(33.96404,-468.2749){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}prefer to connect to closer edge serv ers, ult imately causing}
\put(33.96404,-480.2299){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}the w orkload standard de viation of GA-PSO to increase.}
\put(33.96404,-492.1849){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}Moreo v er , as the b udget increases, the objecti v e function v alue}
\put(33.96404,-504.1409){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}of the GA-PSO algorithm gradually con v er ges and stabilizes.}
\put(33.96404,-516.0958){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}This phenomenon is primarily attrib uted to the la w of di-}
\put(33.96404,-528.0508){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}minishing mar ginal utility: increasing edge serv er deplo yment}
\put(33.96404,-540.0059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}significantly reduces user access delay and brings ob vious}
\put(33.96404,-551.9609){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}performance impro v ements. Ho we v er , when the number of}
\put(33.96404,-563.9169){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}serv ers reaches a certain scale, the performance g ain from}
\put(33.96404,-575.8718){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}adding ne w serv ers weak ens, and insuf ficient utilization of}
\put(33.96404,-587.8268){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}some serv er resources emer ges, slo wi ng the gro wth of o v erall}
\put(33.96404,-599.7819){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}benefits.}
\put(43.92603,-611.6108){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}Fig.}
\put(59.70679,-611.6108){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(64.55858,-611.6108){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}10 ( c ) demonstrat es that the objecti v e function v alue}
\put(33.96404,-623.5659){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}F of GA-PSO remains consistently the lo west among all}
\put(33.96404,-635.5209){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}algorithms. Specifically , when B =4 . 5 × 10}
\put(209.793,-631.9059){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}5}
\put(214.262,-635.5209){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}, the F v alue of}
\put(33.96404,-647.4758){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}GA-PSO is 0.3219, outperform ing T opFirst, Random, A CO-}
\put(33.96404,-659.4308){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}T aboo, QPSO, and GA by 24 . 77\% , 24 . 22\% , 15 . 95\% , 10 . 76\% ,}
\put(33.96405,-671.3859){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}and 2 . 81\% , respecti v ely .}
\put(33.96405,-697.0059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}H.}
\put(43.6477,-697.0059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}}
\put(48.629,-697.0059){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}Impacts of W orkload Scale}
\put(43.92705,-711.9729){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}W e in v estig ated the impacts of w orkload scal e ν}
\put(249.544,-713.4669){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}LoadScale}
\put(33.96404,-723.9279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}on algorithm performance by letting ν}
\put(190.005,-725.4219){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}LoadScale}
\put(228.813,-723.9279){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}v ary from 1 . 0}
\put(33.96402,-735.8829){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}to}
\put(269.2188,-735.8829){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(272.5662,-735.8829){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}11 .}
\put(306.94,-384.5889){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}As}
\put(395.5772,-384.5889){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(400.2995,-384.5889){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}11 ( a ), GA-PSO e xhibits the lo west}
\put(296.978,-396.5439){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}a v erage access delay among all algorithms. Furthermore, as}
\put(296.978,-408.4988){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}the w orkload scale increases, the a v erage access delay of}
\put(296.978,-420.4548){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}all algorithms rises correspondingly . This phenomenon arises}
\put(296.978,-432.4098){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}because when the computational resources of the nearest edge}
\put(296.978,-444.3648){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}serv er to a base station become insuf ficient, the base station}
\put(296.978,-456.3198){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}is compelled to connect to a more distant s erv er . Notably ,}
\put(296.978,-468.2748){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}the performance g ap between GA-PSO and competing al-}
\put(296.978,-480.2298){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}gorithms widens progressi v ely , highlighting GA-PSO’ s supe-}
\put(296.978,-492.1858){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}rior adaptability under high w orkloads. Its deplo yment strat-}
\put(296.978,-504.1407){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}e gy optimally positions edge serv ers closer to user clusters,}
\put(296.978,-516.0957){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}thereby minimizing access latenc y more ef fecti v ely than other}
\put(296.978,-528.0507){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}methods. At ν}
\put(358.638,-529.5457){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}LoadScale}
\put(394.116,-528.0507){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}=2 . 2 , GA-PSO achie v es an a v erage}
\put(296.978,-540.0057){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}access delay of 31.3013 µ s, outperforming T opFirst, Random,}
\put(296.978,-551.9617){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}A CO-T aboo, QPSO, and GA by 11 . 31\% , 11 . 10\% , 6 . 33\% ,}
\put(296.978,-563.9167){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}3 . 97\% , and 3 . 98\% , respecti v ely . When the w orkload scale}
\put(296.978,-575.8717){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}e xceeds 1.8, a pronounced decline in the w orkload standard}
\put(296.978,-587.8267){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}de viation is observ ed across all algorithms. This trend is}
\put(296.978,-599.7817){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}primarily attrib uted to the load balancing mechanism : when}
\put(296.978,-611.7368){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}the occupanc y rate of an edge serv er surpasses the threshold}
\put(296.978,-623.6927){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_274846}th , it prioritizes increasing serv er units o v er deplo ying ne w}
\put(296.978,-635.6477){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}serv ers. Consequent ly , w orkloads from multiple base stations}
\put(296.978,-647.6027){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}may concentrate on a fe w high-capacity serv ers, leading to}
\put(296.978,-659.5577){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}underutilization of the others.}
\put(306.941,-676.1068){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}As}
\put(382.8361,-676.1068){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(388.0167,-676.1068){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}11 ( c ), the objecti v e function v alue F}
\put(296.978,-688.0627){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}of GA-PSO remains consistently lo wer than those of other}
\put(296.978,-700.0178){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}algorithms. When ν}
\put(381.415,-701.5117){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}LoadScale}
\put(416.894,-700.0178){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}=1 . 8 , GA-PSO’ s F is 0.3099,}
\put(296.978,-711.9727){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}which is 31 . 08\% , 30 . 60\% , 16 . 62\% , 13 . 17\% , and 2 . 50\% lo wer}
\put(296.9781,-723.9277){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}than those of T opFirst, Random, A CO-T aboo, QPSO, and GA,}
\put(296.9781,-735.8828){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_274846}respecti v ely .}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE 14}
\put(33.96402,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}I.}
\put(39.77222,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}}
\put(44.75352,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Impacts of Iter ation Number}
\put(43.92702,-73.23596){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T o analyze the con v er gence characteristics of the iterati v e}
\put(33.96402,-85.19098){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}algorithms (GA, QPSO, and GA-PSO), we plot the e v olution}
\put(33.96402,-97.146){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}curv es of their objecti v e function v alues as the iteration}
\put(33.96402,-109.101){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}progresses}
\put(110.5665,-109.101){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}}
\put(116.0758,-109.101){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_30046}12 . All algorithms demonstrate a rapid}
\put(33.96402,-121.057){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}decrease in the objecti v e function v alue during the first 200}
\put(33.96402,-133.012){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}iterations, follo wed by a stabilization trend after approximately}
\put(33.96402,-144.967){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}500 iterations.}
\put(43.92702,-157.98){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}The objecti v e function v alue of QPSO decreases rapidly in}
\put(33.96402,-169.936){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the initial iterati ons, b ut as iterations progress, QPSO becomes}
\put(33.96402,-181.8911){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}trapped in a local optimum and f ails to perform ef fici ent global}
\put(33.96402,-193.8461){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}e xploration. GA can e xplore a broader solution space through}
\put(33.96402,-205.8011){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}multi-generation e v olution, b ut its con v er gence speed is com-}
\put(33.96402,-217.7561){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}parati v ely slo w . By inte grating the global search capability}
\put(33.96402,-229.7111){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of GA with the f ast con v er gence characteristic of PSO, GA-}
\put(33.96402,-241.6671){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}PSO enables ef ficient disco v ery of better solutions within a}
\put(33.96402,-253.6221){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}shorter time frame. It is important to note that due to the}
\put(33.96402,-265.5771){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}constraint on iteration count, GA-PSO may not e xhausti v ely}
\put(33.96402,-277.5321){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}e xplore the entire solution space, resulting in a near -global}
\put(33.96402,-289.4871){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}optimal}
\put(52.793,-461.055){\includegraphics[width=213.4028pt,height=152.8408pt]{latexImage_f3878ebd078617511a4f61e00ea636e8.png}}
\put(33.964,-475.401){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Fig. 12. Con v er gence e v olution of objecti v e function F for}
\put(33.96397,-487.356){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}GA, QPSO, and GA-PSO.}
\put(121.197,-535.112){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}V}
\put(135.1944,-535.112){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(140.6739,-535.112){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}C O N C L U S I O N}
\put(43.92597,-556.555){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}In this paper , we addressed the BC-ESED problem, where}
\put(33.96397,-568.511){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}the objecti v e is to minimize the a v erage access delay be-}
\put(33.96397,-580.466){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}tween users and edge serv ers and the w orkload de viation.}
\put(33.96397,-592.421){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W e formulated the problem as a multi-objecti v e optimization}
\put(33.96397,-604.376){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}problem and pro v ed its NP-hardness. W e then combined GA}
\put(33.96397,-616.331){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and PSO to propose our GA-PSO algorithm for solvi ng the}
\put(33.96397,-628.286){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}BC-ESED problem. GA-PSO emplo ys a four -step selection-}
\put(33.96397,-640.242){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}crosso v er -mutation-correction iteration frame w ork, where the}
\put(33.96397,-652.197){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}three-party G-L-I crosso v er operation inspired by PSO com-}
\put(33.96397,-664.152){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}plements the tw o-party normal cros so v er operation in GA. W e}
\put(33.96397,-676.107){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}conducted e xtensi v e simulation e xperiments on a realistic base}
\put(33.96397,-688.062){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}station topology and w orkload statistics from the Shanghai}
\put(33.96397,-700.018){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}T elecom base station dataset to comparati v ely e v aluate GA-}
\put(33.96397,-711.973){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}PSO’ s performance. The results v alidate GA-PSO’ s superiority}
\put(33.96397,-723.928){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}o v er other benchmark algorithms in terms of a v erage access}
\put(33.96397,-735.883){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}delay and w orkload de viation.}
\put(394.612,-54.43799){\fontsize{9.9626}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}R E F E R E N C E S}
\put(300.963,-76.88501){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[1]}
\put(310.2562,-76.88501){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-76.88501){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Y . Chiang, Y . Zhang, H. Luo, T .-Y . Chen, G.-H. Chen, H.-T . Chen, Y .-J.}
\put(315.237,-85.85101){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}W ang, H.-Y . W ei, and C.-T . Chou, “Management and orchestration of}
\put(315.237,-94.81702){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}edge computing for IoT: A comprehensi v e surv e y , ” IEEE Internet of}
\put(315.237,-103.784){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Things J ournal , v ol. 10, no. 16, pp. 14 307–14 331, 2023.}
\put(300.963,-113.456){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[2]}
\put(310.2562,-113.456){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-113.456){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}X. Zhang, B. Y ang, Z. Y u, X. Cao, G. C. Ale xandropoulos, Y . Zhang,}
\put(315.237,-122.423){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}M. Debbah, and C. Y uen, “Reconfigurable intelligent computational}
\put(315.237,-131.389){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}surf aces for MEC-assisted autonomous dri ving netw orks: Design opti-}
\put(315.237,-140.356){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}mization and analysis, ” IEEE T r ansactions on Intellig ent T r ansportation}
\put(315.237,-149.322){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Systems , v ol. 26, no. 1, pp. 1286–1303, 2025.}
\put(300.963,-158.9949){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[3]}
\put(310.2562,-158.9949){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-158.9949){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}X. Shi, S. Zhang, M. Liu, L. Meng, L. W ei, Y . Gu, K. Liu, H. Cheng,}
\put(315.237,-167.9609){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Y . Song, L. T ang, A. Zhu, N. Chen, and Z. Qian, “Mystique: User -le v el}
\put(315.237,-176.9269){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}adaptation for real-time video analytics in edge netw orks via meta-RL, ”}
\put(315.237,-185.8939){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}IEEE T r ansactions on Mobile Computi ng , v ol. 24, no. 5, pp. 3615–3632,}
\put(315.237,-194.8599){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2025.}
\put(300.963,-204.5329){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[4]}
\put(310.2562,-204.5329){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-204.5329){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Y . Gong, H. Y ao, J. W ang, M. Li, and S. Guo, “Edge intelligence-dri v en}
\put(315.237,-213.4989){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}joint of floading and resource allocation for future 6G industrial Internet}
\put(315.237,-222.4649){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of Things, ” IEEE T r ansactions on Network Science and Engineering ,}
\put(315.237,-231.4319){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v ol. 11, no. 6, pp. 5644–5655, 2024.}
\put(300.963,-241.1049){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[5]}
\put(310.2562,-241.1049){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-241.1049){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}X. Chen, L. Jiao, W . Li, and X. Fu, “Ef ficient multi-user computation}
\put(315.237,-250.0709){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}of floading for mobile-edge cloud computing, ” IEEE/A CM T r ansactions}
\put(315.237,-259.0369){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}on Networking , v ol. 24, no. 5, pp. 2795–2808, 2016.}
\put(300.963,-268.7098){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[6]}
\put(310.2561,-268.7098){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2374,-268.7098){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}R. Y ada v , W . Zhang, K. Li, C. Liu, M. Shafiq, and N. K. Karn, “ An}
\put(315.237,-277.6758){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}adapti v e heuristic for managing ener gy consumption and o v erloaded}
\put(315.237,-286.6429){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}hosts in a cloud data center , ” W ir eless Networks , v ol. 26, pp. 1905–}
\put(315.237,-295.6089){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}1919, 2020.}
\put(300.963,-305.2819){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[7]}
\put(310.2561,-305.2819){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2374,-305.2819){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}R. Y ada v , W . Zhang, O. Kaiw artya, P . R. Singh, I. A. Elgendy , and}
\put(315.237,-314.2479){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Y .-C. T ian, “ Adapti v e ener gy-a w are algorithms for minimizing ener gy}
\put(315.237,-323.2139){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}consumption and SLA violation in cloud computing, ” IEEE Access ,}
\put(315.2369,-332.1809){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v ol. 6, pp. 55 923–55 936, 2018.}
\put(300.963,-341.8529){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[8]}
\put(310.2561,-341.8529){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2374,-341.8529){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}R. Y ada v and W . Zhang, “Mere g: Managing ener gy-sla tradeof f for}
\put(315.2369,-350.8199){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}green mobile cloud computing, ” W ir eless Communications and Mobile}
\put(315.2369,-359.7859){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Computing , v ol. 2017, 2017, Art. no. 6741972.}
\put(300.963,-369.4589){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[9]}
\put(310.2561,-369.4589){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2374,-369.4589){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}N. Abbas, Y . Zhang, A. T aherk ordi , and T . Sk eie, “Mobi le edge}
\put(315.2369,-378.4249){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}computing: A surv e y , ” IEEE Internet of Things J ournal , v ol. 5, no. 1,}
\put(315.2369,-387.3919){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}pp. 450–465, 2018.}
\put(296.9779,-397.0639){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[10]}
\put(310.2561,-397.0639){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-397.0639){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}R. Y ada v , W . Zhang, K. Li, C. Liu, and A. A. Laghari, “Managing}
\put(315.2369,-406.0309){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}o v erloaded hosts for ener gy-ef ficienc y in cloud data centers, ” Cluster}
\put(315.2369,-414.9969){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Computing , v ol. 24, pp. 2001–2015, 2021.}
\put(296.9779,-424.6699){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[11]}
\put(310.2561,-424.6699){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-424.6699){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}S. Y ang, F . Li, M. Shen, X. C hen, X. Fu, and Y . W ang, “Cloudlet}
\put(315.2369,-433.6359){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}placement and task allocation in mobile edge computing, ” IEEE Internet}
\put(315.2369,-442.6019){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}of Things J ournal , v ol. 6, no. 3, pp. 5853–5863, 2019.}
\put(296.9779,-452.2749){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[12]}
\put(310.2561,-452.2749){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-452.2749){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Z. Xu, W . Liang, W . Xu, M. Jia, and S. Guo, “Ef ficient algorithms for}
\put(315.2369,-461.2409){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}capacitated cloudlet placements, ” IEEE T r ansactions on P ar allel and}
\put(315.2369,-470.2079){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Distrib uted Systems , v ol. 27, no. 10, pp. 2866–2880, 2016.}
\put(296.9779,-479.8799){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[13]}
\put(310.2561,-479.8799){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-479.8799){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}X. Huang, B. Zhang, G. Ji, and C. Li, “Service coalition based joint}
\put(315.2369,-488.847){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}application deplo yment and task assi gnment for mobile edge comput-}
\put(315.2369,-497.813){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ing, ” IEEE T r ansactions on V ehicular T ec hnolo gy , v ol. 73, no. 5, pp.}
\put(315.2369,-506.78){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}7007–7018, 2024.}
\put(296.9779,-516.452){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[14]}
\put(310.2561,-516.452){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-516.452){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}S. W ang, Y . Zhao, J. Xu, J. Y uan, and C.-H. Hsu, “Edge serv er place-}
\put(315.2369,-525.4189){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}ment in mobile edge computing, ” J ournal of P ar allel and Distrib uted}
\put(315.2369,-534.385){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Computing , v ol. 127, pp. 160–168, 2019.}
\put(296.9779,-544.058){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[15]}
\put(310.2561,-544.058){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-544.058){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}S. K. Kasi, M. K. Kasi, K. Ali, M. Raza, H. Afzal, A. Lasebae,}
\put(315.2369,-553.024){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}B. Naeem, S. u. Islam, and J. J. P . C. Rodrigues, “Heuristic edge serv er}
\put(315.2369,-561.99){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}placement in industrial Internet of Things and cellular netw orks, ” IEEE}
\put(315.2369,-570.957){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Internet of Things J ournal , v ol. 8, no. 13, pp. 10 308–10 317, 2021.}
\put(296.9779,-580.629){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[16]}
\put(310.2561,-580.629){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-580.629){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}F . Guo, B. T ang, and J. Zhang, “Mobile edge serv er placement based}
\put(315.2369,-589.5959){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}on meta-heuristic algorithm, ” J ournal of Intellig ent \& Fuzzy Systems ,}
\put(315.2369,-598.562){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v ol. 40, no. 5, pp. 8883–8897, 2021.}
\put(296.9779,-608.235){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[17]}
\put(310.2561,-608.235){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-608.235){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}B. Cao, S. F an, J. Zhao, S. T ian, Z. Zheng, Y . Y an, and P . Y ang, “Lar ge-}
\put(315.2369,-617.201){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}scale man y-objecti v e deplo yment optimization of edge serv ers, ” IEEE}
\put(315.2369,-626.168){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}T r ansactions on Intellig ent T r ansportation Systems , v ol. 22, no. 6, pp.}
\put(315.2369,-635.134){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}3841–3849, 2021.}
\put(296.9779,-644.807){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[18]}
\put(310.2561,-644.807){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-644.807){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}X. Zhang, Z. Li, C. Lai, and J. Zhang, “Joint edge serv er placement and}
\put(315.2369,-653.773){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}service placement in mobile-edge com puting, ” IEEE Internet of Things}
\put(315.2369,-662.739){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}J ournal , v ol. 9, no. 13, pp. 11 261–11 274, 2022.}
\put(296.9779,-672.412){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[19]}
\put(310.2561,-672.412){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-672.412){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}L. Lo vén, T . Lähderanta, L. Ruha, T . Leppänen, E. Peltonen, J. Riekki,}
\put(315.2369,-681.378){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and M. J. Sillanpää, “Scaling up an edge serv er deplo yment, ” in 2020}
\put(315.2369,-690.345){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}IEEE International Confer ence on P ervasive Computing and Communi-}
\put(315.2369,-699.311){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}cations W orkshops (P erCom W orkshops) , 2020, pp. 1–7.}
\put(296.9779,-708.984){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[20]}
\put(310.2561,-708.984){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(315.2375,-708.984){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}J. Bi, H. Y uan, S. Duanmu, M. Zhou, and A. Ab usorrah, “Ener gy-}
\put(315.2369,-717.95){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}optimized partial computation of floading in mobile-edge computing with}
\put(315.2369,-726.917){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}genetic simulated-annealing-based particle sw arm optimization, ” IEEE}
\put(315.2369,-735.883){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Internet of Things J ournal , v ol. 8, no. 5, pp. 3774–3785, 2021.}
\end{picture}
\newpage
\begin{tikzpicture}[overlay]\path(0pt,0pt);\end{tikzpicture}
\begin{picture}(-5,0)(2.5,0)
\put(33.964,-21.22101){\fontsize{6.9738}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}IEEE 15}
\put(33.96402,-54.43799){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[21]}
\put(47.24221,-54.43799){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22352,-54.43799){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Y . Li and S. W ang, “ An ener gy-a w are edge serv er placement algorithm}
\put(52.22302,-63.40497){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}in mobile edge computing, ” in 2018 IEEE International Confer ence on}
\put(52.22301,-72.37097){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Edg e Computing (EDGE) , 2018, pp. 66–73.}
\put(33.96402,-81.33698){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[22]}
\put(47.24221,-81.33698){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22352,-81.33698){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Y . Li, A. Zhou, X. Ma, and S. W ang, “Profit-a w are edge serv er}
\put(52.22302,-90.30396){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}placement, ” IEEE Internet of Things J ournal , v ol. 9, no. 1, pp. 55–67,}
\put(52.22301,-99.26996){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2022.}
\put(33.964,-108.2369){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[23]}
\put(47.24219,-108.2369){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.2235,-108.2369){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}F . Zeng, Y . Ren, X. Deng, and W . Li, “Cost-ef fecti v e edge serv er}
\put(52.22301,-117.2029){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}placement in wireless metropolitan area netw orks, ” Sensor s , v ol. 19,}
\put(52.22301,-126.1689){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}no. 1, 2018, Art. no. 32.}
\put(33.964,-135.1359){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[24]}
\put(47.24219,-135.1359){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.2235,-135.1359){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G. Cui, Q. He, X. Xia, F . Chen, H. Jin, and Y . Y ang, “Rob ustness-}
\put(52.22301,-144.1019){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}oriented k edge serv er placement, ” in 2020 20th IEEE/A CM Interna-}
\put(52.22301,-153.0679){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}tional Symposium on Cluster , Cloud and Internet Computing (CCGRID) ,}
\put(52.22302,-162.0349){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}2020, pp. 81–90.}
\put(33.96402,-171.0009){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[25]}
\put(47.24221,-171.0009){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22352,-171.0009){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Y . Gao, J. T ao, H. W ang, Z. W ang, W . Sun, and C. Song, “Joint}
\put(52.22302,-179.9679){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}serv er deplo yment and task scheduling for the maximal profit in mobile-}
\put(52.22302,-188.9339){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}edge computing, ” IEEE Internet of Things J ournal , v ol. 10, no. 24, pp.}
\put(52.22302,-197.8999){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}22 501–22 513, 2023.}
\put(33.96402,-206.8669){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[26]}
\put(47.24221,-206.8669){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22352,-206.8669){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Y . Guo, S. W ang, A. Zhou, J. Xu, J. Y uan, and C.- H. Hsu, “User}
\put(52.22302,-215.8329){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}allocation-a w are edge cloud placement in mobile edge computing, ”}
\put(52.22302,-224.7989){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Softwar e: Pr actice and Experience , v ol. 50, no. 5, pp. 489–502, 2020.}
\put(33.96402,-233.7659){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[27]}
\put(47.24221,-233.7659){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22352,-233.7659){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}R. Y ada v , W . Zhang, O. Kaiw artya, H. Song, and S. Y u, “Ener gy-}
\put(52.22302,-242.7319){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}latenc y tradeof f for dynamic computation of floading in v ehicular fog}
\put(52.22302,-251.6989){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}computing, ” IEEE T r ansactions on V ehicular T ec hnolo gy , v ol. 69, no. 12,}
\put(52.22304,-260.6649){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}pp. 14 198–14 211, 2020.}
\put(33.96404,-269.6309){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[28]}
\put(47.24222,-269.6309){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22353,-269.6309){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}Q. Zhang, S. W ang, A. Zhou, and X. Ma, “Cost-a w are edge serv er}
\put(52.22304,-278.5979){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}placement, ” International J ournal of W eb and Grid Services , v ol. 18,}
\put(52.22305,-287.5639){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}no. 1, pp. 83–98, 2022.}
\put(33.96405,-296.5299){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[29]}
\put(47.24224,-296.5299){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22355,-296.5299){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}A. Asghari, H. Azgomi, A. A. Zoraghchian, and A. Barze g arinezhad,}
\put(52.22305,-305.4969){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}“Ener gy-a w are serv er placement in mobile edge computing using trees}
\put(52.22305,-314.4629){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}social relations optimization algorithm, ” The J ournal of Super computing ,}
\put(52.22302,-323.4289){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}v ol. 80, no. 5, pp. 6382–6410, 2024.}
\put(33.96402,-332.3959){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[30]}
\put(47.24221,-332.3959){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22352,-332.3959){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}X. Xu, B. Shen, X. Y in, M. R. Khosra vi, H. W u, L. Qi, and S. W an,}
\put(52.22302,-341.3619){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}“Edge serv er quantification and placement for of floading social media}
\put(52.22302,-350.3289){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}services in industrial cogniti v e IoV, ” IEEE T r ansactions on Industrial}
\put(52.22302,-359.2949){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Informatics , v ol. 17, no. 4, pp. 2910–2918, 2021.}
\put(33.96402,-368.2609){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[31]}
\put(47.24221,-368.2609){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22353,-368.2609){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}G. Cui, Q. He, F . Chen, H. Jin, and Y . Y ang, “T rading of f between}
\put(52.22302,-377.2279){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}user co v erage and netw ork rob ustness for edge serv er placement, ” IEEE}
\put(52.22304,-386.1939){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}T r ansactions on Cloud Computing , v ol. 10, no. 3, pp. 2178–2189, 2022.}
\put(33.96404,-395.1599){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[32]}
\put(47.24222,-395.1599){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22353,-395.1599){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}J. Zhao, Y . Huang, Q. Zhang, D. W ang, and W . Xu, “Edge serv er and}
\put(52.22304,-404.127){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}service deplo yment considering profit with im pro v ed PSO in IoV, ” IEEE}
\put(52.22304,-413.093){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}Systems J ournal , v ol. 19, no. 1, pp. 55–64, 2025.}
\put(33.96404,-422.06){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[33]}
\put(47.24222,-422.06){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22353,-422.06){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}J.-S. P an, Q. Y ang, S.-C. Chu, and H.-C. Chao, “ A joint edge serv er}
\put(52.22304,-431.026){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}and service deplo yment method in C-RAN with multilayer MEC for}
\put(52.22304,-439.992){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}multicommunities, ” IEEE Internet of Things J ournal , v ol. 12, no. 1, pp.}
\put(52.22305,-448.959){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}904–918, 2025.}
\put(33.96405,-457.925){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[34]}
\put(47.24224,-457.925){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22355,-457.925){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}R. W . Flo yd, “ Algorithm 97: Shortest path, ” Communications of the}
\put(52.22305,-466.891){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}A CM , v ol. 5, no. 6, pp. 345–345, 1962.}
\put(33.96405,-475.858){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[35]}
\put(47.24224,-475.858){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22355,-475.858){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}V . Joseph and B. Chapman, Deploying QoS for Cisco IP and ne xt}
\put(52.22305,-484.824){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}g ener ation networks: The definitive guide . Mor g an Kaufmann, 2009.}
\put(33.96405,-493.791){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}[36]}
\put(47.24224,-493.791){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}}
\put(52.22355,-493.791){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}K. P apagiannaki, S. Moon, C. Fraleigh, P . Thiran, and C. Diot, “Mea-}
\put(52.22305,-502.757){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}surement and analysis of single-hop delay on an IP backbone netw ork, ”}
\put(52.22305,-511.723){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{it}\selectfont\color{color_29791}IEEE J ournal on Selected Ar eas in Communications , v ol. 21, no. 6, pp.}
\put(52.22305,-520.6901){\fontsize{7.9701}{1}\usefont{T1}{cmr}{m}{n}\selectfont\color{color_29791}908–921, 2003.}
\end{picture}
\end{document}