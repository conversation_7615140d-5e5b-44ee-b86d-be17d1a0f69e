Comments to the Author
(1)The author has clearly provided details of the experimental setup, but the analysis of the experimental results remains insufficient. The BC-ESED problem is defined as a multi-objective problem, yet the author lacks comparisons with classic multi-objective optimization algorithms (such as NSGA-II, MOEA/D, etc.). This omission raises concerns about the validity and effectiveness of the proposed GA-PSO approach.

(2)Furthermore, Edge Server Expansion Deployment is a classic paradigm, and related work has already been conducted by other researchers, such as "Scaling up an Edge Server Deployment," 2020 IEEE International Conference on Pervasive Computing and Communications Workshops (PerCom Workshops), Austin, TX, USA, 2020, pp. 1-7, doi: 10.1109/PerComWorkshops48775.2020.9156204. However, the author claims, “As far as we know, there is no work focusing on the ESED paradigm.” Please provide a detailed explanation of how this paper differs from other similar studies.

"我已经恢复了plt.py文件，并详细分析了初始基站配置的含义。现在让我详细解释为什么不能直接使用maintest.py以及初始基站配置的重要性：\n\n## 📋 plt.py文件已恢复\n\n我已经恢复了被误删的plt.py文件，这是用于生成实验结果图表的原始脚本。非常抱歉误删了这个重要文件。\n\n## 🏗️ 初始基站配置详细解释\n\n### 什么是初始基站配置？\n\n初始基站配置 (`initial_basestations`) 是算法优化的**起始状态**，它定义了：\n\n1. **哪些基站ID已经预装了边缘服务器**\n2. **每个预装服务器的初始工作负载容量**\n\n### maintest.py中的真实配置\n\n```python\ninitial_basestations = {\n    12: 200000,   # 基站ID=12，预装容量200000的服务器\n    23: 100000,   # 基站ID=23，预装容量100000的服务器  \n    28: 200000,   # 基站ID=28，预装容量200000的服务器\n    39: 200000,   # 基站ID=39，预装容量200000的服务器\n    47: 300000,   # 基站ID=47，预装容量300000的服务器\n    61: 200000,   # 基站ID=61，预装容量200000的服务器\n    88: 100000,   # 基站ID=88，预装容量100000的服务器\n    125: 200000,  # 基站ID=125，预装容量200000的服务器\n    141: 200000,  # 基站ID=141，预装容量200000的服务器\n    168: 200000,  # 基站ID=168，预装容量200000的服务器\n    192: 200000,  # 基站ID=192，预装容量200000的服务器\n    204: 200000,  # 基站ID=204，预装容量200000的服务器\n    258: 200000,  # 基站ID=258，预装容量200000的服务器\n    289: 300000,  # 基站ID=289，预装容量300000的服务器\n    297: 200000   # 基站ID=297，预装容量200000的服务器\n}\n```\n\n### 为什么这个配置如此重要？\n\n1. **算法起始状态**：\n   - 所有优化算法都从这15个预装服务器开始\n   - 算法的任务是在预算约束下，决定在哪些其他基站增加服务器\n   - 不同的起始状态会导致完全不同的优化路径\n\n2. **地理分布影响**：\n   - 这15个基站的地理位置决定了初始的服务覆盖范围\n   - 影响延迟计算和工作负载分配\n   - 直接影响算法的搜索空间\n\n3. **容量配置影响**：\n   - 不同的初始容量影响工作负载标准差计算\n   - 影响适应度函数的计算结果\n\n## 🚫 为什么不能直接使用maintest.py？\n\n### 1. **实验设计不同**\n\n**maintest.py的设计**：\n```python\n# 参数网格搜索实验\nfor cross_proability in [0,0.2,0.4,0.6,0.8,1]:\n    for mutation_proability in [0,0.05,0.1,0.15,0.2]:\n        # 固定参数\n        rate = 1.0\n        budget = 30  \n        n = 450  # 只测试450个基站\n```\n\n**我们需要的实验**：\n```python\n# 基站数量影响实验\nfor n in [300, 350, 400, 450, 500, 550, 600]:\n    for seed in range(100, 150):  # 50次重复\n        # 固定参数\n        cross_proability = 0.8\n        mutation_proability = 0.1\n```\n\n### 2. **并行处理方式不同**\n\n**maintest.py**：\n- 使用`Pool(13)`创建13个进程\n- 所有算法同时运行\n- 会与现有的`run_bsnum_experiment.py`冲突\n\n**我们需要的方式**：\n- 只运行缺失的GA-PSO和NSGA-II实验\n- 避免与现有实验冲突\n- 独立的输出目录\n\n### 3. **输出格式不同**\n\n**maintest.py的输出格式**：\n```\nseed=100,N=450,t_max=100,budget=30,es_num=15,rate=1.0,cross_proability=0.8,mutation_proability=0.1 GA-PSO第1次的fitness:0.3456,delay:45.2,workload:1234\n```\n\n**run_bsnum_experiment.py的输出格式**：\n```\nseed=100,N=450,t_max=100,budget=30,rate=1.0 GA-PSO的fitness:0.3456,delay:45.2,workload:1234,runtime:123.45\n```\n\n### 4. **算法调用方式不同**\n\n**maintest.py**：\n```python\n# 使用固定参数调用\nproblem.place_server(n, budget, t_max, rate, initial_basestations, \n                    cross_proability, mutation_proability, seed)\n```\n\n**run_bsnum_experiment.py**：\n```python\n# 使用base_station_num参数调用\nproblem.place_server(base_station_num=n, budget=budget, t_max=t_max, \n                    rate=rate, initial_basestations=initial_basestations,\n                    cross_proability=cross_proability, \n                    mutation_proability=mutation_proability, seed=seed)\n```\n\n## 🔍 关键差异总结\n\n### 🔴 最严重的差异：初始基站配置\n\n**run_bsnum_experiment.py**：\n```python\n# 动态计算，基于基站数量比例\ninitial_basestations = {\n    int(n*0.1): 100000, int(n*0.25): 100000, int(n*0.4): 100000,\n    int(n*0.55): 100000, int(n*0.7): 100000, int(n*0.85): 100000\n}\n```\n\n**maintest.py**：\n```python\n# 固定的基站ID和容量\ninitial_basestations = {\n    12: 200000, 23: 100000, 28: 200000, 39: 200000, 47: 300000, \n    61: 200000, 88: 100000, 125: 200000, 141: 200000, 168: 200000, \n    192: 200000, 204: 200000, 258: 200000, 289: 300000, 297: 200000\n}\n```\n\n**影响**：这会导致完全不同的实验结果，无法进行有效对比。\n\n## 💡 解决方案\n\n我创建的`correct_parallel_resume.py`脚本：\n\n1. ✅ **使用maintest.py的固定初始基站配置**\n2. ✅ **适配run_bsnum_experiment.py的实验设计**\n3. ✅ **避免与现有实验冲突**\n4. ✅ **保持输出格式一致**\n5. ✅ **只运行缺失的高优先级实验**\n\n这样既保证了实验结果的一致性，又避免了直接使用maintest.py带来的冲突和格式问题。"