#!/usr/bin/env python3
"""
实验进度监控脚本
实时监控实验进度，显示完成情况和预估剩余时间
"""

import os
import time
import json
import argparse
from datetime import datetime, timedelta
from collections import defaultdict

def parse_result_file(file_path):
    """解析结果文件，返回实验数据"""
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if 'fitness:' in line:
                    # 解析实验参数
                    parts = line.strip().split()
                    params_str = parts[0]
                    
                    # 提取参数
                    params = {}
                    for param in params_str.split(','):
                        if '=' in param:
                            key, value = param.split('=', 1)
                            try:
                                params[key] = float(value) if '.' in value else int(value)
                            except:
                                params[key] = value
                    
                    # 提取结果
                    fitness = float(line.split('fitness:')[1].split(',')[0])
                    delay = float(line.split('delay:')[1].split(',')[0])
                    workload = float(line.split('workload:')[1].split(',')[0])
                    
                    runtime = 0
                    if 'runtime:' in line:
                        runtime = float(line.split('runtime:')[1].split(',')[0])
                    
                    results.append({
                        'params': params,
                        'fitness': fitness,
                        'delay': delay,
                        'workload': workload,
                        'runtime': runtime
                    })
    except Exception as e:
        print(f"解析文件错误 {file_path}: {e}")
    
    return results

def monitor_experiment(experiment_dir, experiment_type='bsnum'):
    """监控实验进度"""
    
    print(f"开始监控实验目录: {experiment_dir}")
    print(f"实验类型: {experiment_type}")
    print("按 Ctrl+C 停止监控\n")
    
    # 实验配置
    if experiment_type == 'bsnum':
        expected_params = {
            'base_station_numbers': [300, 350, 400, 450, 500, 550, 600],
            'algorithms': ['Top-K', 'Random', 'ACO', 'GA-PSO', 'PSO', 'NSGA-II', 'MOEA-D', 'DE'],
            'repetitions': 50
        }
        total_expected = len(expected_params['base_station_numbers']) * len(expected_params['algorithms']) * expected_params['repetitions']
    elif experiment_type == 'budget':
        expected_params = {
            'budgets': [15, 20, 25, 30, 35, 40, 45],
            'algorithms': ['Top-K', 'Random', 'ACO', 'GA-PSO', 'PSO', 'NSGA-II', 'MOEA-D', 'DE'],
            'repetitions': 50
        }
        total_expected = len(expected_params['budgets']) * len(expected_params['algorithms']) * expected_params['repetitions']
    else:
        total_expected = 0
    
    start_time = datetime.now()
    last_count = 0
    
    try:
        while True:
            # 清屏
            os.system('cls' if os.name == 'nt' else 'clear')
            
            current_time = datetime.now()
            elapsed_time = current_time - start_time
            
            print("=" * 80)
            print(f"GAPSO实验进度监控 - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"监控时间: {elapsed_time}")
            print("=" * 80)
            
            if not os.path.exists(experiment_dir):
                print(f"实验目录不存在: {experiment_dir}")
                time.sleep(10)
                continue
            
            # 统计结果文件
            algorithm_stats = defaultdict(lambda: defaultdict(int))
            total_experiments = 0
            total_runtime = 0
            
            for file in os.listdir(experiment_dir):
                if file.endswith('.txt') and not file.startswith('experiment_'):
                    algorithm = file.split('_')[0]
                    file_path = os.path.join(experiment_dir, file)
                    
                    results = parse_result_file(file_path)
                    
                    for result in results:
                        if experiment_type == 'bsnum':
                            key = result['params'].get('N', 'unknown')
                        elif experiment_type == 'budget':
                            key = result['params'].get('budget', 'unknown')
                        else:
                            key = 'all'
                        
                        algorithm_stats[algorithm][key] += 1
                        total_experiments += 1
                        total_runtime += result['runtime']
            
            # 显示总体进度
            if total_expected > 0:
                progress = total_experiments / total_expected * 100
                print(f"总体进度: {progress:.1f}% ({total_experiments}/{total_expected})")
                
                if total_experiments > last_count and total_experiments > 0:
                    experiments_per_hour = total_experiments / (elapsed_time.total_seconds() / 3600)
                    remaining_experiments = total_expected - total_experiments
                    if experiments_per_hour > 0:
                        eta_hours = remaining_experiments / experiments_per_hour
                        eta_time = current_time + timedelta(hours=eta_hours)
                        print(f"预计完成时间: {eta_time.strftime('%Y-%m-%d %H:%M:%S')} (剩余 {eta_hours:.1f} 小时)")
                
                print(f"平均运行时间: {total_runtime/total_experiments:.1f} 秒/实验" if total_experiments > 0 else "")
            else:
                print(f"总实验数: {total_experiments}")
            
            print("\n" + "-" * 80)
            print("各算法完成情况:")
            print("-" * 80)
            
            # 显示各算法进度
            if experiment_type == 'bsnum':
                header = f"{'算法':<12}"
                for n in expected_params['base_station_numbers']:
                    header += f"{n:>8}"
                header += f"{'总计':<8}{'进度':<8}"
                print(header)
                print("-" * len(header))
                
                for algorithm in expected_params['algorithms']:
                    row = f"{algorithm:<12}"
                    total_alg = 0
                    
                    for n in expected_params['base_station_numbers']:
                        count = algorithm_stats[algorithm][n]
                        total_alg += count
                        row += f"{count:>8}"
                    
                    expected_alg = len(expected_params['base_station_numbers']) * expected_params['repetitions']
                    progress_alg = total_alg / expected_alg * 100 if expected_alg > 0 else 0
                    row += f"{total_alg:<8}{progress_alg:>6.1f}%"
                    print(row)
            
            elif experiment_type == 'budget':
                header = f"{'算法':<12}"
                for budget in expected_params['budgets']:
                    header += f"{budget:>6}"
                header += f"{'总计':<8}{'进度':<8}"
                print(header)
                print("-" * len(header))
                
                for algorithm in expected_params['algorithms']:
                    row = f"{algorithm:<12}"
                    total_alg = 0
                    
                    for budget in expected_params['budgets']:
                        count = algorithm_stats[algorithm][budget]
                        total_alg += count
                        row += f"{count:>6}"
                    
                    expected_alg = len(expected_params['budgets']) * expected_params['repetitions']
                    progress_alg = total_alg / expected_alg * 100 if expected_alg > 0 else 0
                    row += f"{total_alg:<8}{progress_alg:>6.1f}%"
                    print(row)
            
            # 显示最近的实验结果
            print("\n" + "-" * 80)
            print("最近完成的实验 (最新5个):")
            print("-" * 80)
            
            recent_results = []
            for file in os.listdir(experiment_dir):
                if file.endswith('.txt') and not file.startswith('experiment_'):
                    file_path = os.path.join(experiment_dir, file)
                    results = parse_result_file(file_path)
                    
                    for result in results:
                        result['algorithm'] = file.split('_')[0]
                        recent_results.append(result)
            
            # 按时间排序（这里简单按顺序，实际可以按文件修改时间）
            recent_results = recent_results[-5:] if len(recent_results) >= 5 else recent_results
            
            for result in recent_results:
                if experiment_type == 'bsnum':
                    param_info = f"N={result['params'].get('N', '?')}"
                elif experiment_type == 'budget':
                    param_info = f"budget={result['params'].get('budget', '?')}"
                else:
                    param_info = "unknown"
                
                print(f"{result['algorithm']:<10} {param_info:<12} "
                      f"fitness={result['fitness']:<8.4f} "
                      f"delay={result['delay']:<8.2f} "
                      f"runtime={result['runtime']:<6.1f}s")
            
            print("\n" + "=" * 80)
            print("监控中... (按 Ctrl+C 停止)")
            
            last_count = total_experiments
            time.sleep(30)  # 每30秒更新一次
            
    except KeyboardInterrupt:
        print("\n监控已停止")
    except Exception as e:
        print(f"\n监控错误: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='GAPSO实验进度监控')
    parser.add_argument('--dir', '-d', default='experiments/bsnum', 
                       help='实验目录路径 (默认: experiments/bsnum)')
    parser.add_argument('--type', '-t', default='bsnum', 
                       choices=['bsnum', 'budget', 'workload'],
                       help='实验类型 (默认: bsnum)')
    
    args = parser.parse_args()
    
    monitor_experiment(args.dir, args.type)

if __name__ == "__main__":
    main()
