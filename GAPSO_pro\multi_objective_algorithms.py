"""
多目标优化算法实现模块
实现NSGA-II、MOEA/D、SPEA2等经典多目标优化算法用于边缘服务器部署问题
"""

import numpy as np
import random
import math
from copy import deepcopy
from typing import List, Tuple, Dict
from base_station import BaseStation
from edge_server import EdgeServer
from particle import Particle
from config import *

# 尝试导入多目标优化库
try:
    from pymoo.algorithms.moo.nsga2 import NSGA2
    from pymoo.algorithms.moo.moead import MOEAD
    from pymoo.algorithms.moo.nsga3 import NSGA3
    from pymoo.core.problem import Problem
    from pymoo.optimize import minimize
    from pymoo.core.variable import Real, Integer, Binary
    PYMOO_AVAILABLE = True
except ImportError:
    print("Warning: pymoo not installed. Install with: pip install pymoo")
    PYMOO_AVAILABLE = False

try:
    from deap import base, creator, tools, algorithms
    DEAP_AVAILABLE = True
except ImportError:
    print("Warning: deap not installed. Install with: pip install deap")
    DEAP_AVAILABLE = False


class EdgeServerDeploymentProblem(Problem):
    """
    边缘服务器部署问题的多目标优化问题定义
    使用pymoo框架
    """
    
    def __init__(self, base_stations: List[BaseStation], distance_topology: List[List[float]], 
                 budget: int, t_max: int, initial_basestations: Dict):
        self.base_stations = base_stations
        self.distance_topology = distance_topology
        self.budget = budget
        self.t_max = t_max
        self.initial_basestations = initial_basestations
        self.n = len(base_stations)
        
        # 定义决策变量：每个基站是否部署边缘服务器 + 服务器容量
        n_var = self.n * 2  # 位置变量 + 容量变量
        n_obj = 3  # 三个目标：延迟、工作负载均衡、成本
        n_constr = 0  # 暂时移除约束以支持MOEA/D

        # 变量边界：前n个为二进制（是否部署），后n个为连续（容量）
        xl = np.concatenate([np.zeros(self.n), np.zeros(self.n)])
        xu = np.concatenate([np.ones(self.n), np.full(self.n, single_max_workload)])

        super().__init__(n_var=n_var, n_obj=n_obj, n_constr=n_constr, xl=xl, xu=xu)
    
    def _evaluate(self, X, out, *args, **kwargs):
        """
        评估函数：计算目标函数值
        """
        n_solutions = X.shape[0]
        F = np.zeros((n_solutions, self.n_obj))

        for i in range(n_solutions):
            solution = X[i]

            # 解析决策变量
            deployment = solution[:self.n] > 0.5  # 二进制化
            capacities = solution[self.n:]

            # 计算目标函数
            delay, workload_std, cost = self._calculate_objectives(deployment, capacities)

            # 如果超出预算，给予惩罚
            if cost > self.budget:
                delay += 1000  # 延迟惩罚
                workload_std += 10000  # 工作负载惩罚
                cost += 1000  # 成本惩罚

            F[i, 0] = delay
            F[i, 1] = workload_std
            F[i, 2] = cost

        out["F"] = F
    
    def _calculate_objectives(self, deployment: np.ndarray, capacities: np.ndarray) -> Tuple[float, float, float]:
        """
        计算三个目标函数值
        """
        # 创建边缘服务器列表
        edge_servers = []
        total_cost = 0
        
        for i, is_deployed in enumerate(deployment):
            if is_deployed:
                bs = self.base_stations[i]
                capacity = max(capacities[i], 1000)  # 最小容量
                edge_server = EdgeServer(len(edge_servers), bs.latitude, bs.longitude, capacity, bs.id)
                edge_servers.append(edge_server)
                
                # 计算成本
                total_cost += fix_cost + install_cost + distance_to_cost * 0  # 简化成本计算
        
        if len(edge_servers) == 0:
            return float('inf'), float('inf'), float('inf')
        
        # 分配基站到边缘服务器
        assignments = self._assign_base_stations(edge_servers)
        
        # 计算延迟
        total_delay = 0
        delay_count = 0
        
        for bs_id, es_id in assignments.items():
            if es_id >= 0:  # 分配到边缘服务器
                es = edge_servers[es_id]
                delay = self._calculate_distance(self.base_stations[bs_id], es) * alpha + beta
                total_delay += delay
                delay_count += 1
            else:  # 分配到云端
                total_delay += remote_delay
                delay_count += 1
        
        avg_delay = total_delay / delay_count if delay_count > 0 else float('inf')
        
        # 计算工作负载标准差
        workloads = []
        for es in edge_servers:
            workload = sum(self.base_stations[bs_id].workload for bs_id, es_id in assignments.items() if es_id == es.id)
            workloads.append(workload)
        
        workload_std = np.std(workloads) if len(workloads) > 1 else 0
        
        return avg_delay, workload_std, total_cost
    
    def _assign_base_stations(self, edge_servers: List[EdgeServer]) -> Dict[int, int]:
        """
        将基站分配到最近的边缘服务器
        返回：{基站ID: 边缘服务器ID}，-1表示分配到云端
        """
        assignments = {}
        
        for bs_id, bs in enumerate(self.base_stations):
            min_distance = float('inf')
            best_es_id = -1  # -1表示云端
            
            for es_id, es in enumerate(edge_servers):
                distance = self._calculate_distance(bs, es)
                if distance < min_distance and distance <= self.t_max:
                    min_distance = distance
                    best_es_id = es_id
            
            assignments[bs_id] = best_es_id
        
        return assignments
    
    def _calculate_distance(self, bs: BaseStation, es: EdgeServer) -> float:
        """
        计算基站和边缘服务器之间的距离
        """
        # 使用Haversine公式计算地理距离
        lat1, lon1 = math.radians(bs.latitude), math.radians(bs.longitude)
        lat2, lon2 = math.radians(es.latitude), math.radians(es.longitude)
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371  # 地球半径（公里）
        
        return c * r


class NSGAIIServerPlacer:
    """
    基于NSGA-II算法的边缘服务器部署算法
    """
    
    def __init__(self, base_stations: List[BaseStation], distance_topology: List[List[float]]):
        self.base_stations = base_stations
        self.distance_topology = distance_topology
        self.edge_servers = None
        self.remote_cloud = []
    
    def place_server(self, base_station_num: int, budget: int, t_max: int, rate: float,
                    initial_basestations: Dict, cross_probability: float, mutation_probability: float, seed: int):
        """
        使用NSGA-II算法进行边缘服务器部署
        """
        if not PYMOO_AVAILABLE:
            raise ImportError("pymoo library is required for NSGA-II algorithm")
        
        random.seed(seed)
        np.random.seed(seed)
        
        # 缩放工作负载
        base_stations_scale = deepcopy(self.base_stations[:base_station_num])
        for bs in base_stations_scale:
            bs.workload = bs.workload * rate
        
        # 定义问题
        problem = EdgeServerDeploymentProblem(
            base_stations_scale, self.distance_topology, budget, t_max, initial_basestations
        )
        
        # 配置NSGA-II算法
        algorithm = NSGA2(
            pop_size=pop,
            n_offsprings=pop,
            eliminate_duplicates=True
        )
        
        # 运行优化
        res = minimize(
            problem,
            algorithm,
            ('n_gen', MAX_GENERATION),
            seed=seed,
            verbose=False
        )
        
        # 改进的解选择策略：使用加权和方法选择最优解
        if res.F is not None and len(res.F) > 0:
            # 方法1：使用加权和选择解（与传统算法一致的评价标准）
            normalized_F = res.F.copy()

            # 归一化目标函数值
            for i in range(res.F.shape[1]):
                f_min = np.min(res.F[:, i])
                f_max = np.max(res.F[:, i])
                if f_max > f_min:
                    normalized_F[:, i] = (res.F[:, i] - f_min) / (f_max - f_min)
                else:
                    normalized_F[:, i] = 0

            # 使用与传统算法相同的权重计算综合适应度
            weights = np.array([w1, w2, 0.5])  # 延迟、工作负载、成本权重
            weighted_scores = np.sum(normalized_F * weights, axis=1)
            best_idx = np.argmin(weighted_scores)

            # 方法2：也可以选择膝点解（knee point）作为备选
            # knee_idx = self._find_knee_point(res.F)

            best_solution = res.X[best_idx]

            # 解析解并构建边缘服务器
            self._build_solution(best_solution, base_stations_scale, budget, t_max)
        else:
            # 如果优化失败，使用随机解
            self._build_random_solution(base_stations_scale, budget, t_max, initial_basestations)
    
    def _build_solution(self, solution: np.ndarray, base_stations: List[BaseStation], 
                       budget: int, t_max: int):
        """
        根据优化解构建边缘服务器部署方案
        """
        n = len(base_stations)
        deployment = solution[:n] > 0.5
        capacities = solution[n:]
        
        self.edge_servers = []
        self.remote_cloud = [0] * n
        
        # 创建边缘服务器
        for i, is_deployed in enumerate(deployment):
            if is_deployed:
                bs = base_stations[i]
                capacity = max(capacities[i], 1000)
                edge_server = EdgeServer(len(self.edge_servers), bs.latitude, bs.longitude, capacity, bs.id)
                self.edge_servers.append(edge_server)
        
        # 分配基站
        for bs_id, bs in enumerate(base_stations):
            assigned = False
            min_distance = float('inf')
            best_es = None
            
            for es in self.edge_servers:
                distance = self._calculate_distance(bs, es)
                if distance <= t_max and distance < min_distance:
                    min_distance = distance
                    best_es = es
            
            if best_es is not None:
                best_es.assigned_base_stations.append(bs)
                best_es.workload += bs.workload
            else:
                self.remote_cloud[bs_id] = 1
    
    def _build_random_solution(self, base_stations: List[BaseStation], budget: int, 
                              t_max: int, initial_basestations: Dict):
        """
        构建随机解作为备选方案
        """
        # 简化实现：随机选择一些基站部署边缘服务器
        self.edge_servers = []
        self.remote_cloud = [0] * len(base_stations)
        
        # 随机选择部署位置
        n_servers = min(budget // 2, len(base_stations) // 10)  # 简化的服务器数量估算
        selected_indices = random.sample(range(len(base_stations)), n_servers)
        
        for i, idx in enumerate(selected_indices):
            bs = base_stations[idx]
            edge_server = EdgeServer(i, bs.latitude, bs.longitude, single_max_workload, bs.id)
            self.edge_servers.append(edge_server)
        
        # 简单分配
        for bs_id, bs in enumerate(base_stations):
            if self.edge_servers:
                # 分配到第一个边缘服务器
                self.edge_servers[0].assigned_base_stations.append(bs)
                self.edge_servers[0].workload += bs.workload
            else:
                self.remote_cloud[bs_id] = 1
    
    def _calculate_distance(self, bs: BaseStation, es: EdgeServer) -> float:
        """计算距离"""
        lat1, lon1 = math.radians(bs.latitude), math.radians(bs.longitude)
        lat2, lon2 = math.radians(es.latitude), math.radians(es.longitude)
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371
        
        return c * r
    
    # 添加与其他算法兼容的目标函数方法
    def objective_latency(self):
        """计算平均延迟"""
        if not self.edge_servers:
            return float('inf')
        
        total_delay = 0
        count = 0
        
        for es in self.edge_servers:
            for bs in es.assigned_base_stations:
                delay = self._calculate_distance(bs, es) * alpha + beta
                total_delay += delay
                count += 1
        
        # 添加云端延迟
        cloud_count = sum(self.remote_cloud)
        total_delay += cloud_count * remote_delay
        count += cloud_count
        
        return total_delay / count if count > 0 else float('inf')
    
    def objective_workload(self):
        """计算工作负载标准差"""
        if not self.edge_servers:
            return float('inf')
        
        workloads = [es.workload for es in self.edge_servers]
        return np.std(workloads) if len(workloads) > 1 else 0
    
    def objective_fitness(self):
        """计算综合适应度 - 与传统算法保持一致的计算方法"""
        if not self.edge_servers:
            return float('inf')

        # 计算延迟列表（与传统算法一致）
        delay_list = []
        for bs in self.base_stations:
            min_delay = float('inf')
            assigned_to_edge = False

            # 找到最近的边缘服务器
            for es in self.edge_servers:
                distance = self._calculate_distance(bs, es)
                if distance <= 100:  # t_max
                    delay = distance * alpha + beta
                    if delay < min_delay:
                        min_delay = delay
                        assigned_to_edge = True

            if assigned_to_edge:
                delay_list.append(min_delay)
            else:
                delay_list.append(remote_delay)  # 分配到云端

        # 计算工作负载列表
        workload_list = [es.workload / (es.max_workload / single_max_workload) for es in self.edge_servers]

        # 使用与传统算法相同的归一化方法
        if len(delay_list) > 1:
            max_delay = max(delay_list)
            min_delay = min(delay_list)
            if max_delay > min_delay:
                nor_delay = [(d - min_delay) / (max_delay - min_delay) for d in delay_list]
            else:
                nor_delay = [0] * len(delay_list)
        else:
            nor_delay = [0]

        if len(workload_list) > 1:
            max_workload = max(workload_list)
            if max_workload > 0:
                nor_workload = [w / max_workload for w in workload_list]
            else:
                nor_workload = [0] * len(workload_list)
        else:
            nor_workload = [0]

        # 计算标准差和平均值（与传统算法一致）
        w_std1 = np.std(nor_workload) if len(nor_workload) > 1 else 0
        d_avg = sum(delay_list) / len(delay_list) / t_max if delay_list else 0

        return d_avg + w_std1  # 与传统算法相同的组合方式
    
    def objective_cluster(self):
        """返回边缘服务器数量"""
        return len(self.edge_servers) if self.edge_servers else 0
    
    def objective_cluster_centers(self):
        """返回边缘服务器列表"""
        return self.edge_servers if self.edge_servers else []
    
    def objective_es_maxworkload(self):
        """返回最大工作负载"""
        if not self.edge_servers:
            return 0
        return max(es.workload for es in self.edge_servers)
    
    def objective_cloud(self):
        """返回云端分配列表"""
        return self.remote_cloud

    def _find_knee_point(self, objectives: np.ndarray) -> int:
        """
        找到Pareto前沿的膝点（knee point）
        膝点是在多目标权衡中最平衡的解
        """
        if len(objectives) == 0:
            return 0

        # 归一化目标函数值
        normalized_obj = objectives.copy()
        for i in range(objectives.shape[1]):
            f_min = np.min(objectives[:, i])
            f_max = np.max(objectives[:, i])
            if f_max > f_min:
                normalized_obj[:, i] = (objectives[:, i] - f_min) / (f_max - f_min)

        # 计算到理想点(0,0,0)的距离
        distances = np.sqrt(np.sum(normalized_obj**2, axis=1))

        # 返回距离最小的解的索引
        return np.argmin(distances)


class MOEADServerPlacer:
    """
    基于MOEA/D算法的边缘服务器部署算法
    """

    def __init__(self, base_stations: List[BaseStation], distance_topology: List[List[float]]):
        self.base_stations = base_stations
        self.distance_topology = distance_topology
        self.edge_servers = None
        self.remote_cloud = []

    def place_server(self, base_station_num: int, budget: int, t_max: int, rate: float,
                    initial_basestations: Dict, cross_probability: float, mutation_probability: float, seed: int):
        """
        使用MOEA/D算法进行边缘服务器部署
        """
        if not PYMOO_AVAILABLE:
            raise ImportError("pymoo library is required for MOEA/D algorithm")

        random.seed(seed)
        np.random.seed(seed)

        # 缩放工作负载
        base_stations_scale = deepcopy(self.base_stations[:base_station_num])
        for bs in base_stations_scale:
            bs.workload = bs.workload * rate

        # 定义问题
        problem = EdgeServerDeploymentProblem(
            base_stations_scale, self.distance_topology, budget, t_max, initial_basestations
        )

        # 配置MOEA/D算法
        from pymoo.util.ref_dirs import get_reference_directions
        from pymoo.decomposition.pbi import PBI
        ref_dirs = get_reference_directions("das-dennis", problem.n_obj, n_partitions=12)

        algorithm = MOEAD(
            ref_dirs=ref_dirs,
            n_neighbors=20,
            decomposition=PBI(),
            prob_neighbor_mating=0.7
        )

        # 运行优化
        res = minimize(
            problem,
            algorithm,
            ('n_gen', MAX_GENERATION),
            seed=seed,
            verbose=False
        )

        # 选择最优解
        if res.F is not None and len(res.F) > 0:
            # 使用加权和方法选择解
            weights = np.array([w1, w2, w3])
            weighted_scores = np.sum(res.F * weights, axis=1)
            best_idx = np.argmin(weighted_scores)
            best_solution = res.X[best_idx]

            self._build_solution(best_solution, base_stations_scale, budget, t_max)
        else:
            self._build_random_solution(base_stations_scale, budget, t_max, initial_basestations)

    def _build_solution(self, solution: np.ndarray, base_stations: List[BaseStation],
                       budget: int, t_max: int):
        """根据优化解构建边缘服务器部署方案"""
        # 复用NSGAIIServerPlacer的实现
        nsga_placer = NSGAIIServerPlacer(base_stations, self.distance_topology)
        nsga_placer._build_solution(solution, base_stations, budget, t_max)
        self.edge_servers = nsga_placer.edge_servers
        self.remote_cloud = nsga_placer.remote_cloud

    def _build_random_solution(self, base_stations: List[BaseStation], budget: int,
                              t_max: int, initial_basestations: Dict):
        """构建随机解作为备选方案"""
        nsga_placer = NSGAIIServerPlacer(base_stations, self.distance_topology)
        nsga_placer._build_random_solution(base_stations, budget, t_max, initial_basestations)
        self.edge_servers = nsga_placer.edge_servers
        self.remote_cloud = nsga_placer.remote_cloud

    # 复用目标函数方法
    def objective_latency(self):
        nsga_placer = NSGAIIServerPlacer(self.base_stations, self.distance_topology)
        nsga_placer.edge_servers = self.edge_servers
        nsga_placer.remote_cloud = self.remote_cloud
        return nsga_placer.objective_latency()

    def objective_workload(self):
        nsga_placer = NSGAIIServerPlacer(self.base_stations, self.distance_topology)
        nsga_placer.edge_servers = self.edge_servers
        return nsga_placer.objective_workload()

    def objective_fitness(self):
        nsga_placer = NSGAIIServerPlacer(self.base_stations, self.distance_topology)
        nsga_placer.edge_servers = self.edge_servers
        nsga_placer.remote_cloud = self.remote_cloud
        return nsga_placer.objective_fitness()

    def objective_cluster(self):
        return len(self.edge_servers) if self.edge_servers else 0

    def objective_cluster_centers(self):
        return self.edge_servers if self.edge_servers else []

    def objective_es_maxworkload(self):
        if not self.edge_servers:
            return 0
        return max(es.workload for es in self.edge_servers)

    def objective_cloud(self):
        return self.remote_cloud


class DifferentialEvolutionServerPlacer:
    """
    基于差分进化算法的边缘服务器部署算法
    """

    def __init__(self, base_stations: List[BaseStation], distance_topology: List[List[float]]):
        self.base_stations = base_stations
        self.distance_topology = distance_topology
        self.edge_servers = None
        self.remote_cloud = []

    def place_server(self, base_station_num: int, budget: int, t_max: int, rate: float,
                    initial_basestations: Dict, cross_probability: float, mutation_probability: float, seed: int):
        """
        使用差分进化算法进行边缘服务器部署
        """
        random.seed(seed)
        np.random.seed(seed)

        # 缩放工作负载
        base_stations_scale = deepcopy(self.base_stations[:base_station_num])
        for bs in base_stations_scale:
            bs.workload = bs.workload * rate

        self.base_station_num = base_station_num
        self.budget = budget
        self.t_max = t_max
        self.base_stations_scale = base_stations_scale

        # DE参数
        pop_size = pop
        F = 0.5  # 缩放因子
        CR = cross_probability  # 交叉概率

        # 初始化种群
        population = self._initialize_population(pop_size, base_station_num)
        fitness = [self._evaluate_solution(ind) for ind in population]

        best_fitness = float('inf')
        best_solution = None

        # DE主循环
        for generation in range(MAX_GENERATION):
            for i in range(pop_size):
                # 变异操作
                mutant = self._mutate(population, i, F)

                # 交叉操作
                trial = self._crossover(population[i], mutant, CR)

                # 边界处理
                trial = self._repair_solution(trial)

                # 选择操作
                trial_fitness = self._evaluate_solution(trial)
                if trial_fitness < fitness[i]:
                    population[i] = trial
                    fitness[i] = trial_fitness

                    if trial_fitness < best_fitness:
                        best_fitness = trial_fitness
                        best_solution = trial.copy()

            if generation % 50 == 0:
                print(f"DE Generation {generation}, Best Fitness: {best_fitness}")

        # 构建最优解
        if best_solution is not None:
            self._build_solution_from_vector(best_solution, base_stations_scale, budget, t_max)
        else:
            self._build_random_solution(base_stations_scale, budget, t_max, initial_basestations)

    def _initialize_population(self, pop_size: int, n: int) -> List[np.ndarray]:
        """初始化种群"""
        population = []
        for _ in range(pop_size):
            # 每个个体包含部署决策和容量决策
            deployment = np.random.rand(n) > 0.8  # 20%概率部署
            capacities = np.random.uniform(1000, single_max_workload, n)
            individual = np.concatenate([deployment.astype(float), capacities])
            population.append(individual)
        return population

    def _mutate(self, population: List[np.ndarray], current_idx: int, F: float) -> np.ndarray:
        """变异操作"""
        pop_size = len(population)
        indices = list(range(pop_size))
        indices.remove(current_idx)

        # 随机选择三个不同的个体
        r1, r2, r3 = random.sample(indices, 3)

        # DE/rand/1变异
        mutant = population[r1] + F * (population[r2] - population[r3])
        return mutant

    def _crossover(self, target: np.ndarray, mutant: np.ndarray, CR: float) -> np.ndarray:
        """交叉操作"""
        trial = target.copy()
        n = len(target)

        # 确保至少有一个基因来自变异向量
        j_rand = random.randint(0, n - 1)

        for j in range(n):
            if random.random() < CR or j == j_rand:
                trial[j] = mutant[j]

        return trial

    def _repair_solution(self, solution: np.ndarray) -> np.ndarray:
        """修复解的边界"""
        n = len(solution) // 2

        # 修复部署决策（0-1之间）
        solution[:n] = np.clip(solution[:n], 0, 1)

        # 修复容量决策
        solution[n:] = np.clip(solution[n:], 1000, single_max_workload)

        return solution

    def _evaluate_solution(self, solution: np.ndarray) -> float:
        """评估解的适应度"""
        n = len(solution) // 2
        deployment = solution[:n] > 0.5
        capacities = solution[n:]

        # 检查是否有部署的服务器
        if not np.any(deployment):
            return float('inf')

        # 计算成本约束
        cost = np.sum(deployment) * (fix_cost + install_cost)
        if cost > self.budget:
            return float('inf')

        # 计算目标函数
        delay, workload_std = self._calculate_objectives_simple(deployment, capacities)

        # 组合目标函数
        return w1 * delay / 100.0 + w2 * workload_std / 10000.0

    def _calculate_objectives_simple(self, deployment: np.ndarray, capacities: np.ndarray) -> Tuple[float, float]:
        """简化的目标函数计算"""
        # 创建边缘服务器
        edge_servers = []
        for i, is_deployed in enumerate(deployment):
            if is_deployed:
                bs = self.base_stations_scale[i]
                capacity = capacities[i]
                edge_servers.append((i, bs, capacity))

        if not edge_servers:
            return float('inf'), float('inf')

        # 计算平均延迟
        total_delay = 0
        workloads = [0] * len(edge_servers)

        for bs_id, bs in enumerate(self.base_stations_scale):
            # 找到最近的边缘服务器
            min_distance = float('inf')
            best_es_idx = -1

            for es_idx, (es_bs_id, es_bs, capacity) in enumerate(edge_servers):
                distance = self._calculate_distance_simple(bs, es_bs)
                if distance <= self.t_max and distance < min_distance:
                    min_distance = distance
                    best_es_idx = es_idx

            if best_es_idx >= 0:
                delay = min_distance * alpha + beta
                total_delay += delay
                workloads[best_es_idx] += bs.workload
            else:
                total_delay += remote_delay

        avg_delay = total_delay / len(self.base_stations_scale)
        workload_std = np.std(workloads) if len(workloads) > 1 else 0

        return avg_delay, workload_std

    def _calculate_distance_simple(self, bs1: BaseStation, bs2: BaseStation) -> float:
        """简化的距离计算"""
        lat1, lon1 = math.radians(bs1.latitude), math.radians(bs1.longitude)
        lat2, lon2 = math.radians(bs2.latitude), math.radians(bs2.longitude)

        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371

        return c * r

    def _build_solution_from_vector(self, solution: np.ndarray, base_stations: List[BaseStation],
                                   budget: int, t_max: int):
        """从解向量构建边缘服务器部署方案"""
        n = len(solution) // 2
        deployment = solution[:n] > 0.5
        capacities = solution[n:]

        self.edge_servers = []
        self.remote_cloud = [0] * n

        # 创建边缘服务器
        for i, is_deployed in enumerate(deployment):
            if is_deployed:
                bs = base_stations[i]
                capacity = max(capacities[i], 1000)
                edge_server = EdgeServer(len(self.edge_servers), bs.latitude, bs.longitude, capacity, bs.id)
                self.edge_servers.append(edge_server)

        # 分配基站
        for bs_id, bs in enumerate(base_stations):
            min_distance = float('inf')
            best_es = None

            for es in self.edge_servers:
                distance = self._calculate_distance_simple(bs, base_stations[es.base_station_id])
                if distance <= t_max and distance < min_distance:
                    min_distance = distance
                    best_es = es

            if best_es is not None:
                best_es.assigned_base_stations.append(bs)
                best_es.workload += bs.workload
            else:
                self.remote_cloud[bs_id] = 1

    def _build_random_solution(self, base_stations: List[BaseStation], budget: int,
                              t_max: int, initial_basestations: Dict):
        """构建随机解"""
        nsga_placer = NSGAIIServerPlacer(base_stations, self.distance_topology)
        nsga_placer._build_random_solution(base_stations, budget, t_max, initial_basestations)
        self.edge_servers = nsga_placer.edge_servers
        self.remote_cloud = nsga_placer.remote_cloud

    # 复用目标函数方法
    def objective_latency(self):
        nsga_placer = NSGAIIServerPlacer(self.base_stations, self.distance_topology)
        nsga_placer.edge_servers = self.edge_servers
        nsga_placer.remote_cloud = self.remote_cloud
        return nsga_placer.objective_latency()

    def objective_workload(self):
        nsga_placer = NSGAIIServerPlacer(self.base_stations, self.distance_topology)
        nsga_placer.edge_servers = self.edge_servers
        return nsga_placer.objective_workload()

    def objective_fitness(self):
        nsga_placer = NSGAIIServerPlacer(self.base_stations, self.distance_topology)
        nsga_placer.edge_servers = self.edge_servers
        nsga_placer.remote_cloud = self.remote_cloud
        return nsga_placer.objective_fitness()

    def objective_cluster(self):
        return len(self.edge_servers) if self.edge_servers else 0

    def objective_cluster_centers(self):
        return self.edge_servers if self.edge_servers else []

    def objective_es_maxworkload(self):
        if not self.edge_servers:
            return 0
        return max(es.workload for es in self.edge_servers)

    def objective_cloud(self):
        return self.remote_cloud
