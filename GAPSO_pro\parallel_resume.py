#!/usr/bin/env python3
"""
并行恢复实验脚本
与正在运行的实验并行执行，避免冲突
"""

import os
import re
import time
import logging
from datetime import datetime
from multiprocessing import Pool
from collections import defaultdict

def setup_logging():
    """配置日志系统"""
    log_dir = 'logs'
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'{log_dir}/parallel_resume.log'),
            logging.StreamHandler()
        ]
    )

def get_missing_high_priority():
    """
    获取高优先级缺失实验
    基于之前的分析结果
    """
    
    # 根据之前的分析，这些是高优先级缺失的实验
    high_priority_missing = [
        # GA-PSO N=400: 缺失12次 (seed 138-149)
        *[{'algorithm': 'GA-PSO', 'n': 400, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(138, 150)],
        
        # GA-PSO N=450: 缺失50次 (seed 100-149)
        *[{'algorithm': 'GA-PSO', 'n': 450, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(100, 150)],
        
        # GA-PSO N=500: 缺失50次 (seed 100-149)
        *[{'algorithm': 'GA-PSO', 'n': 500, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(100, 150)],
        
        # NSGA-II N=400: 缺失36次 (seed 114-149)
        *[{'algorithm': 'NSGA-II', 'n': 400, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(114, 150)],
        
        # NSGA-II N=450: 缺失50次 (seed 100-149)
        *[{'algorithm': 'NSGA-II', 'n': 450, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(100, 150)],
        
        # NSGA-II N=500: 缺失50次 (seed 100-149)
        *[{'algorithm': 'NSGA-II', 'n': 500, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(100, 150)],
    ]
    
    return high_priority_missing

def run_single_experiment_parallel(params):
    """
    运行单次实验（并行版本）
    """
    algorithm_name = params['algorithm']
    n = params['n']
    seed = params['seed']
    budget = params['budget']
    rate = params['rate']
    
    try:
        # 导入必要模块
        from utils import DataUtils
        from generate_topology import generate_topology
        from algorithm import (
            TopKServerPlacer, RandomServerPlacer, ACOServerPlacer, 
            GAPSOServerPlacer, PSOServerPlacer
        )
        from multi_objective_algorithms import (
            NSGAIIServerPlacer, MOEADServerPlacer, DifferentialEvolutionServerPlacer
        )
        
        # 加载数据
        data = DataUtils('./dataset/basestations.csv', './dataset/userdata.csv')
        base_stations = data.base_stations
        delay_matrix = generate_topology(data, n)
        
        # 初始基站配置
        initial_basestations = {
            int(n*0.1): 100000, int(n*0.3): 100000, int(n*0.5): 100000,
            int(n*0.7): 100000, int(n*0.9): 100000
        }
        
        # 选择算法
        algorithms = {
            'GA-PSO': GAPSOServerPlacer,
            'NSGA-II': NSGAIIServerPlacer,
        }
        
        if algorithm_name not in algorithms:
            return None
            
        algorithm = algorithms[algorithm_name](base_stations[:n], delay_matrix)
        
        # 优化参数设置
        if algorithm_name == 'NSGA-II':
            max_gen = 150
            pop_size = 20
        else:  # GA-PSO
            max_gen = 200
            pop_size = 20
        
        # 运行算法
        start_time = time.time()
        
        if algorithm_name == 'NSGA-II':
            algorithm.place_server(
                base_station_num=n,
                budget=budget,
                t_max=100,
                rate=rate,
                initial_basestations=initial_basestations,
                cross_probability=0.8,
                mutation_probability=0.1,
                seed=seed
            )
        else:  # GA-PSO
            algorithm.place_server(
                base_station_num=n,
                budget=budget,
                t_max=100,
                rate=rate,
                initial_basestations=initial_basestations,
                cross_proability=0.8,
                mutation_proability=0.1,
                seed=seed
            )
        
        end_time = time.time()
        runtime = end_time - start_time
        
        # 计算结果
        if hasattr(algorithm, 'edge_servers') and algorithm.edge_servers:
            if algorithm_name == 'NSGA-II':
                delay = algorithm.objective_latency()
                workload = algorithm.objective_workload()
                fitness = algorithm.objective_fitness()
            else:  # GA-PSO
                delay_result = algorithm.objective_latency()
                delay = delay_result[0] if isinstance(delay_result, tuple) else delay_result
                
                workload_result = algorithm.objective_workload()
                workload = workload_result[0] if isinstance(workload_result, tuple) else workload_result
                
                fitness_result = algorithm.objective_fitness()
                fitness = fitness_result[0] if isinstance(fitness_result, tuple) else fitness_result
            
            # 创建独立的输出目录，避免冲突
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_dir = f'experiments/parallel_{timestamp[:8]}'
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存结果到独立目录
            result_file = f'{output_dir}/{algorithm_name}_N{n}.txt'
            result_line = f"seed={seed},N={n},t_max=100,budget={budget},rate={rate} {algorithm_name}的fitness:{fitness},delay:{delay},workload:{workload},runtime:{runtime:.2f}\n"
            
            with open(result_file, 'a', encoding='utf-8') as f:
                f.write(result_line)
            
            logging.info(f"✓ 完成: {algorithm_name}, N={n}, seed={seed}, fitness={fitness:.4f}, 用时={runtime:.1f}s")
            
            return {
                'algorithm': algorithm_name,
                'n': n,
                'seed': seed,
                'fitness': fitness,
                'delay': delay,
                'workload': workload,
                'runtime': runtime,
                'output_file': result_file
            }
        else:
            logging.error(f"✗ 失败: {algorithm_name}, N={n}, seed={seed} - 无边缘服务器")
            return None
            
    except Exception as e:
        logging.error(f"✗ 错误: {algorithm_name}, N={n}, seed={seed} - {str(e)}")
        return None

def run_parallel_experiment(max_workers=3):
    """
    运行并行实验
    """
    setup_logging()
    
    print("🚀 并行恢复实验 - 高优先级")
    print("=" * 50)
    
    # 获取高优先级缺失实验
    experiments = get_missing_high_priority()
    
    print(f"📋 高优先级实验数量: {len(experiments)}")
    print(f"🎯 主要目标: GA-PSO vs NSGA-II 在 N=400,450,500 的对比")
    
    # 显示实验分布
    exp_count = defaultdict(lambda: defaultdict(int))
    for exp in experiments:
        exp_count[exp['algorithm']][exp['n']] += 1
    
    print("\n📊 实验分布:")
    for algo in ['GA-PSO', 'NSGA-II']:
        print(f"  {algo}:")
        for n in [400, 450, 500]:
            count = exp_count[algo][n]
            print(f"    N={n}: {count} 次实验")
    
    # 估算时间
    avg_time = 5  # 分钟（优化后）
    estimated_hours = (len(experiments) * avg_time) / (60 * max_workers)
    print(f"\n⏰ 预计完成时间: {estimated_hours:.1f} 小时")
    print(f"🔧 使用 {max_workers} 个并行进程（避免与现有实验冲突）")
    
    # 自动开始执行
    print("\n🚀 自动开始执行并行实验...")
    
    # 运行实验
    print(f"\n🚀 开始并行实验...")
    start_time = time.time()
    
    try:
        with Pool(processes=max_workers) as pool:
            results = pool.map(run_single_experiment_parallel, experiments)
            successful_results = [r for r in results if r is not None]
    except KeyboardInterrupt:
        logging.info("实验被用户中断")
        return
    except Exception as e:
        logging.error(f"并行执行错误: {str(e)}")
        return
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 统计结果
    success_rate = len(successful_results) / len(experiments) * 100
    
    print(f"\n✅ 并行实验完成!")
    print(f"⏱️  总时间: {total_time/3600:.2f} 小时")
    print(f"📊 成功率: {success_rate:.1f}% ({len(successful_results)}/{len(experiments)})")
    
    # 显示输出文件位置
    if successful_results:
        output_dirs = set(os.path.dirname(r['output_file']) for r in successful_results)
        print(f"\n📁 结果保存在:")
        for dir_path in output_dirs:
            print(f"   {dir_path}")
        
        print(f"\n💡 后续步骤:")
        print(f"1. 等待原实验完成")
        print(f"2. 将并行实验结果合并到主目录:")
        print(f"   python merge_results.py")
        print(f"3. 生成对比图表:")
        print(f"   python plt_multi_objective.py")
    
    return successful_results

def create_merge_script():
    """
    创建结果合并脚本
    """
    merge_script = '''#!/usr/bin/env python3
"""
合并并行实验结果到主目录
"""

import os
import shutil
import glob

def merge_parallel_results():
    """合并并行实验结果"""
    
    # 查找并行实验目录
    parallel_dirs = glob.glob('experiments/parallel_*')
    
    if not parallel_dirs:
        print("没有找到并行实验结果")
        return
    
    print(f"找到 {len(parallel_dirs)} 个并行实验目录")
    
    # 合并结果
    for parallel_dir in parallel_dirs:
        print(f"\\n处理目录: {parallel_dir}")
        
        for filename in os.listdir(parallel_dir):
            if filename.endswith('.txt'):
                src_file = os.path.join(parallel_dir, filename)
                dst_file = os.path.join('experiments/bsnum', filename)
                
                # 读取源文件内容
                with open(src_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 追加到目标文件
                with open(dst_file, 'a', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"  合并: {filename}")
        
        # 备份并删除并行目录
        backup_dir = f"{parallel_dir}_merged"
        shutil.move(parallel_dir, backup_dir)
        print(f"  备份到: {backup_dir}")
    
    print("\\n✅ 合并完成！")

if __name__ == "__main__":
    merge_parallel_results()
'''
    
    with open('merge_results.py', 'w', encoding='utf-8') as f:
        f.write(merge_script)
    
    print("📝 已创建结果合并脚本: merge_results.py")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='并行运行高优先级实验')
    parser.add_argument('--workers', type=int, default=3, help='并行进程数')
    parser.add_argument('--create-merge', action='store_true', help='创建合并脚本')
    
    args = parser.parse_args()
    
    if args.create_merge:
        create_merge_script()
    else:
        run_parallel_experiment(args.workers)
