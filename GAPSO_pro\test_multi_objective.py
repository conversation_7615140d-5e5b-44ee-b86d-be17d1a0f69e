"""
多目标优化算法测试脚本
用于验证新实现的算法是否正常工作
"""

import sys
import traceback
from utils import DataUtils
from generate_topology import generate_topology
from config import *

def test_algorithm(algorithm_class, algorithm_name):
    """
    测试单个算法
    """
    print(f"\n=== Testing {algorithm_name} ===")
    
    try:
        # 加载数据
        data = DataUtils('./dataset/basestations.csv', './dataset/userdata.csv')
        base_stations = data.base_stations
        
        # 生成拓扑
        n = 50  # 使用较小的数据集进行测试
        delay_matrix = generate_topology(data, n)
        
        # 创建算法实例
        algorithm = algorithm_class(base_stations[:n], delay_matrix)
        
        # 设置测试参数
        budget = 5
        t_max = 50
        rate = 1.0
        initial_basestations = {2: 50000, 8: 50000}  # 减少初始服务器数量
        cross_probability = 0.8
        mutation_probability = 0.1
        seed = 100

        print(f"Running {algorithm_name} with {n} base stations...")

        # 运行算法 - 处理不同的参数名称
        if algorithm_name in ['NSGA-II', 'MOEA/D', 'Differential Evolution']:
            # 多目标算法使用正确拼写
            algorithm.place_server(
                base_station_num=n,
                budget=budget,
                t_max=t_max,
                rate=rate,
                initial_basestations=initial_basestations,
                cross_probability=cross_probability,
                mutation_probability=mutation_probability,
                seed=seed
            )
        else:
            # 传统算法使用原有拼写错误的参数名
            algorithm.place_server(
                base_station_num=n,
                budget=budget,
                t_max=t_max,
                rate=rate,
                initial_basestations=initial_basestations,
                cross_proability=cross_probability,  # 注意拼写错误
                mutation_proability=mutation_probability,  # 注意拼写错误
                seed=seed
            )
        
        # 检查结果
        if hasattr(algorithm, 'edge_servers') and algorithm.edge_servers is not None:
            print(f"✓ {algorithm_name} completed successfully")
            print(f"  - Edge servers deployed: {len(algorithm.edge_servers)}")
            
            # 计算目标函数
            try:
                if algorithm_name in ['NSGA-II', 'MOEA/D', 'Differential Evolution']:
                    # 多目标算法返回单一数值
                    delay = algorithm.objective_latency()
                    workload = algorithm.objective_workload()
                    fitness = algorithm.objective_fitness()
                else:
                    # 传统算法可能返回元组
                    delay_result = algorithm.objective_latency()
                    if isinstance(delay_result, tuple):
                        delay = delay_result[0]
                    else:
                        delay = delay_result

                    workload_result = algorithm.objective_workload()
                    if isinstance(workload_result, tuple):
                        workload = workload_result[0]
                    else:
                        workload = workload_result

                    fitness_result = algorithm.objective_fitness()
                    if isinstance(fitness_result, tuple):
                        fitness = fitness_result[0]
                    else:
                        fitness = fitness_result

                print(f"  - Average delay: {delay:.2f} ms")
                print(f"  - Workload std: {workload:.2f}")
                print(f"  - Fitness: {fitness:.4f}")

                return True
            except Exception as e:
                print(f"✗ Error calculating objectives: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print(f"✗ {algorithm_name} failed - no edge servers deployed")
            return False
            
    except ImportError as e:
        print(f"✗ {algorithm_name} failed - missing dependencies: {e}")
        print("  Please run: python install_dependencies.py")
        return False
    except Exception as e:
        print(f"✗ {algorithm_name} failed with error: {e}")
        print("  Traceback:")
        traceback.print_exc()
        return False

def test_traditional_algorithms():
    """
    测试传统算法作为对照
    """
    print("=== Testing Traditional Algorithms ===")
    
    try:
        from algorithm import TopKServerPlacer, RandomServerPlacer, PSOServerPlacer
        
        algorithms = [
            (TopKServerPlacer, "TopFirst"),
            (RandomServerPlacer, "Random"),
            (PSOServerPlacer, "PSO")
        ]
        
        success_count = 0
        for alg_class, alg_name in algorithms:
            if test_algorithm(alg_class, alg_name):
                success_count += 1
        
        print(f"\nTraditional algorithms test result: {success_count}/{len(algorithms)} passed")
        return success_count == len(algorithms)
        
    except Exception as e:
        print(f"Error testing traditional algorithms: {e}")
        return False

def test_multi_objective_algorithms():
    """
    测试多目标优化算法
    """
    print("=== Testing Multi-Objective Algorithms ===")
    
    try:
        from multi_objective_algorithms import NSGAIIServerPlacer, MOEADServerPlacer, DifferentialEvolutionServerPlacer
        
        algorithms = [
            (NSGAIIServerPlacer, "NSGA-II"),
            (MOEADServerPlacer, "MOEA/D"),
            (DifferentialEvolutionServerPlacer, "Differential Evolution")
        ]
        
        success_count = 0
        for alg_class, alg_name in algorithms:
            if test_algorithm(alg_class, alg_name):
                success_count += 1
        
        print(f"\nMulti-objective algorithms test result: {success_count}/{len(algorithms)} passed")
        return success_count == len(algorithms)
        
    except ImportError as e:
        print(f"Cannot import multi-objective algorithms: {e}")
        print("Please ensure multi_objective_algorithms.py is in the correct location")
        return False
    except Exception as e:
        print(f"Error testing multi-objective algorithms: {e}")
        return False

def test_plotting():
    """
    测试绘图功能
    """
    print("\n=== Testing Plotting Functions ===")
    
    try:
        import matplotlib.pyplot as plt
        import numpy as np
        
        # 测试基本绘图
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        
        plt.figure(figsize=(8, 6))
        plt.plot(x, y)
        plt.title("Test Plot")
        plt.xlabel("X")
        plt.ylabel("Y")
        plt.savefig('test_plot.png')
        plt.close()
        
        print("✓ Basic plotting works")
        
        # 测试多目标绘图脚本
        try:
            from plt_multi_objective import plot_algorithm_ranking
            print("✓ Multi-objective plotting module imported successfully")
            return True
        except ImportError as e:
            print(f"✗ Cannot import multi-objective plotting: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Plotting test failed: {e}")
        return False

def run_comprehensive_test():
    """
    运行综合测试
    """
    print("=" * 60)
    print("GAPSO Multi-Objective Algorithms Comprehensive Test")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 传统算法
    test_results.append(("Traditional Algorithms", test_traditional_algorithms()))
    
    # 测试2: 多目标算法
    test_results.append(("Multi-Objective Algorithms", test_multi_objective_algorithms()))
    
    # 测试3: 绘图功能
    test_results.append(("Plotting Functions", test_plotting()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<30} {status}")
        if result:
            passed_tests += 1
    
    print("-" * 60)
    print(f"Overall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! The system is ready for experiments.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
        print("\nTroubleshooting tips:")
        print("1. Run: python install_dependencies.py")
        print("2. Check if all required files are in place")
        print("3. Verify dataset files exist in ./dataset/")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
