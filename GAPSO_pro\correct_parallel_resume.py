#!/usr/bin/env python3
"""
基于maintest.py的正确并行恢复实验脚本
确保与原始实验完全一致的配置
"""

import os
import time
import logging
from datetime import datetime
from multiprocessing import Pool
from collections import defaultdict

# 导入原始配置
from config import *
from utils import DataUtils
from generate_topology import generate_topology
from algorithm import *
from multi_objective_algorithms import NSGAIIServerPlacer, MOEADServerPlacer, DifferentialEvolutionServerPlacer

def setup_logging():
    """配置日志系统"""
    log_dir = 'logs'
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'{log_dir}/correct_parallel_resume.log'),
            logging.StreamHandler()
        ]
    )

def get_missing_high_priority():
    """
    获取高优先级缺失实验
    基于之前的分析结果
    """
    
    high_priority_missing = [
        # GA-PSO N=400: 缺失12次 (seed 138-149)
        *[{'algorithm': 'GA-PSO', 'n': 400, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(138, 150)],
        
        # GA-PSO N=450: 缺失50次 (seed 100-149)
        *[{'algorithm': 'GA-PSO', 'n': 450, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(100, 150)],
        
        # GA-PSO N=500: 缺失50次 (seed 100-149)
        *[{'algorithm': 'GA-PSO', 'n': 500, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(100, 150)],
        
        # NSGA-II N=400: 缺失36次 (seed 114-149)
        *[{'algorithm': 'NSGA-II', 'n': 400, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(114, 150)],
        
        # NSGA-II N=450: 缺失50次 (seed 100-149)
        *[{'algorithm': 'NSGA-II', 'n': 450, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(100, 150)],
        
        # NSGA-II N=500: 缺失50次 (seed 100-149)
        *[{'algorithm': 'NSGA-II', 'n': 500, 'seed': seed, 'budget': 30, 'rate': 1.0} 
          for seed in range(100, 150)],
    ]
    
    return high_priority_missing

def run_problem_parallel(params):
    """
    基于maintest.py的run_problem函数的并行版本
    确保与原始实验完全一致
    """
    algorithm_name = params['algorithm']
    n = params['n']
    seed = params['seed']
    budget = params['budget']
    rate = params['rate']
    
    try:
        # 加载数据（与maintest.py完全一致）
        data = DataUtils('./dataset/basestations.csv', './dataset/userdata.csv')
        base_stations = data.base_stations
        delay_matrix = generate_topology(data, n)
        
        # 使用maintest.py中的固定初始基站配置
        # 这是关键差异！必须与maintest.py保持一致
        initial_basestations = {
            12: 200000, 23: 100000, 28: 200000, 39: 200000, 47: 300000, 
            61: 200000, 88: 100000, 125: 200000, 141: 200000, 168: 200000, 
            192: 200000, 204: 200000, 258: 200000, 289: 300000, 297: 200000
        }
        
        # 创建算法实例（与maintest.py一致）
        problems = {}
        problems['Top-K'] = TopKServerPlacer(base_stations[:n], delay_matrix)
        problems['Random'] = RandomServerPlacer(base_stations[:n], delay_matrix)
        problems['ACO'] = ACOServerPlacer(base_stations[:n], delay_matrix)
        problems['PSO'] = PSOServerPlacer(base_stations[:n], delay_matrix)
        problems['GA-PSO'] = GAPSOServerPlacer(base_stations[:n], delay_matrix)
        problems['NSGA-II'] = NSGAIIServerPlacer(base_stations[:n], delay_matrix)
        problems['MOEA-D'] = MOEADServerPlacer(base_stations[:n], delay_matrix)
        problems['DE'] = DifferentialEvolutionServerPlacer(base_stations[:n], delay_matrix)
        
        if algorithm_name not in problems:
            logging.error(f"未知算法: {algorithm_name}")
            return None
            
        problem = problems[algorithm_name]
        
        # 使用原始配置参数（从config.py）
        cross_proability = 0.8  # 注意：保持原始拼写错误
        mutation_proability = 0.1  # 注意：保持原始拼写错误
        
        # 运行算法（与maintest.py的run_problem函数完全一致）
        start_time = time.time()
        
        # 调用place_server方法（与maintest.py一致）
        if algorithm_name in ['NSGA-II', 'MOEA-D', 'DE']:
            # 多目标算法使用正确的参数名
            problem.place_server(
                base_station_num=n,
                budget=budget,
                t_max=t_max,
                rate=rate,
                initial_basestations=initial_basestations,
                cross_probability=cross_proability,  # 使用正确拼写
                mutation_probability=mutation_proability,  # 使用正确拼写
                seed=seed
            )
        else:
            # 传统算法使用原有的参数名（包含拼写错误）
            problem.place_server(
                base_station_num=n,
                budget=budget,
                t_max=t_max,
                rate=rate,
                initial_basestations=initial_basestations,
                cross_proability=cross_proability,  # 保持拼写错误
                mutation_proability=mutation_proability,  # 保持拼写错误
                seed=seed
            )
        
        end_time = time.time()
        runtime = end_time - start_time
        
        # 计算结果（与maintest.py的run_problem函数一致）
        if hasattr(problem, 'edge_servers') and problem.edge_servers:
            try:
                # 获取性能指标（与maintest.py一致）
                latency_result = problem.objective_latency()
                workload_result = problem.objective_workload()
                fitness_result = problem.objective_fitness()
                
                # 处理返回值格式（与run_bsnum_experiment.py一致）
                delay = latency_result[0] if isinstance(latency_result, tuple) else latency_result
                workload = workload_result[0] if isinstance(workload_result, tuple) else workload_result
                fitness = fitness_result[0] if isinstance(fitness_result, tuple) else fitness_result
                
                num_servers = len(problem.edge_servers)
                
                # 数据合理性检查
                if fitness > 100 or delay > 1000 or workload > 100000:
                    logging.warning(f"异常数据: {algorithm_name}, N={n}, fitness={fitness}, delay={delay}, workload={workload}")
                
                # 创建独立的输出目录，避免冲突
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_dir = f'experiments/correct_parallel_{timestamp[:8]}'
                os.makedirs(output_dir, exist_ok=True)
                
                # 保存结果到独立目录（格式与原始实验一致）
                result_file = f'{output_dir}/{algorithm_name}_N{n}.txt'
                result_line = f"seed={seed},N={n},t_max={t_max},budget={budget},rate={rate} {algorithm_name}的fitness:{fitness},delay:{delay},workload:{workload},runtime:{runtime:.2f}\n"
                
                with open(result_file, 'a', encoding='utf-8') as f:
                    f.write(result_line)
                
                logging.info(f"✓ 完成: {algorithm_name}, N={n}, seed={seed}, fitness={fitness:.4f}, 用时={runtime:.1f}s")
                
                return {
                    'algorithm': algorithm_name,
                    'n': n,
                    'seed': seed,
                    'budget': budget,
                    'rate': rate,
                    'fitness': float(fitness),
                    'delay': float(delay),
                    'workload': float(workload),
                    'num_servers': num_servers,
                    'runtime': runtime,
                    'output_file': result_file,
                    'timestamp': datetime.now().isoformat()
                }
                
            except Exception as e:
                logging.error(f"结果计算失败: {algorithm_name}, N={n}, seed={seed}, 错误: {str(e)}")
                return None
        else:
            logging.error(f"无边缘服务器: {algorithm_name}, N={n}, seed={seed}")
            return None
            
    except Exception as e:
        logging.error(f"实验运行失败: {algorithm_name}, N={n}, seed={seed}, 错误: {str(e)}")
        return None

def run_correct_parallel_experiment(max_workers=3):
    """
    运行正确的并行实验
    """
    setup_logging()
    
    print("🚀 正确的并行恢复实验 - 基于maintest.py")
    print("=" * 60)
    
    # 获取高优先级缺失实验
    experiments = get_missing_high_priority()
    
    print(f"📋 高优先级实验数量: {len(experiments)}")
    print(f"🎯 主要目标: GA-PSO vs NSGA-II 在 N=400,450,500 的对比")
    print(f"✅ 使用maintest.py的固定初始基站配置")
    print(f"✅ 使用config.py的原始算法参数")
    
    # 显示实验分布
    exp_count = defaultdict(lambda: defaultdict(int))
    for exp in experiments:
        exp_count[exp['algorithm']][exp['n']] += 1
    
    print("\n📊 实验分布:")
    for algo in ['GA-PSO', 'NSGA-II']:
        print(f"  {algo}:")
        for n in [400, 450, 500]:
            count = exp_count[algo][n]
            print(f"    N={n}: {count} 次实验")
    
    # 显示关键配置信息
    print(f"\n⚙️ 关键配置:")
    print(f"  种群大小: {pop}")
    print(f"  最大迭代数: {MAX_GENERATION}")
    print(f"  重复次数: {repetitions}")
    print(f"  交叉概率: 0.8")
    print(f"  变异概率: 0.1")
    print(f"  覆盖半径: {t_max}")
    
    # 估算时间
    avg_time = 8  # 分钟（使用原始参数会更慢）
    estimated_hours = (len(experiments) * avg_time) / (60 * max_workers)
    print(f"\n⏰ 预计完成时间: {estimated_hours:.1f} 小时")
    print(f"🔧 使用 {max_workers} 个并行进程")
    
    # 自动开始执行
    print(f"\n🚀 开始正确的并行实验...")
    start_time = time.time()
    
    try:
        with Pool(processes=max_workers) as pool:
            results = pool.map(run_problem_parallel, experiments)
            successful_results = [r for r in results if r is not None]
    except KeyboardInterrupt:
        logging.info("实验被用户中断")
        return
    except Exception as e:
        logging.error(f"并行执行错误: {str(e)}")
        return
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 统计结果
    success_rate = len(successful_results) / len(experiments) * 100
    
    print(f"\n✅ 正确的并行实验完成!")
    print(f"⏱️  总时间: {total_time/3600:.2f} 小时")
    print(f"📊 成功率: {success_rate:.1f}% ({len(successful_results)}/{len(experiments)})")
    
    # 显示输出文件位置
    if successful_results:
        output_dirs = set(os.path.dirname(r['output_file']) for r in successful_results)
        print(f"\n📁 结果保存在:")
        for dir_path in output_dirs:
            print(f"   {dir_path}")
        
        print(f"\n💡 后续步骤:")
        print(f"1. 等待原实验完成")
        print(f"2. 将正确的并行实验结果合并到主目录")
        print(f"3. 生成对比图表")
    
    return successful_results

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='运行正确的并行实验')
    parser.add_argument('--workers', type=int, default=3, help='并行进程数')
    
    args = parser.parse_args()
    
    print(f"开始运行正确的并行实验，使用 {args.workers} 个进程")
    results = run_correct_parallel_experiment(args.workers)
    print(f"实验完成，共获得 {len(results) if results else 0} 个有效结果")
