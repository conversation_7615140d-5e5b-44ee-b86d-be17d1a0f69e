"""
小规模实验测试
验证所有算法能正常运行并生成结果
"""

from typing import Dict
import sys
import random
import os

import config
from algorithm import *
from generate_topology import *
from config import *
from multi_objective_algorithms import NSGAIIServerPlacer, MOEADServerPlacer, DifferentialEvolutionServerPlacer

def run_small_scale_experiment():
    """运行小规模实验"""
    print("=== GAPSO Small Scale Experiment ===")
    
    # 加载数据
    data = DataUtils('./dataset/basestations.csv','./dataset/userdata.csv')
    base_stations = data.base_stations
    
    # 实验参数
    n = 100  # 基站数量
    budget = 15  # 预算
    t_max = 100  # 覆盖半径
    rate = 1.0  # 工作负载比例
    cross_proability = 0.8  # 交叉概率（注意拼写）
    mutation_proability = 0.1  # 变异概率（注意拼写）
    
    # 初始基站配置
    initial_basestations = {10: 100000, 25: 100000, 40: 100000, 55: 100000, 70: 100000}
    
    # 生成拓扑
    delay_matrix = generate_topology(data, n)
    
    # 定义算法
    algorithms = {}
    
    # 传统算法
    try:
        algorithms['Top-K'] = TopKServerPlacer(base_stations[:n], delay_matrix)
        print("✓ Top-K algorithm loaded")
    except Exception as e:
        print(f"✗ Top-K algorithm failed to load: {e}")
    
    try:
        algorithms['Random'] = RandomServerPlacer(base_stations[:n], delay_matrix)
        print("✓ Random algorithm loaded")
    except Exception as e:
        print(f"✗ Random algorithm failed to load: {e}")
    
    try:
        algorithms['ACO'] = ACOServerPlacer(base_stations[:n], delay_matrix)
        print("✓ ACO algorithm loaded")
    except Exception as e:
        print(f"✗ ACO algorithm failed to load: {e}")
    
    # 跳过有问题的PSO算法
    # try:
    #     algorithms['PSO'] = PSOServerPlacer(base_stations[:n], delay_matrix)
    #     print("✓ PSO algorithm loaded")
    # except Exception as e:
    #     print(f"✗ PSO algorithm failed to load: {e}")
    
    try:
        algorithms['GA-PSO'] = GAPSOServerPlacer(base_stations[:n], delay_matrix)
        print("✓ GA-PSO algorithm loaded")
    except Exception as e:
        print(f"✗ GA-PSO algorithm failed to load: {e}")
    
    # 多目标算法
    try:
        algorithms['NSGA-II'] = NSGAIIServerPlacer(base_stations[:n], delay_matrix)
        print("✓ NSGA-II algorithm loaded")
    except Exception as e:
        print(f"✗ NSGA-II algorithm failed to load: {e}")
    
    try:
        algorithms['MOEA-D'] = MOEADServerPlacer(base_stations[:n], delay_matrix)
        print("✓ MOEA-D algorithm loaded")
    except Exception as e:
        print(f"✗ MOEA-D algorithm failed to load: {e}")
    
    try:
        algorithms['DE'] = DifferentialEvolutionServerPlacer(base_stations[:n], delay_matrix)
        print("✓ DE algorithm loaded")
    except Exception as e:
        print(f"✗ DE algorithm failed to load: {e}")
    
    print(f"\nTotal algorithms loaded: {len(algorithms)}")
    
    # 确保results目录存在
    if not os.path.exists('results'):
        os.makedirs('results')
    
    # 运行实验
    results = {}
    for alg_name, algorithm in algorithms.items():
        print(f"\n--- Running {alg_name} ---")
        
        try:
            # 设置种子
            seed = 100
            
            # 运行算法
            if alg_name in ['NSGA-II', 'MOEA-D', 'DE']:
                # 多目标算法使用正确拼写
                algorithm.place_server(
                    base_station_num=n,
                    budget=budget,
                    t_max=t_max,
                    rate=rate,
                    initial_basestations=initial_basestations,
                    cross_probability=cross_proability,
                    mutation_probability=mutation_proability,
                    seed=seed
                )
            else:
                # 传统算法使用原有拼写
                algorithm.place_server(
                    base_station_num=n,
                    budget=budget,
                    t_max=t_max,
                    rate=rate,
                    initial_basestations=initial_basestations,
                    cross_proability=cross_proability,
                    mutation_proability=mutation_proability,
                    seed=seed
                )
            
            # 计算结果
            if hasattr(algorithm, 'edge_servers') and algorithm.edge_servers is not None:
                num_servers = len(algorithm.edge_servers)
                
                # 计算目标函数
                if alg_name in ['NSGA-II', 'MOEA-D', 'DE']:
                    delay = algorithm.objective_latency()
                    workload = algorithm.objective_workload()
                    fitness = algorithm.objective_fitness()
                else:
                    # 处理可能的元组返回值
                    delay_result = algorithm.objective_latency()
                    delay = delay_result[0] if isinstance(delay_result, tuple) else delay_result
                    
                    workload_result = algorithm.objective_workload()
                    workload = workload_result[0] if isinstance(workload_result, tuple) else workload_result
                    
                    fitness_result = algorithm.objective_fitness()
                    fitness = fitness_result[0] if isinstance(fitness_result, tuple) else fitness_result
                
                results[alg_name] = {
                    'servers': num_servers,
                    'delay': delay,
                    'workload': workload,
                    'fitness': fitness
                }
                
                print(f"✓ {alg_name} completed successfully")
                print(f"  - Edge servers: {num_servers}")
                print(f"  - Delay: {delay:.2f} ms")
                print(f"  - Workload std: {workload:.2f}")
                print(f"  - Fitness: {fitness:.4f}")
                
                # 保存结果到文件
                result_file = f'results/{alg_name}.txt'
                with open(result_file, 'w', encoding='utf-8') as f:
                    f.write(f"seed={seed},N={n},t_max={t_max},budget={budget},rate={rate} {alg_name}的fitness:{fitness},delay:{delay},workload:{workload}\n")
                
            else:
                print(f"✗ {alg_name} failed - no edge servers deployed")
                results[alg_name] = None
                
        except Exception as e:
            print(f"✗ {alg_name} failed with error: {e}")
            import traceback
            traceback.print_exc()
            results[alg_name] = None
    
    # 汇总结果
    print("\n" + "="*60)
    print("EXPERIMENT SUMMARY")
    print("="*60)
    
    successful_algorithms = 0
    total_algorithms = len(algorithms)
    
    for alg_name, result in results.items():
        if result is not None:
            print(f"{alg_name:<15} SUCCESS - Fitness: {result['fitness']:.4f}")
            successful_algorithms += 1
        else:
            print(f"{alg_name:<15} FAILED")
    
    print("-"*60)
    print(f"Success Rate: {successful_algorithms}/{total_algorithms} ({successful_algorithms/total_algorithms*100:.1f}%)")
    
    if successful_algorithms >= 4:  # 至少4个算法成功
        print("🎉 Small scale experiment successful!")
        print("Ready for full-scale experiments.")
        return True
    else:
        print("⚠️ Too many algorithms failed.")
        print("Please check the issues before running full experiments.")
        return False

if __name__ == "__main__":
    success = run_small_scale_experiment()
    sys.exit(0 if success else 1)
