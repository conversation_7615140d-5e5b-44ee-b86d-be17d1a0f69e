import math

# import cplex
import numpy as np
import matplotlib.pyplot as plt
from copy import deepcopy, copy
from scipy.cluster import vq
import logging
import random
from datetime import datetime
from typing import List, Iterable
import config
import utils
from config import *
from particle import Particle, Gbest_vector
from base_station import BaseStation
# from config import *
from edge_server import EdgeServer
from utils import Random_Placement_Set
from math import cos, asin, sqrt

class ServerPlacer(object):
    def __init__(self, all_base_stations: List[BaseStation], topology: List[List[float]]):
        self.base_stations = all_base_stations
        self.base_station_num = len(self.base_stations)
        logging.info('载入基站信息，共有{0}个基站'.format(len(all_base_stations)))
        self.distance_topology = topology[:len(all_base_stations)]
        self.edge_servers = None
        self.remote_cloud = []
        self.distances = topology
        self.MAX_GENERATION = MAX_GENERATION  # 迭代次数
    def place_server(self, base_station_num, budget,t_max,rate,initial_basestations, cross_proability, mutation_proability,seed):
        raise NotImplementedError

    def get_bs_distance(self, bs1: int, bs2: int):
        return self.distance_topology[bs1][bs2]

    def _distance_edge_server_base_station(self, edge_server: EdgeServer, base_station: BaseStation) -> float:
        """
        Calculate distance between given edge server and base station

        :param edge_server:
        :param base_station:
        :return: distance(km)
        """
        if edge_server.base_station_id is not None:
            return self.distance_topology[edge_server.base_station_id][base_station.id]
        else:
            print(edge_server, '\n\n', base_station)
            raise Exception('topology error')
        # if edge_server.base_station_id:
        #     return self.distances[edge_server.base_station_id][base_station.id]
        # return DataUtils.calc_distance(edge_server.latitude, edge_server.longitude, base_station.latitude,
        #                                base_station.longitude)

    def objective_latency(self):
        """
        Calculate average edge server access delay
        """
        assert self.edge_servers
        total_delay = 0
        base_station_num = 0
        count = 0
        for es in self.edge_servers:
            for bs in es.assigned_base_stations:
                delay = self._distance_edge_server_base_station(es, bs)
                total_delay += delay
                base_station_num += 1
                count += 1
        for i in range(self.base_station_num):
            if self.remote_cloud[i] == 1:
                total_delay += remote_delay
                base_station_num += 1
        return total_delay / base_station_num, count

    def objective_workload(self):
        """
        Calculate average edge server workload

        Max worklaod of edge server - Min workload
        """
        workloads_list = []
        assert self.edge_servers
        for edge in self.edge_servers:
            workloads_list.append(edge.workload / edge.max_workload * 100000)
        res = np.std(workloads_list)
        return res

    def objective_cluster(self):
        """
        Calculate average edge server workload

        Max worklaod of edge server - Min workload
        """
        assert self.edge_servers
        results = []
        result = []
        for i, es in enumerate(self.edge_servers):
            for j, bs in enumerate(es.assigned_base_stations):
                result.append(bs.id)
            results.append(result)
            result = []
        return results

    def objective_cluster_centers(self):
        """
        Calculate average edge server workload

        Max worklaod of edge server - Min workload
        """
        assert self.edge_servers
        results = []
        for es in self.edge_servers:
            results.append(es.base_station_id)
        return results

    def objective_es_maxworkload(self):
        """
        Calculate average edge server workload

        Max worklaod of edge server - Min workload
        """
        assert self.edge_servers
        es_workload = {}
        for es in self.edge_servers:
            es_workload[es.base_station_id] = es.workload
        return es_workload

    def objective_fitness(self):
        assert self.edge_servers
        edge_temp_workload = {}
        delay_list = [-1 for k in range(self.base_station_num)]  # ��������������������������������
        for es in self.edge_servers:
            edge_temp_workload[es.base_station_id] = es.workload / (es.max_workload / single_max_workload)
            for bs in es.assigned_base_stations:
                delay_list[bs.id] = self.get_bs_distance(es.base_station_id,bs.id)
        for i in range(self.base_station_num):
            if self.remote_cloud[i] == 1:
                delay_list[i] = remote_delay
        max_delay = max(delay_list)
        min_delay = min(delay_list)
        nor_delay = []
        for d in delay_list:
            nor_d = (d - min_delay) / (max_delay - min_delay)
            nor_delay.append(nor_d)
        max_workload = max(edge_temp_workload.values())
        min_workload = min(edge_temp_workload.values())
        nor_workload = []
        for e in edge_temp_workload.values():
            nor_w = (e) / (max_workload)
            nor_workload.append(nor_w)
        w_std1 = np.std(nor_workload)
        d_avg = sum(delay_list) / len(delay_list) / t_max
        return d_avg + w_std1

    def objective_cloud(self):
        remote_list = []
        for i in range(self.base_station_num):
            if self.remote_cloud[i] == 1:
                remote_list.append(i)
        return remote_list

    def calc_distance(self, lat_a, lng_a, lat_b=31.239835, lng_b=121.499798):
        """
        由经纬度计算距离

        :param lat_a: 纬度A
        :param lng_a: 经度A
        :param lat_b: 纬度B
        :param lng_b: 经度B
        :return: 距离(km)
        """
        ## 31.2398356455997, 121.4997982130827 东方明珠
        p = 0.017453292519943295  # Pi/180
        a = 0.5 - cos((lat_b - lat_a) * p) / 2 + cos(lat_a * p) * cos(lat_b * p) * (1 - cos((lng_b - lng_a) * p)) / 2
        return 12742 * asin(sqrt(a))  # 2*R*asin...

    def get_cost(self,bs,max_workload):
        dis_with_city_center = utils.calc_distance(bs.latitude, bs.longitude)
        rental_cost = distance_to_cost * math.log10(dis_with_city_center) + fix_cost
        exp_cost = install_cost * (max_workload/single_max_workload)
        return rental_cost+exp_cost

    def get_total_cost(self,edge_servers,init_basestations):
        movable_edge_server_id = []
        bs_cost = 0
        for edge in edge_servers:
            if edge.base_station_id not in init_basestations.keys():
                movable_edge_server_id.append(edge.base_station_id)
        for id in movable_edge_server_id:  # 新添加的边缘服务器的花费
            for edge in edge_servers:
                if edge.base_station_id == id:
                    bs_cost += self.get_cost(self.base_stations[id],edge.max_workload)
        for key, values in init_basestations.items():  # 老的边缘服务器的花费
            for edge in edge_servers:
                if key == edge.base_station_id:
                    bs_cost += install_cost * ((edge.max_workload-values) / single_max_workload)
        return bs_cost


class TopKServerPlacer(ServerPlacer):
    """
    Top-K approach
    """
    def place_server(self, base_station_num, budget,t_max,rate,initial_basestations, cross_proability, mutation_proability,seed):
        random.seed(seed)
        base_stations_scale = deepcopy(self.base_stations)
        for bs in base_stations_scale:
            bs.workload = bs.workload * rate
        self.base_station_num = base_station_num
        self.edge_temp_workload = {}
        self.fixed_base_stations = []
        self.cross_proability = cross_proability
        self.mutation_proability = mutation_proability
        unplaced_set = Random_Placement_Set(base_station_num)
        sorted_base_stations = sorted(base_stations_scale, key=lambda x: x.workload, reverse=False)
        edge_servers = []
        count = 0
        for key, value in initial_basestations.items():
            unplaced_set.remove(key)
            edge_servers.append(EdgeServer(count, base_stations_scale[key].latitude, base_stations_scale[key].longitude, value, base_stations_scale[key].id))
            sorted_base_stations.remove(base_stations_scale[key])
            count += 1
        count_edge_id = len(initial_basestations.keys())
        while self.get_total_cost(edge_servers,initial_basestations) < budget:  # 新放置边缘服务器
            distance_dict = {}
            total_dis_dict = {}
            remote_cloud = [0 for i in range(self.base_station_num)]
            e = sorted_base_stations.pop().id
            unplaced_set.remove(e)
            bs_e = base_stations_scale[e]
            edge_temp = EdgeServer(count_edge_id, bs_e.latitude, bs_e.longitude, single_max_workload,bs_e.id)
            count_edge_id += 1
            edge_servers.append(edge_temp)
            for id,edge in enumerate(edge_servers):
                edge.assigned_base_stations = []
                edge.workload = 0
                edge.assigned_base_stations.append(base_stations_scale[edge.base_station_id])  # 加入分配队列
                edge.workload = base_stations_scale[edge.base_station_id].workload  # 更新edge负载
                if id < len(initial_basestations.values()):
                    edge.max_workload = list(initial_basestations.values())[id]
                else:
                    edge.max_workload = single_max_workload
            for bs_num in unplaced_set:
                for edge in edge_servers:
                    tmp = self.get_bs_distance(edge.base_station_id, base_stations_scale[bs_num].id)
                    distance_dict[edge.id] = tmp
                sorted_distance_list = sorted(distance_dict.items(), key=lambda x: x[1], reverse=False)
                total_dis_dict[bs_num] = sorted_distance_list
            unplaced_list = list(unplaced_set)
            for num,bs_num in enumerate(unplaced_list):
                distance_k = {}
                for i in total_dis_dict[bs_num]:  # 基站连接
                    if i[1] < t_max:
                        if (edge_servers[i[0]].workload + base_stations_scale[bs_num].workload) / edge_servers[i[0]].max_workload < 1:
                            distance_k[i[0]] = w0 * i[1] / t_max + edge_servers[i[0]].workload / edge_servers[i[0]].max_workload
                    else:
                        break
                sorted_distance_k = sorted(distance_k.items(), key=lambda x: x[1], reverse=False)
                if len(sorted_distance_k) == 0:  # 没有可连接的基站
                    print('to cloud')
                    remote_cloud[bs_num] = 1
                else:
                    link_edge = random.sample(sorted_distance_k[:sample_num], 1)
                    link_edge_num = link_edge[0][0]
                    if (edge_servers[link_edge_num].workload + base_stations_scale[bs_num].workload) / edge_servers[link_edge_num].max_workload > th and edge_servers[link_edge_num].max_workload < c_max * single_max_workload:
                        edge_servers[link_edge_num].max_workload += single_max_workload
                    edge_servers[link_edge_num].assigned_base_stations.append(base_stations_scale[bs_num])
                    edge_servers[link_edge_num].workload += base_stations_scale[bs_num].workload
                    remote_cloud[bs_num] = 0
            if self.get_total_cost(edge_servers,initial_basestations) < budget:
                self.edge_servers = deepcopy(edge_servers)
                self.remote_cloud = deepcopy(remote_cloud)
            else:
                break

        # ==============================验证bs分配状况==========================================
        if self.edge_servers is None:
            self.place_server(base_station_num,budget,t_max,rate=1.0,initial_basestations=initial_basestations,cross_proability=cross_proability, mutation_proability=mutation_proability,seed =seed+1)
        recorded_bs = set()
        recorded_list = []
        for edge in self.edge_servers:
            for bs in edge.assigned_base_stations:
                recorded_bs.add(bs.id)
                recorded_list.append(bs.id)
        for bs in range(self.base_station_num):
            if self.remote_cloud[bs] == 1:
                recorded_bs.add(bs)
                recorded_list.append(bs)
        if len(recorded_bs) != base_station_num and len(recorded_list) != base_station_num:
            raise Exception('未将所有bs正确分配')
        # ==============================验证bs分配状况==========================================
        logging.info("{0}:End running TopFirst ".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))

class RandomServerPlacer(ServerPlacer):
    """
    Random approach
    """
    def place_server(self, base_station_num,budget,t_max,rate,initial_basestations, cross_proability, mutation_proability,seed):
        random.seed(seed)
        base_stations_scale = deepcopy(self.base_stations)
        for bs in base_stations_scale:
            bs.workload = bs.workload * rate
        self.base_station_num = base_station_num
        self.edge_temp_workload = {}
        self.fixed_base_stations = []
        self.cross_proability = cross_proability
        self.mutation_proability = mutation_proability
        unplaced_set = Random_Placement_Set(base_station_num)
        edge_servers = []
        count = 0
        for key,value in initial_basestations.items():
            unplaced_set.remove(key)
            edge_servers.append(EdgeServer(count, base_stations_scale[key].latitude, base_stations_scale[key].longitude, value, base_stations_scale[key].id))
            count += 1
        count_edge_id = len(initial_basestations.keys())
        while self.get_total_cost(edge_servers,initial_basestations) < budget:  # 新放置边缘服务器
            distance_dict = {}
            total_dis_dict = {}
            remote_cloud = [0 for i in range(self.base_station_num)]
            e = unplaced_set.pop()
            bs_e = base_stations_scale[e]
            edge_temp = EdgeServer(count_edge_id, bs_e.latitude, bs_e.longitude,single_max_workload, bs_e.id)
            count_edge_id += 1
            edge_servers.append(edge_temp)
            for id, edge in enumerate(edge_servers):
                edge.assigned_base_stations = []
                edge.workload = 0
                edge.assigned_base_stations.append(base_stations_scale[edge.base_station_id])  # 加入分配队列
                edge.workload = base_stations_scale[edge.base_station_id].workload  # 更新edge负载
                if id < len(initial_basestations.values()):
                    edge.max_workload = list(initial_basestations.values())[id]
                else:
                    edge.max_workload = single_max_workload
            for bs_num in unplaced_set:
                for edge in edge_servers:
                    tmp = self.get_bs_distance(edge.base_station_id, base_stations_scale[bs_num].id)
                    distance_dict[edge.id] = tmp
                sorted_distance_list = sorted(distance_dict.items(), key=lambda x: x[1], reverse=False)
                total_dis_dict[bs_num] = sorted_distance_list
            # sorted_unplaced_set = sorted(unplaced_set, key=lambda x: base_stations_scale[x].workload, reverse=True)
            # unplaced_list = list(sorted_unplaced_set)
            unplaced_list = list(unplaced_set)
            for num,bs_num in enumerate(unplaced_list):
                distance_k = {}
                for i in total_dis_dict[bs_num]:  # 基站连接
                    if i[1] < t_max:
                        if(edge_servers[i[0]].workload + base_stations_scale[bs_num].workload) / edge_servers[i[0]].max_workload < 1:
                            distance_k[i[0]] = w0 * i[1] / t_max + edge_servers[i[0]].workload / edge_servers[i[0]].max_workload
                    else:
                        break
                sorted_distance_k = sorted(distance_k.items(), key=lambda x: x[1], reverse=False)
                workload = {}
                difference = {}
                if len(sorted_distance_k) == 0:
                    print('to cloud')
                    remote_cloud[bs_num] = 1
                else:
                    link_edge = random.sample(sorted_distance_k[:sample_num], 1)
                    link_edge_num = link_edge[0][0]
                    if (edge_servers[link_edge_num].workload + base_stations_scale[bs_num].workload) / edge_servers[link_edge_num].max_workload > th and edge_servers[link_edge_num].max_workload < c_max * single_max_workload:
                        edge_servers[link_edge_num].max_workload += single_max_workload
                    edge_servers[link_edge_num].assigned_base_stations.append(base_stations_scale[bs_num])
                    edge_servers[link_edge_num].workload += base_stations_scale[bs_num].workload
                    remote_cloud[bs_num] = 0
            if self.get_total_cost(edge_servers,initial_basestations) < budget:
                self.edge_servers = deepcopy(edge_servers)
                self.remote_cloud = deepcopy(remote_cloud)
            else:
                break
        # ==============================验证bs分配状况==========================================
        if self.edge_servers is None:
            self.place_server(base_station_num, budget, t_max, rate=1.0, initial_basestations=initial_basestations,cross_proability=cross_proability, mutation_proability=mutation_proability,seed =seed+1)
        recorded_bs = set()
        recorded_list = []
        for edge in self.edge_servers:
            for bs in edge.assigned_base_stations:
                recorded_bs.add(bs.id)
                recorded_list.append(bs.id)
        for bs in range(self.base_station_num):
            if self.remote_cloud[bs] == 1:
                recorded_bs.add(bs)
                recorded_list.append(bs)
        if len(recorded_bs) != base_station_num and len(recorded_list) != base_station_num:
            raise Exception('未将所有bs正确分配')
        # ==============================验证bs分配状况==========================================
        logging.info("{0}:End running TopFirst ".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))


class ACOServerPlacer(ServerPlacer):
    """
    ACO approach
    """
    def place_server(self, base_station_num,budget,t_max,rate,initial_basestations, cross_proability, mutation_proability,seed):
        random.seed(seed)
        base_stations_scale = deepcopy(self.base_stations)
        for bs in base_stations_scale:
            bs.workload = bs.workload * rate
        self.base_station_num = base_station_num
        self.MAX_GENERATION = 10
        self.cross_proability = cross_proability
        self.mutation_proability = mutation_proability
        self.edge_temp_workload = {}
        # 批量初始化
        self.ants = [Particle(i,
                               base_stations_scale,
                               self.distance_topology,budget,t_max,
                               initial_basestations,
                               cross_proability, mutation_proability)
                          for i in range(pop)]
        # 更新pbest
        for a in self.ants:
            a.init_pbest_vector()
        self.g_best = Gbest_vector()
        self.g_best.update(self.ants)
        best_fitness = []
        for generation in range(self.MAX_GENERATION):
            print("\n\n==============覆盖半径", t_max, '  基站数目', self.base_station_num, "  第", generation,"代==================")
            for num, ant in enumerate(self.ants):
                # ant.evo(self.g_best)
                ant.ant_move()
                # 更新pbest
                ant.update_pbest_vector()
                print("ACO第{0}个粒子的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,ant.fitness_now,ant.norm_delay,ant.norm_workload,len(ant.edge_temp_workload)))
            self.g_best.update(self.ants)
            best_fitness.append(self.g_best.fitness)
            print("ACO第{0}次迭代的best_fitness:{1},nordelay:{2},norworkload:{3}".format(generation,best_fitness[generation],self.g_best.norm_delay,self.g_best.norm_workload))
            if (generation + 1) % 10 == 0:
                file = open('data/resultsACO.txt', 'a')
                file.write("N={0},t_max={1},ACO第{2}次迭代的best_fitness:{3},nordelay:{4},norworkload:{5}\n".format(self.base_station_num, t_max, generation + 1, best_fitness[generation],
                    self.g_best.norm_delay, self.g_best.norm_workload))
                file.flush()
                file.close()
        # 把结果翻译成标准输出
        edge_servers = []
        count_edge_id = 0
        placement_scheme = self.g_best.placement_scheme
        self.vectorY = self.g_best.vectorY
        for edge in placement_scheme:
            at_bs = base_stations_scale[edge['at_bs_No']]
            edge_temp = EdgeServer(count_edge_id, at_bs.latitude, at_bs.longitude, edge['max_workload'],edge['at_bs_No'])
            edge_temp.workload = edge['workload']
            edge_temp.assigned_base_stations = edge['serving_bs']
            count_edge_id += 1
            edge_servers.append(edge_temp)
        self.edge_servers = edge_servers
        # ==============================验证bs分配状况==========================================
        recorded_bs = set()
        recorded_list = []
        self.remote_cloud = [0 for i in range(self.base_station_num)]
        for edge in self.edge_servers:
            for bs in edge.assigned_base_stations:
                recorded_bs.add(bs.id)
                recorded_list.append(bs.id)
        for bs in range(self.base_station_num):
            if self.vectorY[bs] == self.base_station_num:
                self.remote_cloud[bs] = 1
                recorded_bs.add(bs)
                recorded_list.append(bs)
        if len(recorded_bs) != base_station_num and len(recorded_list) != base_station_num:
            raise Exception('未将所有bs正确分配')
        # ==============================验证bs分配状况==========================================
        logging.info("{0}:End running TopFirst ".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        # plt.xlabel('generation')
        # plt.ylabel('fitness')
        # plt.plot(range(0, self.MAX_GENERATION, 1), best_fitness)
        # plt.savefig(
        #     './final/ACO_{}basestation_{}iter_{}pop.png'.format(base_station_num, self.MAX_GENERATION, pop))
        # plt.close()


class PSOServerPlacer(ServerPlacer):

    def place_server(self, base_station_num,budget,t_max,rate,initial_basestations, cross_proability, mutation_proability,seed):
        random.seed(seed)
        base_stations_scale = deepcopy(self.base_stations)
        for bs in base_stations_scale:
            bs.workload = bs.workload * rate
        self.base_station_num = base_station_num
        self.particles = []
        self.cross_proability = cross_proability
        self.mutation_proability = mutation_proability
        self.g_best = None
        # 批量初始化
        self.particles = [Particle(i,
                                   base_stations_scale,
                                   self.distance_topology,budget,t_max,
                                   initial_basestations,
                                   cross_proability, mutation_proability)
                          for i in range(pop)]

        # 更新pbest
        for p in self.particles:
            p.init_pbest_vector()
        self.g_best = Gbest_vector()
        self.g_best.update(self.particles)
        best_fitness = []
        # 更新粒子
        for generation in range(self.MAX_GENERATION):
            print("\n\n==============覆盖半径", t_max, '  基站数目', self.base_station_num, "  第", generation,"代==================")
            for num, particle in enumerate(self.particles):
                particle.evolution_vector(self.g_best)
                # 更新pbest
                particle.update_pbest_vector()
                print("PSO第{0}个粒子的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
            self.g_best.update(self.particles)
            best_fitness.append(self.g_best.fitness)
            print("PSO第{0}次迭代的best_fitness:{1},nordelay:{2},norworkload:{3}".format(generation,best_fitness[generation],self.g_best.norm_delay,self.g_best.norm_workload))
            if (generation + 1) % 10 == 0:
                file = open('iter/resultsPSO.txt', 'a')
                file.write("N={0},t_max={1},PSO第{2}次,seed={6}迭代的best_fitness:{3},nordelay:{4},norworkload:{5}\n".format(
                    self.base_station_num, t_max, generation + 1, best_fitness[generation],
                    self.g_best.norm_delay, self.g_best.norm_workload,seed))
                file.flush()
                file.close()
        # 把结果翻译成标准输出
        edge_servers = []
        count_edge_id = 0
        placement_scheme = self.g_best.placement_scheme
        self.vectorY = self.g_best.vectorY
        for edge in placement_scheme:
            at_bs = base_stations_scale[edge['at_bs_No']]
            edge_temp = EdgeServer(count_edge_id, at_bs.latitude, at_bs.longitude, edge['max_workload'],
                                   edge['at_bs_No'])
            edge_temp.workload = edge['workload']
            edge_temp.assigned_base_stations = edge['serving_bs']
            count_edge_id += 1
            edge_servers.append(edge_temp)
        self.edge_servers = edge_servers
        # ==============================验证bs分配状况==========================================
        recorded_bs = set()
        recorded_list = []
        self.remote_cloud = [0 for i in range(self.base_station_num)]
        for edge in self.edge_servers:
            for bs in edge.assigned_base_stations:
                recorded_bs.add(bs.id)
                recorded_list.append(bs.id)
        for bs in range(self.base_station_num):
            if self.vectorY[bs] == self.base_station_num:
                self.remote_cloud[bs] = 1
                recorded_bs.add(bs)
                recorded_list.append(bs)
        if len(recorded_bs) != base_station_num and len(recorded_list) != base_station_num:
            raise Exception('未将所有bs正确分配')
        # ==============================验证bs分配状况==========================================
        logging.info("{0}:End running TopFirst ".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        # plt.xlabel('generation')
        # plt.ylabel('fitness')
        # plt.plot(range(0, self.MAX_GENERATION, 1), best_fitness)
        # plt.savefig(
        #     './final/PSO_{}basestation_{}iter_{}pop_{}seed.png'.format(base_station_num, self.MAX_GENERATION, pop,seed))
        # plt.close()

class PSO1ServerPlacer(ServerPlacer):
    def place_server(self, base_station_num,budget,t_max,rate,initial_basestations, cross_proability, mutation_proability,seed):
        random.seed(seed)
        base_stations_scale = deepcopy(self.base_stations)
        for bs in base_stations_scale:
            bs.workload = bs.workload * rate
        self.base_station_num = base_station_num
        self.particles = []
        self.g_best = None
        # 批量初始化
        self.particles = [Particle(i,
                                   base_stations_scale,
                                   self.distance_topology,budget,t_max,
                                   initial_basestations,
                                   cross_proability, mutation_proability)
                          for i in range(int(pop*(1+two_party_num_rate0)))]
        # 更新pbest
        for p in self.particles:
            p.init_pbest_vector()
        self.g_best = Gbest_vector()
        self.g_best.update(self.particles)
        self.particles = sorted(self.particles, key=lambda individual: individual.fitness_now, reverse=False)
        best_fitness = []
        # 更新粒子
        for generation in range(self.MAX_GENERATION):
            print("\n\n==============覆盖半径", t_max, '  基站数目', self.base_station_num, "  第", generation,
                  "代==================")
            # 目标函数列表
            count = 0
            pop_list = [i for i in range(pop)]
            elite_group = self.particles[:int(0.3 * pop)]
            two_party = random.sample(pop_list,int(pop * two_party_num_rate0))
            for num, particle in enumerate(self.particles):
                if num < int(0.3 * pop):
                    if num in two_party:
                        vectorX1, vectorX2, vectorY1, vectorY2 = particle.crossover(self.g_best)
                        particle.vectorX = vectorX1
                        particle.vectorY = vectorY1
                        self.particles[pop+count].vectorX = vectorX2
                        self.particles[pop+count].vectorY = vectorY2
                        particle.mutation()
                        wrong_x, wrong_y, flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        self.particles[pop + count].mutation()
                        wrong_x, wrong_y,flag = self.particles[pop+count].check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            self.particles[pop+count].del_and_refill_vector(wrong_x, wrong_y)
                        self.particles[pop+count].update_pbest_vector()
                        print("PSO第{0}个粒子X1的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                        print("PSO第{0}个粒子X2的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,self.particles[pop+count].fitness_now,self.particles[pop+count].norm_delay,self.particles[pop+count].norm_workload,len(self.particles[pop+count].edge_temp_workload)))
                        count += 1
                    else:
                        particle.evolution_vector_gapso(self.g_best)
                        wrong_x, wrong_y,flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        print("PSO第{0}个粒子的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                elif int(0.3 * pop) <= num < int(pop):
                    if num in two_party:
                        elite_particle = random.sample(elite_group, 1)[0]
                        vectorX1, vectorX2, vectorY1, vectorY2 = particle.crossover(elite_particle)
                        particle.vectorX = vectorX1
                        particle.vectorY = vectorY1
                        self.particles[pop + count].vectorX = vectorX2
                        self.particles[pop + count].vectorY = vectorY2
                        particle.mutation()
                        wrong_x, wrong_y, flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        self.particles[pop + count].mutation()
                        wrong_x, wrong_y, flag = self.particles[pop + count].check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            self.particles[pop + count].del_and_refill_vector(wrong_x, wrong_y)
                        self.particles[pop + count].update_pbest_vector()
                        print("PSO第{0}个粒子X1的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                        print("PSO第{0}个粒子X2的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,self.particles[pop + count].fitness_now,self.particles[pop + count].norm_delay,self.particles[pop + count].norm_workload,len(self.particles[pop + count].edge_temp_workload)))
                        count += 1
                    else:
                        particle.evolution_vector_gapso(self.g_best)
                        wrong_x, wrong_y ,flag= particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        print("PSO第{0}个粒子的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
            self.particles = sorted(self.particles, key=lambda individual: individual.fitness_now, reverse=False)
            self.g_best.update(self.particles)
            best_fitness.append(self.g_best.fitness)
            print("PSO第{0}次迭代的best_fitness:{1},nordelay:{2},norworkload:{3}".format(generation, best_fitness[generation], self.g_best.norm_delay, self.g_best.norm_workload))
            if (generation + 1) % 10 == 0:
                file = open('iter/resultsPSO.txt', 'a')
                file.write("N={0},t_max={1},PSO第{2}次,seed={6}迭代的best_fitness:{3},nordelay:{4},norworkload:{5}\n".format(
                    self.base_station_num, t_max,generation + 1, best_fitness[generation],
                    self.g_best.norm_delay, self.g_best.norm_workload,seed))
                file.flush()
                file.close()
        # 把结果翻译成标准输出
        edge_servers = []
        count_edge_id = 0
        placement_scheme = self.g_best.placement_scheme
        self.vectorY = self.g_best.vectorY
        for edge in placement_scheme:
            at_bs = base_stations_scale[edge['at_bs_No']]
            edge_temp = EdgeServer(count_edge_id, at_bs.latitude, at_bs.longitude, edge['max_workload'],
                                   edge['at_bs_No'])
            edge_temp.workload = edge['workload']
            edge_temp.assigned_base_stations = edge['serving_bs']
            count_edge_id += 1
            edge_servers.append(edge_temp)
        self.edge_servers = edge_servers
        # ==============================验证bs分配状况==========================================
        recorded_bs = set()
        recorded_list = []
        self.remote_cloud = [0 for i in range(self.base_station_num)]
        for edge in self.edge_servers:
            for bs in edge.assigned_base_stations:
                recorded_bs.add(bs.id)
                recorded_list.append(bs.id)
        for bs in range(self.base_station_num):
            if self.vectorY[bs] == self.base_station_num:
                self.remote_cloud[bs] = 1
                recorded_bs.add(bs)
                recorded_list.append(bs)
        if len(recorded_bs) != base_station_num and len(recorded_list) != base_station_num:
            raise Exception('未将所有bs正确分配')
        # ==============================验证bs分配状况==========================================
        logging.info("{0}:End running TopFirst ".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        # plt.xlabel('generation')
        # plt.ylabel('fitness')
        # plt.plot(range(0, self.MAX_GENERATION, 1), best_fitness)
        # plt.savefig(
        #     './final/PSO_{}basestation_{}iter_{}pop_{}seed.png'.format(base_station_num, self.MAX_GENERATION, pop,seed))
        # plt.close()

class GAPSOServerPlacer(ServerPlacer):
    def place_server(self, base_station_num,budget,t_max,rate,initial_basestations, cross_proability, mutation_proability,seed):
        random.seed(seed)
        base_stations_scale = deepcopy(self.base_stations)
        for bs in base_stations_scale:
            bs.workload = bs.workload * rate
        self.base_station_num = base_station_num
        self.particles = []
        self.g_best = None
        # 批量初始化
        self.particles = [Particle(i,
                                   base_stations_scale,
                                   self.distance_topology,budget,t_max,
                                   initial_basestations,
                                   cross_proability, mutation_proability)
                          for i in range(int(pop*(1+two_party_num_rate)+pop))]
        # 更新pbest
        for p in self.particles:
            p.init_pbest_vector()
        self.g_best = Gbest_vector()
        self.g_best.update(self.particles)
        self.particles = sorted(self.particles, key=lambda individual: individual.fitness_now, reverse=False)
        best_fitness = []
        # 更新粒子
        for generation in range(self.MAX_GENERATION):
            print("\n\n==============覆盖半径", t_max, '  基站数目', self.base_station_num, "  第", generation,
                  "代==================")
            # 目标函数列表
            count = 0
            pop_list = [i for i in range(pop)]
            elite_group = self.particles[:int(0.3 * pop)]
            two_party = random.sample(pop_list,int(pop * two_party_num_rate))
            for num,p in enumerate(self.particles[:pop]):
                self.particles[int(pop*(1+two_party_num_rate))+num].vectorX = deepcopy(p.vectorX)
                self.particles[int(pop*(1+two_party_num_rate))+num].vectorY = deepcopy(p.vectorY)
                self.particles[int(pop*(1+two_party_num_rate))+num].norm_delay = deepcopy(p.norm_delay)
                self.particles[int(pop*(1+two_party_num_rate))+num].norm_workload = deepcopy(p.norm_workload)
                self.particles[int(pop*(1+two_party_num_rate))+num].fitness_now = deepcopy(p.fitness_now)
                self.particles[int(pop*(1+two_party_num_rate))+num].pbest = deepcopy(p.pbest)
            for num, particle in enumerate(self.particles):
                if num < int(0.3 * pop):
                    if num in two_party:
                        vectorX1, vectorX2, vectorY1, vectorY2 = particle.crossover(self.g_best)
                        particle.vectorX = vectorX1
                        particle.vectorY = vectorY1
                        self.particles[pop+count].vectorX = vectorX2
                        self.particles[pop+count].vectorY = vectorY2
                        particle.mutation()
                        wrong_x, wrong_y, flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector1(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        self.particles[pop + count].mutation()
                        wrong_x, wrong_y,flag = self.particles[pop+count].check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            self.particles[pop+count].del_and_refill_vector1(wrong_x, wrong_y)
                        self.particles[pop+count].update_pbest_vector()
                        print("GAPSO第{0}个粒子X1的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                        print("GAPSO第{0}个粒子X2的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,self.particles[pop+count].fitness_now,self.particles[pop+count].norm_delay,self.particles[pop+count].norm_workload,len(self.particles[pop+count].edge_temp_workload)))
                        count += 1
                    else:
                        particle.evolution_vector_gapso(self.g_best)
                        particle.mutation()
                        wrong_x, wrong_y,flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector1(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        print("GAPSO第{0}个粒子的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                elif int(0.3 * pop) <= num < int(pop):
                    if num in two_party:
                        elite_particle = random.sample(elite_group, 1)[0]
                        vectorX1, vectorX2, vectorY1, vectorY2 = particle.crossover(elite_particle)
                        particle.vectorX = vectorX1
                        particle.vectorY = vectorY1
                        self.particles[pop + count].vectorX = vectorX2
                        self.particles[pop + count].vectorY = vectorY2
                        particle.mutation()
                        wrong_x, wrong_y, flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector1(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        self.particles[pop + count].mutation()
                        wrong_x, wrong_y, flag = self.particles[pop + count].check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            self.particles[pop + count].del_and_refill_vector1(wrong_x, wrong_y)
                        self.particles[pop + count].update_pbest_vector()
                        print("GAPSO第{0}个粒子X1的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                        print("GAPSO第{0}个粒子X2的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,self.particles[pop + count].fitness_now,self.particles[pop + count].norm_delay,self.particles[pop + count].norm_workload,len(self.particles[pop + count].edge_temp_workload)))
                        count += 1
                    else:
                        particle.evolution_vector_gapso(self.g_best)
                        particle.mutation()
                        wrong_x, wrong_y ,flag= particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector1(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        print("GAPSO第{0}个粒子的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
            self.particles = sorted(self.particles, key=lambda individual: individual.fitness_now, reverse=False)
            self.g_best.update(self.particles)
            best_fitness.append(self.g_best.fitness)
            print("GAPSO第{0}次迭代的best_fitness:{1},nordelay:{2},norworkload:{3}".format(generation, best_fitness[generation], self.g_best.norm_delay, self.g_best.norm_workload))
            if (generation + 1) % 10 == 0:
                file = open('iter/resultsGAPSO.txt', 'a')
                file.write("N={0},t_max={1},GAPSO第{2}次,seed={6}迭代的best_fitness:{3},nordelay:{4},norworkload:{5}\n".format(
                    self.base_station_num, t_max,generation + 1, best_fitness[generation],
                    self.g_best.norm_delay, self.g_best.norm_workload,seed))
                file.flush()
                file.close()
        # 把结果翻译成标准输出
        edge_servers = []
        count_edge_id = 0
        placement_scheme = self.g_best.placement_scheme
        self.vectorY = self.g_best.vectorY
        for edge in placement_scheme:
            at_bs = base_stations_scale[edge['at_bs_No']]
            edge_temp = EdgeServer(count_edge_id, at_bs.latitude, at_bs.longitude, edge['max_workload'],
                                   edge['at_bs_No'])
            edge_temp.workload = edge['workload']
            edge_temp.assigned_base_stations = edge['serving_bs']
            count_edge_id += 1
            edge_servers.append(edge_temp)
        self.edge_servers = edge_servers
        # ==============================验证bs分配状况==========================================
        recorded_bs = set()
        recorded_list = []
        self.remote_cloud = [0 for i in range(self.base_station_num)]
        for edge in self.edge_servers:
            for bs in edge.assigned_base_stations:
                recorded_bs.add(bs.id)
                recorded_list.append(bs.id)
        for bs in range(self.base_station_num):
            if self.vectorY[bs] == self.base_station_num:
                self.remote_cloud[bs] = 1
                recorded_bs.add(bs)
                recorded_list.append(bs)
        if len(recorded_bs) != base_station_num and len(recorded_list) != base_station_num:
            raise Exception('未将所有bs正确分配')
        # ==============================验证bs分配状况==========================================
        logging.info("{0}:End running TopFirst ".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        # plt.xlabel('generation')
        # plt.ylabel('fitness')
        # plt.plot(range(0, self.MAX_GENERATION, 1), best_fitness)
        # plt.savefig(
        #     './final/GAPSO_{}basestation_{}iter_{}pop_{}seed.png'.format(base_station_num, self.MAX_GENERATION, pop,seed))
        # plt.close()

class GAPSO0ServerPlacer(ServerPlacer):

    def place_server(self, base_station_num,budget,t_max,rate,initial_basestations, cross_proability, mutation_proability,seed):
        random.seed(seed)
        base_stations_scale = deepcopy(self.base_stations)
        for bs in base_stations_scale:
            bs.workload = bs.workload * rate
        self.base_station_num = base_station_num
        self.particles = []
        self.g_best = None
        # 批量初始化
        self.particles = [Particle(i,
                                   base_stations_scale,
                                   self.distance_topology,budget,t_max,
                                   initial_basestations,
                                   cross_proability, mutation_proability)
                          for i in range(int(pop*(1+two_party_num_rate)))]
        # 更新pbest
        for p in self.particles:
            p.init_pbest_vector()
        self.g_best = Gbest_vector()
        self.g_best.update(self.particles)
        self.particles = sorted(self.particles, key=lambda individual: individual.fitness_now, reverse=False)
        best_fitness = []
        # 更新粒子
        for generation in range(self.MAX_GENERATION):
            print("\n\n==============覆盖半径", t_max, '  基站数目', self.base_station_num, "  第", generation,
                  "代==================")
            # 目标函数列表
            count = 0
            pop_list = [i for i in range(pop)]
            elite_group = self.particles[:int(0.3 * pop)]
            two_party = random.sample(pop_list,int(pop * two_party_num_rate))
            for num, particle in enumerate(self.particles):
                if num < int(0.3 * pop):
                    if num in two_party:
                        vectorX1, vectorX2, vectorY1, vectorY2 = particle.crossover(self.g_best)
                        particle.vectorX = vectorX1
                        particle.vectorY = vectorY1
                        self.particles[pop+count].vectorX = vectorX2
                        self.particles[pop+count].vectorY = vectorY2
                        particle.mutation()
                        wrong_x, wrong_y, flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        self.particles[pop + count].mutation()
                        wrong_x, wrong_y,flag = self.particles[pop+count].check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            self.particles[pop+count].del_and_refill_vector(wrong_x, wrong_y)
                        self.particles[pop+count].update_pbest_vector()
                        print("GAPSO0第{0}个粒子X1的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                        print("GAPSO0第{0}个粒子X2的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,self.particles[pop+count].fitness_now,self.particles[pop+count].norm_delay,self.particles[pop+count].norm_workload,len(self.particles[pop+count].edge_temp_workload)))
                        count += 1
                    else:
                        particle.evolution_vector_gapso(self.g_best)
                        particle.mutation()
                        wrong_x, wrong_y,flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        print("GAPSO0第{0}个粒子的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                elif int(0.3 * pop) <= num < int(pop):
                    if num in two_party:
                        elite_particle = random.sample(elite_group, 1)[0]
                        vectorX1, vectorX2, vectorY1, vectorY2 = particle.crossover(elite_particle)
                        particle.vectorX = vectorX1
                        particle.vectorY = vectorY1
                        self.particles[pop + count].vectorX = vectorX2
                        self.particles[pop + count].vectorY = vectorY2
                        particle.mutation()
                        wrong_x, wrong_y, flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        self.particles[pop + count].mutation()
                        wrong_x, wrong_y, flag = self.particles[pop + count].check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            self.particles[pop + count].del_and_refill_vector(wrong_x, wrong_y)
                        self.particles[pop + count].update_pbest_vector()
                        print("GAPSO0第{0}个粒子X1的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                        print("GAPSO0第{0}个粒子X2的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,self.particles[pop + count].fitness_now,self.particles[pop + count].norm_delay,self.particles[pop + count].norm_workload,len(self.particles[pop + count].edge_temp_workload)))
                        count += 1
                    else:
                        particle.evolution_vector_gapso(self.g_best)
                        particle.mutation()
                        wrong_x, wrong_y ,flag= particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        print("GAPSO0第{0}个粒子的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
            self.particles = sorted(self.particles, key=lambda individual: individual.fitness_now, reverse=False)
            self.g_best.update(self.particles)
            best_fitness.append(self.g_best.fitness)
            print("GAPSO0第{0}次迭代的best_fitness:{1},nordelay:{2},norworkload:{3}".format(generation, best_fitness[generation], self.g_best.norm_delay, self.g_best.norm_workload))
            if (generation + 1) % 10 == 0:
                file = open('iter/resultsGAPSO0.txt', 'a')
                file.write("N={0},t_max={1},GAPSO0第{2}次,seed={6}迭代的best_fitness:{3},nordelay:{4},norworkload:{5}\n".format(
                    self.base_station_num, t_max,generation + 1, best_fitness[generation],
                    self.g_best.norm_delay, self.g_best.norm_workload,seed))
                file.flush()
                file.close()
        # 把结果翻译成标准输出
        edge_servers = []
        count_edge_id = 0
        placement_scheme = self.g_best.placement_scheme
        self.vectorY = self.g_best.vectorY
        for edge in placement_scheme:
            at_bs = base_stations_scale[edge['at_bs_No']]
            edge_temp = EdgeServer(count_edge_id, at_bs.latitude, at_bs.longitude, edge['max_workload'],
                                   edge['at_bs_No'])
            edge_temp.workload = edge['workload']
            edge_temp.assigned_base_stations = edge['serving_bs']
            count_edge_id += 1
            edge_servers.append(edge_temp)
        self.edge_servers = edge_servers
        # ==============================验证bs分配状况==========================================
        recorded_bs = set()
        recorded_list = []
        self.remote_cloud = [0 for i in range(self.base_station_num)]
        for edge in self.edge_servers:
            for bs in edge.assigned_base_stations:
                recorded_bs.add(bs.id)
                recorded_list.append(bs.id)
        for bs in range(self.base_station_num):
            if self.vectorY[bs] == self.base_station_num:
                self.remote_cloud[bs] = 1
                recorded_bs.add(bs)
                recorded_list.append(bs)
        if len(recorded_bs) != base_station_num and len(recorded_list) != base_station_num:
            raise Exception('未将所有bs正确分配')
        # ==============================验证bs分配状况==========================================
        logging.info("{0}:End running TopFirst ".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        # plt.xlabel('generation')
        # plt.ylabel('fitness')
        # plt.plot(range(0, self.MAX_GENERATION, 1), best_fitness)
        # plt.savefig(
        #     './final/GAPSO0_{}basestation_{}iter_{}pop_{}seed.png'.format(base_station_num, self.MAX_GENERATION, pop,seed))
        # plt.close()


class GAPSO1ServerPlacer(ServerPlacer):

    def place_server(self, base_station_num,budget,t_max,rate,initial_basestations, cross_proability, mutation_proability,seed):
        random.seed(seed)
        base_stations_scale = deepcopy(self.base_stations)
        for bs in base_stations_scale:
            bs.workload = bs.workload * rate
        self.base_station_num = base_station_num
        self.particles = []
        self.g_best = None
        # 批量初始化
        self.particles = [Particle(i,
                                   base_stations_scale,
                                   self.distance_topology,budget,t_max,
                                   initial_basestations,
                                   cross_proability, mutation_proability)
                          for i in range(int(pop*(1+two_party_num_rate1)))]
        # 更新pbest
        for p in self.particles:
            p.init_pbest_vector()
        self.g_best = Gbest_vector()
        self.g_best.update(self.particles)
        self.particles = sorted(self.particles, key=lambda individual: individual.fitness_now, reverse=False)
        best_fitness = []
        # 更新粒子
        for generation in range(self.MAX_GENERATION):
            print("\n\n==============覆盖半径", t_max, '  基站数目', self.base_station_num, "  第", generation,
                  "代==================")
            # 目标函数列表
            count = 0
            pop_list = [i for i in range(pop)]
            elite_group = self.particles[:int(0.3 * pop)]
            two_party = random.sample(pop_list,int(pop * two_party_num_rate1))
            for num, particle in enumerate(self.particles):
                if num < int(0.3 * pop):
                    if num in two_party:
                        vectorX1, vectorX2, vectorY1, vectorY2 = particle.crossover(self.g_best)
                        particle.vectorX = vectorX1
                        particle.vectorY = vectorY1
                        self.particles[pop+count].vectorX = vectorX2
                        self.particles[pop+count].vectorY = vectorY2
                        particle.mutation()
                        wrong_x, wrong_y, flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        self.particles[pop + count].mutation()
                        wrong_x, wrong_y,flag = self.particles[pop+count].check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            self.particles[pop+count].del_and_refill_vector(wrong_x, wrong_y)
                        self.particles[pop+count].update_pbest_vector()
                        print("GAPSO1第{0}个粒子X1的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                        print("GAPSO1第{0}个粒子X2的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,self.particles[pop+count].fitness_now,self.particles[pop+count].norm_delay,self.particles[pop+count].norm_workload,len(self.particles[pop+count].edge_temp_workload)))
                        count += 1
                    else:
                        particle.evolution_vector_gapso(self.g_best)
                        particle.mutation()
                        wrong_x, wrong_y,flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        print("GAPSO1第{0}个粒子的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                elif int(0.3 * pop) <= num < int(pop):
                    if num in two_party:
                        elite_particle = random.sample(elite_group, 1)[0]
                        vectorX1, vectorX2, vectorY1, vectorY2 = particle.crossover(elite_particle)
                        particle.vectorX = vectorX1
                        particle.vectorY = vectorY1
                        self.particles[pop + count].vectorX = vectorX2
                        self.particles[pop + count].vectorY = vectorY2
                        particle.mutation()
                        wrong_x, wrong_y, flag = particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        self.particles[pop + count].mutation()
                        wrong_x, wrong_y, flag = self.particles[pop + count].check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            self.particles[pop + count].del_and_refill_vector(wrong_x, wrong_y)
                        self.particles[pop + count].update_pbest_vector()
                        print("GAPSO1第{0}个粒子X1的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
                        print("GAPSO1第{0}个粒子X2的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,self.particles[pop + count].fitness_now,self.particles[pop + count].norm_delay,self.particles[pop + count].norm_workload,len(self.particles[pop + count].edge_temp_workload)))
                        count += 1
                    else:
                        particle.evolution_vector_gapso(self.g_best)
                        particle.mutation()
                        wrong_x, wrong_y ,flag= particle.check_particles_vector(model=True)
                        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
                            particle.del_and_refill_vector(wrong_x, wrong_y)
                        particle.update_pbest_vector()
                        print("GAPSO1第{0}个粒子的fitness_now:{1},nordelay:{2},norworkload:{3},edge_num:{4}".format(num,particle.fitness_now,particle.norm_delay,particle.norm_workload,len(particle.edge_temp_workload)))
            self.particles = sorted(self.particles, key=lambda individual: individual.fitness_now, reverse=False)
            self.g_best.update(self.particles)
            best_fitness.append(self.g_best.fitness)
            print("GAPSO1第{0}次迭代的best_fitness:{1},nordelay:{2},norworkload:{3}".format(generation, best_fitness[generation], self.g_best.norm_delay, self.g_best.norm_workload))
            if (generation + 1) % 10 == 0:
                file = open('iter/resultsGAPSO1.txt', 'a')
                file.write("N={0},t_max={1},GAPSO1第{2}次,seed={6}迭代的best_fitness:{3},nordelay:{4},norworkload:{5}\n".format(
                    self.base_station_num, t_max,generation + 1, best_fitness[generation],
                    self.g_best.norm_delay, self.g_best.norm_workload,seed))
                file.flush()
                file.close()
        # 把结果翻译成标准输出
        edge_servers = []
        count_edge_id = 0
        placement_scheme = self.g_best.placement_scheme
        self.vectorY = self.g_best.vectorY
        for edge in placement_scheme:
            at_bs = base_stations_scale[edge['at_bs_No']]
            edge_temp = EdgeServer(count_edge_id, at_bs.latitude, at_bs.longitude, edge['max_workload'],
                                   edge['at_bs_No'])
            edge_temp.workload = edge['workload']
            edge_temp.assigned_base_stations = edge['serving_bs']
            count_edge_id += 1
            edge_servers.append(edge_temp)
        self.edge_servers = edge_servers
        # ==============================验证bs分配状况==========================================
        recorded_bs = set()
        recorded_list = []
        self.remote_cloud = [0 for i in range(self.base_station_num)]
        for edge in self.edge_servers:
            for bs in edge.assigned_base_stations:
                recorded_bs.add(bs.id)
                recorded_list.append(bs.id)
        for bs in range(self.base_station_num):
            if self.vectorY[bs] == self.base_station_num:
                self.remote_cloud[bs] = 1
                recorded_bs.add(bs)
                recorded_list.append(bs)
        if len(recorded_bs) != base_station_num and len(recorded_list) != base_station_num:
            raise Exception('未将所有bs正确分配')
        # ==============================验证bs分配状况==========================================
        logging.info("{0}:End running TopFirst ".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        # plt.xlabel('generation')
        # plt.ylabel('fitness')
        # plt.plot(range(0, self.MAX_GENERATION, 1), best_fitness)
        # plt.savefig(
        #     './final/GAPSO1_{}basestation_{}iter_{}pop_{}seed.png'.format(base_station_num, self.MAX_GENERATION, pop,seed))
        # plt.close()




