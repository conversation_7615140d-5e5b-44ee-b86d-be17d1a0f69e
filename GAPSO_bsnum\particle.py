import config
import edge_server
import utils
from edge_server import EdgeServer
from utils import Random_Placement_Set
import numpy as np
import math
from config import *
from base_station import BaseStation
from typing import List
from utils import DataUtils
import random
# from config import *
from copy import deepcopy


class Particle(object):  # 粒子类

    def __init__(self, particle_id: int, bs_list: List[BaseStation], distances: List[List[float]],budget,t_max,initial_basestations,cross_proability, mutation_proability,):

        #  自身数据
        self.id = particle_id
        self.bs_list = bs_list
        self.distance_topology = distances
        self.n = len(bs_list)
        self.V = []
        self.pbest = {}
        self.workload = 0
        self.fitness_now = 0
        self.initial_basestations = initial_basestations
        self.budget = budget
        self.t_max = t_max
        self.cross_proability = cross_proability
        self.mutation_proability = mutation_proability
        # ===============粒子初始化=======================
        self.randam_init_vector()

    def get_cost_init(self,bs,max_workload):
        dis_with_city_center = utils.calc_distance(bs.latitude, bs.longitude)
        rental_cost = distance_to_cost * math.log10(dis_with_city_center) + fix_cost
        exp_cost = install_cost * (max_workload/single_max_workload)
        return rental_cost+exp_cost

    def get_total_cost_init(self,edge_servers,init_basestations):
        movable_edge_server_id = []
        bs_cost = 0
        for edge in edge_servers:
            if edge.base_station_id not in init_basestations.keys():
                movable_edge_server_id.append(edge.base_station_id)
        for id in movable_edge_server_id:  # 新添加的边缘服务器的花费
            for edge in edge_servers:
                if edge.base_station_id == id:
                    bs_cost += self.get_cost_init(self.bs_list[id],edge.max_workload)
        for key, values in init_basestations.items():  # 老的边缘服务器的花费
            for edge in edge_servers:
                if key == edge.base_station_id:
                    bs_cost += install_cost * ((edge.max_workload-values) / single_max_workload)
        return bs_cost


    def randam_init_vector(self):
        self.edge_temp_workload = {}
        self.edge_servers = None
        self.vectorX = [0 for i in range(self.n+1)]
        self.vectorY = [0 for i in range(self.n)]
        self.V = [True if random.random() < 0.5 else False for i in range(self.n)]
        unplaced_set = Random_Placement_Set(self.n)
        edge_servers = []
        count = 0
        for key,value in self.initial_basestations.items():
            unplaced_set.remove(key)
            edge_servers.append(EdgeServer(count, self.bs_list[key].latitude, self.bs_list[key].longitude, value, self.bs_list[key].id))
            count += 1
        count_edge_id = len(self.initial_basestations.keys())
        while self.get_total_cost_init(edge_servers,self.initial_basestations) < self.budget:  # 新放置边缘服务器
            distance_dict = {}
            total_dis_dict = {}
            cloud = [0 for i in range(self.n)]
            e = unplaced_set.pop()
            bs_e = self.bs_list[e]
            edge_temp = EdgeServer(count_edge_id, bs_e.latitude, bs_e.longitude,single_max_workload, bs_e.id)
            count_edge_id += 1
            edge_servers.append(edge_temp)
            for id, edge in enumerate(edge_servers):
                edge.assigned_base_stations = []
                edge.workload = 0
                edge.assigned_base_stations.append(self.bs_list[edge.base_station_id])  # 加入分配队列
                edge.workload = self.bs_list[edge.base_station_id].workload  # 更新edge负载
                if id < len(self.initial_basestations.values()):
                    edge.max_workload = list(self.initial_basestations.values())[id]
                else:
                    edge.max_workload = single_max_workload
            for bs_num in unplaced_set:
                for edge in edge_servers:
                    tmp = self.get_bs_distance(edge.base_station_id, self.bs_list[bs_num].id)
                    distance_dict[edge.id] = tmp
                sorted_distance_list = sorted(distance_dict.items(), key=lambda x: x[1], reverse=False)
                total_dis_dict[bs_num] = sorted_distance_list
            unplaced_list = list(unplaced_set)
            for num, bs_num in enumerate(unplaced_list):
                distance_k = {}
                for i in total_dis_dict[bs_num]:  # 基站连接
                    if i[1] < self.t_max:
                        if (edge_servers[i[0]].workload + self.bs_list[bs_num].workload) / edge_servers[i[0]].max_workload < 1:
                            distance_k[i[0]] = 1 * i[1] / self.t_max + edge_servers[i[0]].workload / edge_servers[i[0]].max_workload
                    else:
                        break
                sorted_distance_k = sorted(distance_k.items(), key=lambda x: x[1], reverse=False)
                if len(sorted_distance_k) == 0:
                    cloud[bs_num] = 1
                else:
                    link_edge = random.sample(sorted_distance_k[:sample_num], 1)
                    link_edge_num = link_edge[0][0]
                    if (edge_servers[link_edge_num].workload + self.bs_list[bs_num].workload) / edge_servers[link_edge_num].max_workload > th and edge_servers[link_edge_num].max_workload < c_max * single_max_workload:
                        edge_servers[link_edge_num].max_workload += single_max_workload
                    edge_servers[link_edge_num].assigned_base_stations.append(self.bs_list[bs_num])
                    edge_servers[link_edge_num].workload += self.bs_list[bs_num].workload
                    cloud[bs_num] = 0
            if self.get_total_cost_init(edge_servers,self.initial_basestations) < self.budget:
                self.edge_servers = deepcopy(edge_servers)
                self.cloud = deepcopy(cloud)
            else:
                break

        # ******************************************************************
        if self.edge_servers is None:
            self.randam_init_vector()
        for e in self.edge_servers:
            # 先将这个edge标记成已经放置
            self.vectorX[e.base_station_id] = int(e.max_workload/single_max_workload)
            # 开始修改矩阵，给edge分配bs
            for b in e.assigned_base_stations:
                self.vectorY[b.id] = e.base_station_id
        for bs in range(self.n):
            if self.cloud[bs] == 1:
                self.vectorY[bs] = self.n
        # ==============================验证bs分配状况==========================================
        recorded_bs = set()
        recorded_list = []
        for edge in self.edge_servers:
            for bs in edge.assigned_base_stations:
                recorded_bs.add(bs.id)
                recorded_list.append(bs.id)
        for bs in range(self.n):
            if self.vectorY[bs] == self.n:
                recorded_bs.add(bs)
                recorded_list.append(bs)
        if len(recorded_bs) != self.n and len(recorded_list) != self.n:
            raise Exception('未将所有bs正确分配')
        # ==============================验证bs分配状况==========================================
    # ================================end of __init()__========================================================

    def get_cost_vector(self,bs):
        dis_with_city_center = utils.calc_distance(bs.latitude, bs.longitude)
        edge_max_workload = self.vectorX[bs.id] * single_max_workload
        if dis_with_city_center < r_to_center:
            rental_cost = fix_cost
        else:
            rental_cost = distance_to_cost * math.log10(dis_with_city_center) + fix_cost
        exp_cost = install_cost * self.vectorX[bs.id]
        return rental_cost+exp_cost,edge_max_workload

    # 计算两个edge的距离
    def get_edge_distance(self, edge1, edge2):
        edge1_bs = self.bs_list[edge1].id
        edge2_bs = self.bs_list[edge2].id
        return self.distance_topology[edge1_bs][edge2_bs]

    # 计算连个基站之间的距离
    def get_bs_distance(self, bs1:int, bs2:int):
        return self.distance_topology[bs1][bs2]

    # 计算平均时延
    def get_average_delay(self, delay_list):
        return sum(delay_list) / len(delay_list)

    # 某一列，是service的话返回当前的负载，不是返回0
    def get_workload_of_edge_vector(self, i,vectorX,vectorY):
        if vectorX[i] == 0:
            return 0
        else:
            temp = 0
            for num, y in enumerate(vectorY):
                if (y == i) and (y is not False):
                    temp += self.bs_list[num].workload
        return temp

    # 查看当前放置方案(哪些edge放了service，每个service服务哪些edge，service的负载)
    def get_condition_vector(self):
        condition = []
        # 逐列遍历
        for i in range(self.n):
            bs = self.vectorX[i]
            edge_workload_temp = 0
            bs_list_temp = []
            if bs != 0:
                max_workload = self.vectorX[i] * single_max_workload
                for j in range(self.n):
                    if (self.vectorY[j] == i):
                        edge_workload_temp += self.bs_list[j].workload
                        bs_list_temp.append(self.bs_list[j])
                dict_temp = {'at_bs_No': i, 'serving_bs': bs_list_temp, 'workload': edge_workload_temp,
                             'max_workload': max_workload}
                condition.append(dict_temp)
        return condition
    
    def get_to_cloud_vector(self):
        remote_list = []
        for i in range(self.n):
            if self.vectorY[i] == self.n:
                remote_list.append(i)
        return remote_list

    def total_workloads__delay_list_vector(self):
        '''
        获取当前情况
        :return: total_energy，delay_list
        '''
        self.edge_temp_workload = {}
        total_workloads = 0
        delay_list = [-1 for k in range(self.n)]  # 记录所有基站到该边缘服务器的延迟
        delay_not_record = [i for i in range(self.n)]  # 记录哪些bs的时延没有被登记
        for i in range(self.n):
            if self.vectorX[i] != 0:  # 如果是edge
                load_temp = 0
                for j in range(self.n):  # 遍历所有与边缘服务器i相连的基站j
                    if (self.vectorY[j] == i):
                        load_temp += self.bs_list[j].workload
                        delay_list[j] = self.get_bs_distance(j, i)
                        delay_not_record.remove(j)
                total_workloads += load_temp  # 计算全部的负载
                self.edge_temp_workload[i] = load_temp/self.vectorX[i]
        for i in range(self.n):
            if self.vectorY[i] == self.n:
                delay_list[i] = remote_delay
                delay_not_record.remove(i)
        if len(delay_not_record) > 0:
            # self.randam_init_vector()
            raise Exception('delay list记录不完整')
        return total_workloads, delay_list


    def get_fitness(self):
        fitness = self.pbest['norm_delay'] + self.pbest['norm_workload']
        return fitness


    def get_total_cost_vector(self,vectorX=None):  # 得到全部花费
        if vectorX is None:
            movable_edge_server_id = []
            for i in range(self.n):
                if self.vectorX[i]:
                    if i not in self.initial_basestations.keys():
                        movable_edge_server_id.append(i)
            bs_cost = 0
            for id in movable_edge_server_id:  # 新添加的边缘服务器的花费
                bs_e_cost, max_workload = self.get_cost_vector(self.bs_list[id])
                bs_cost += bs_e_cost
            for key, values in self.initial_basestations.items():  # 老的边缘服务器的花费
                if self.vectorX[key] - (values / single_max_workload) > 0:
                    bs_e_cost = install_cost * (self.vectorX[key] - (values / single_max_workload))
                    bs_cost += bs_e_cost
        else:
            movable_edge_server_id = []
            for i in range(self.n):
                if vectorX[i]:
                    if i not in self.initial_basestations.keys():
                        movable_edge_server_id.append(i)
            bs_cost = 0
            for id in movable_edge_server_id:  # 新添加的边缘服务器的花费
                bs_e_cost, max_workload = self.get_cost_vector(self.bs_list[id])
                bs_cost += bs_e_cost
            for key, values in self.initial_basestations.items():  # 老的边缘服务器的花费
                if vectorX[key] - (values / single_max_workload) > 0:
                    bs_e_cost = install_cost * (vectorX[key] - (values / single_max_workload))
                    bs_cost += bs_e_cost
        return bs_cost

    # 初始化pbest
    def init_pbest_vector(self):
        total_workloads, delay_list = self.total_workloads__delay_list_vector()
        self.pbest['total_workloads'] = total_workloads
        self.pbest['vectorX'] = deepcopy(self.vectorX)
        self.pbest['vectorY'] = deepcopy(self.vectorY)
        self.pbest['average_delay'] = self.get_average_delay(delay_list)
        average_delay = self.pbest['average_delay']
        self.pbest['norm_delay'] = self.norm_delays(average_delay,delay_list)
        self.pbest['norm_workload'] = self.norm_workloads()
        self.pbest['fitness'] = self.get_fitness()
        self.norm_delay = self.pbest['norm_delay']
        self.norm_workload = self.pbest['norm_workload']
        self.fitness_now = self.pbest['norm_workload'] + self.pbest['norm_delay']

    def norm_delays(self,average_delay,delay_list):
        max_delay = max(delay_list)
        min_delay = min(delay_list)
        nor_item_list = []
        for item in delay_list:
            nor_item_list.append((item - min_delay) / (max_delay - min_delay))
        nor_delay1 = sum(nor_item_list) / len(nor_item_list)
        nor_delay2 = average_delay/self.t_max
        return nor_delay2

    #计算每个粒子的负载均衡的程度，并且归一化
    def norm_workloads(self):
        each_workload_std = 0
        edge_workload_list = []
        w_std = np.std(list(self.edge_temp_workload.values()))
        workloads = sum(self.edge_temp_workload.values())
        x = workloads / len(self.edge_temp_workload)  #每个边缘服务器的平均负载
        max_x = max(self.edge_temp_workload.values())
        min_x = min(self.edge_temp_workload.values())
        for item in self.edge_temp_workload.values():
            each_workload_std += abs((item - x)/x)
            nor_item = (item)/(max_x)
            edge_workload_list.append(nor_item)
        avg_std = each_workload_std / len(self.edge_temp_workload)
        edge_workload_list_std = np.std(edge_workload_list)
        return edge_workload_list_std

    # 一次放置计划整体的方差
    def workload_std(self):
        workloads = [e.workload for e in self.edge_servers]
        std = np.std(workloads)
        return std

    def average_workload(self):
        w = sum(self.edge_temp_workload.values()) / len(self.edge_temp_workload)
        return w

    def average_workload_srd(self):
        a_w_s = sum(self.edge_temp_workload.values()) / len(self.edge_temp_workload)
        return a_w_s

    # 更新pbest
    def update_pbest_vector(self):
        # ===========================优化profit==========================================
        total_workloads, delay_list = self.total_workloads__delay_list_vector()
        average_delay = self.get_average_delay(delay_list)
        self.norm_delay = self.norm_delays(average_delay,delay_list)
        self.norm_workload = self.norm_workloads()
        self.fitness_now = self.norm_delay + self.norm_workload
        if self.pbest['fitness'] > self.fitness_now:
            # total_workloads, delay_list = self.total_energy__delay_list()
            self.pbest['vectorX'] = deepcopy(self.vectorX)
            self.pbest['vectorY'] = deepcopy(self.vectorY)
            self.pbest['average_delay'] = self.get_average_delay(delay_list)
            average_delay = self.pbest['average_delay']
            self.pbest['norm_delay'] = self.norm_delays(average_delay,delay_list)
            self.pbest['norm_workload'] = self.norm_workloads()
            self.pbest['fitness'] = self.get_fitness()


    # 检查粒子是否合法，分别按照行列检查
    # 返回出错的行号
    def check_particles_vector(self,model):
        wrong_x = set()
        wrong_y = set()
        flag = False
        for num2, y in enumerate(self.vectorY):
            if model is True:  # 是否要将分配给远程云的基站设置为违规
                if self.vectorX[self.vectorY[num2]] == 0:
                    wrong_y.add(num2)
            else:
                if self.vectorX[self.vectorY[num2]] == 0 and y != self.n:
                    wrong_y.add(num2)
        for num1, x in enumerate(self.vectorX):
            if self.vectorX[num1] != 0:
                if self.vectorY[num1] != num1:
                    # wrong_x.add(num1)
                    wrong_y.add(num1)
                    for num4, y in enumerate(self.vectorY):  # 对于Y向量，若Y[num]=y，那么X[y]存在标记
                        if y == num1:
                            wrong_y.add(num4)
                if self.get_workload_of_edge_vector(num1,self.vectorX,self.vectorY) > x * single_max_workload:
                    # wrong_x.add(num1)
                    for num3, y in enumerate(self.vectorY):  # 对于Y向量，若Y[num]=y，那么X[y]存在标记
                        if self.vectorY[num3] == num1:
                            wrong_y.add(num3)
        if self.get_total_cost_vector() > self.budget:
            flag = True
        return wrong_x,wrong_y,flag


    def del_and_refill_vector(self, wrong_x, wrong_y):
        count = 0
        movable_edge_server_id = []
        unplace_basestation = [i for i in range(self.n)]
        edge_workload_list = []
        for i in range(self.n):
            if self.vectorX[i]:
                edge_workload = self.get_workload_of_edge_vector(i, self.vectorX, self.vectorY)
                edge_workload_list.append(edge_workload / self.vectorX[i])
        workload_avg = sum(edge_workload_list) / len(edge_workload_list)
        # for wx in wrong_x:   # 去除违规的向量x中的元素
        #     if wx in self.initial_basestations.keys():
        #         self.vectorX[wx] = int(self.initial_basestations.get(wx) / single_max_workload)
        #     else:
        #         self.vectorX[wx] = 0
        # for wy in wrong_y:  # 去除违规的向量y中的元素
        #     if wy in self.initial_basestations.keys():
        #         self.vectorY[wy] = wy
        #     else:
        #         self.vectorY[wy] = False
        for wy in wrong_y:  # 去除违规的向量y中的元素
            self.vectorY[wy] = False
        for i in range(self.n):
            if self.vectorX[i] != 0:
                self.vectorY[i] = i
                if i not in self.initial_basestations.keys():
                    movable_edge_server_id.append(i)
            if i in self.initial_basestations.keys():
                self.vectorX[i] = max(int(self.get_workload_of_edge_vector(i, self.vectorX,self.vectorY) / single_max_workload) + 1,
                                      int(self.initial_basestations.get(i) / single_max_workload))
                self.vectorY[i] = i
        for i in movable_edge_server_id:
            if self.get_workload_of_edge_vector(i,self.vectorX, self.vectorY) / self.vectorX[i] < 0.5 * workload_avg and self.vectorX[i] > 1 \
                    and self.get_workload_of_edge_vector(i, self.vectorX, self.vectorY) < (self.vectorX[i] - 1) * single_max_workload:
                self.vectorX[i] -= 1
        init_vectorX = deepcopy(self.vectorX)
        init_vectorY = deepcopy(self.vectorY)
        init_movable_edge_server_id = deepcopy(movable_edge_server_id)
        global vectorX1
        global vectorY1
        if self.get_total_cost_vector() > self.budget:
            vectorX1 = None
            vectorY1 = None
            while self.get_total_cost_vector() > self.budget:
                e = random.sample(movable_edge_server_id, 1)[0]
                movable_edge_server_id.remove(e)
                total_list = movable_edge_server_id + list(self.initial_basestations.keys())
                self.vectorX[e] = 0
                for i in range(self.n):
                    if self.vectorY[i] == e:
                        self.vectorY[i] = False
                vectorX = deepcopy(self.vectorX)
                vectorY = deepcopy(self.vectorY)
                for num, y in enumerate(vectorY):
                    if y is False:
                        F_value = {}
                        for edge in total_list:
                            distance = self.get_bs_distance(num, edge)
                            workload = self.get_workload_of_edge_vector(edge, vectorX, vectorY)
                            if distance < self.t_max and workload + self.bs_list[num].workload < vectorX[edge] * single_max_workload:
                                F_value[edge] = w1 * distance / self.t_max + workload / (vectorX[edge] * single_max_workload)
                        sorted_F_value = sorted(F_value.items(), key=lambda x: x[1], reverse=False)
                        if len(sorted_F_value) == 0:
                            vectorY[num] = self.n
                        else:
                            link_edge = random.sample(sorted_F_value[:sample_num], 1)
                            link_edge_num = link_edge[0][0]
                            workload = self.get_workload_of_edge_vector(link_edge_num, vectorX, vectorY)
                            if (workload + self.bs_list[num].workload) / (vectorX[link_edge_num] * single_max_workload) > th and vectorX[
                                link_edge_num] < c_max and self.get_total_cost_vector(vectorX) + install_cost <= self.budget:
                                vectorX[link_edge_num] += 1
                            vectorY[num] = link_edge_num
                self.vectorX = deepcopy(vectorX)  # 拷贝X，用于下面判断是否超出预算
                if self.get_total_cost_vector() <= self.budget:
                    vectorX1 = deepcopy(vectorX)
                    vectorY1 = deepcopy(vectorY)
            self.vectorX = deepcopy(vectorX1)
            self.vectorY = deepcopy(vectorY1)

        else:
            vectorX1 = None
            vectorY1 = None
            while self.get_total_cost_vector() <= self.budget:
                e = random.sample(list(set(unplace_basestation) - set(list(self.initial_basestations.keys()) + movable_edge_server_id)),1)[0]
                movable_edge_server_id.append(e)
                self.vectorX[e] = 1
                self.vectorY[e] = e
                vectorX = deepcopy(self.vectorX)
                vectorY = deepcopy(self.vectorY)
                count += 1
                for num, y in enumerate(vectorY):
                    if y is False:
                        F_value = {}
                        for edge in (movable_edge_server_id + list(self.initial_basestations.keys())):
                            distance = self.get_bs_distance(num, edge)
                            workload = self.get_workload_of_edge_vector(edge, vectorX, vectorY)
                            if distance < self.t_max and workload + self.bs_list[num].workload < vectorX[edge] * single_max_workload:
                                F_value[edge] = w1 * distance / self.t_max + workload / (vectorX[edge] * single_max_workload)
                        sorted_F_value = sorted(F_value.items(), key=lambda x: x[1], reverse=False)
                        if len(sorted_F_value) == 0:
                            vectorY[num] = self.n
                        else:
                            link_edge = random.sample(sorted_F_value[:sample_num], 1)
                            link_edge_num = link_edge[0][0]
                            workload = self.get_workload_of_edge_vector(link_edge_num, vectorX, vectorY)
                            if (workload + self.bs_list[num].workload) / (vectorX[link_edge_num] * single_max_workload) > th and vectorX[
                                link_edge_num] < c_max and self.get_total_cost_vector(vectorX) + install_cost <= self.budget:
                                vectorX[link_edge_num] += 1
                            vectorY[num] = link_edge_num
                self.vectorX = deepcopy(vectorX)  # 拷贝X，用于下面判断是否超出预算
                if self.get_total_cost_vector() <= self.budget:
                    vectorX1 = deepcopy(vectorX)
                    vectorY1 = deepcopy(vectorY)
            if count != 1:  # 执行次数大于1，表示vectorX1才是小于预算的解
                self.vectorX = deepcopy(vectorX1)
                self.vectorY = deepcopy(vectorY1)
            if count == 1:  # 执行次数等于1，表示第一次进入，就超出预算，没有进入最后的if self.get_total_cost_vector() <= self.budget判断语句
                self.vectorX = deepcopy(init_vectorX)
                self.vectorY = deepcopy(init_vectorY)
                for num, y in enumerate(self.vectorY):
                    if y is False:
                        F_value = {}
                        for edge in (init_movable_edge_server_id + list(self.initial_basestations.keys())):
                            distance = self.get_bs_distance(num, edge)
                            workload = self.get_workload_of_edge_vector(edge, self.vectorX, self.vectorY)
                            if distance < self.t_max and workload + self.bs_list[num].workload < self.vectorX[edge] * single_max_workload:
                                F_value[edge] = w1 * distance / self.t_max + workload / (self.vectorX[edge] * single_max_workload)
                        sorted_F_value = sorted(F_value.items(), key=lambda x: x[1], reverse=False)
                        if len(sorted_F_value) == 0:
                            self.vectorY[num] = self.n
                        else:
                            link_edge = random.sample(sorted_F_value[:sample_num], 1)
                            link_edge_num = link_edge[0][0]
                            self.vectorY[num] = link_edge_num
        wrong_x, wrong_y, flag = self.check_particles_vector(model=False)
        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
            # self.randam_init_vector()
            raise Exception('wrong')

    def del_and_refill_vector1(self, wrong_x, wrong_y):
        count = 0
        movable_edge_server_id = []
        unplace_basestation = [i for i in range(self.n)]
        edge_workload_list = []
        for i in range(self.n):
            if self.vectorX[i]:
                edge_workload = self.get_workload_of_edge_vector(i, self.vectorX, self.vectorY)
                edge_workload_list.append(edge_workload / self.vectorX[i])
        workload_avg = sum(edge_workload_list) / len(edge_workload_list)
        # for wx in wrong_x:   # 去除违规的向量x中的元素
        #     if wx in self.initial_basestations.keys():
        #         self.vectorX[wx] = int(self.initial_basestations.get(wx) / single_max_workload)
        #     else:
        #         self.vectorX[wx] = 0
        # for wy in wrong_y:  # 去除违规的向量y中的元素
        #     if wy in self.initial_basestations.keys():
        #         self.vectorY[wy] = wy
        #     else:
        #         self.vectorY[wy] = False
        for wy in wrong_y:  # 去除违规的向量y中的元素
            self.vectorY[wy] = False
        for i in range(self.n):
            if self.vectorX[i] != 0:
                self.vectorY[i] = i
                if i not in self.initial_basestations.keys():
                    movable_edge_server_id.append(i)
            if i in self.initial_basestations.keys():
                self.vectorX[i] = max(int(self.get_workload_of_edge_vector(i, self.vectorX,self.vectorY) / single_max_workload) + 1,
                                      int(self.initial_basestations.get(i) / single_max_workload))
                self.vectorY[i] = i
        for i in movable_edge_server_id:
            if self.get_workload_of_edge_vector(i,self.vectorX, self.vectorY) / self.vectorX[i] < 0.5 * workload_avg and self.vectorX[i] > 1 \
                    and self.get_workload_of_edge_vector(i, self.vectorX, self.vectorY) < (self.vectorX[i] - 1) * single_max_workload:
                self.vectorX[i] -= 1
        init_vectorX = deepcopy(self.vectorX)
        init_vectorY = deepcopy(self.vectorY)
        init_movable_edge_server_id = deepcopy(movable_edge_server_id)
        global vectorX1
        global vectorY1
        if self.get_total_cost_vector() > self.budget:
            vectorX1 = None
            vectorY1 = None
            while self.get_total_cost_vector() > self.budget:
                e = random.sample(movable_edge_server_id, 1)[0]
                movable_edge_server_id.remove(e)
                total_list = movable_edge_server_id + list(self.initial_basestations.keys())
                self.vectorX[e] = 0
                for i in range(self.n):
                    if self.vectorY[i] == e:
                        self.vectorY[i] = False
                vectorX = deepcopy(self.vectorX)
                vectorY = deepcopy(self.vectorY)
                for num, y in enumerate(vectorY):
                    if y is False:
                        F_value = {}
                        for edge in total_list:
                            distance = self.get_bs_distance(num, edge)
                            workload = self.get_workload_of_edge_vector(edge, vectorX, vectorY)
                            if distance < self.t_max and workload + self.bs_list[num].workload < vectorX[edge] * single_max_workload:
                                F_value[edge] = w2 * distance / self.t_max + workload / (vectorX[edge] * single_max_workload)
                        sorted_F_value = sorted(F_value.items(), key=lambda x: x[1], reverse=False)
                        if len(sorted_F_value) == 0:
                            vectorY[num] = self.n
                        else:
                            link_edge = random.sample(sorted_F_value[:sample_num], 1)
                            link_edge_num = link_edge[0][0]
                            workload = self.get_workload_of_edge_vector(link_edge_num, vectorX, vectorY)
                            if (workload + self.bs_list[num].workload) / (vectorX[link_edge_num] * single_max_workload) > th and vectorX[
                                link_edge_num] < c_max and self.get_total_cost_vector(vectorX) + install_cost <= self.budget:
                                vectorX[link_edge_num] += 1
                            vectorY[num] = link_edge_num
                self.vectorX = deepcopy(vectorX)  # 拷贝X，用于下面判断是否超出预算
                if self.get_total_cost_vector() <= self.budget:
                    vectorX1 = deepcopy(vectorX)
                    vectorY1 = deepcopy(vectorY)
            self.vectorX = deepcopy(vectorX1)
            self.vectorY = deepcopy(vectorY1)

        else:
            vectorX1 = None
            vectorY1 = None
            while self.get_total_cost_vector() <= self.budget:
                e = random.sample(list(set(unplace_basestation) - set(list(self.initial_basestations.keys()) + movable_edge_server_id)),1)[0]
                movable_edge_server_id.append(e)
                self.vectorX[e] = 1
                self.vectorY[e] = e
                vectorX = deepcopy(self.vectorX)
                vectorY = deepcopy(self.vectorY)
                count += 1
                for num, y in enumerate(vectorY):
                    if y is False:
                        F_value = {}
                        for edge in (movable_edge_server_id + list(self.initial_basestations.keys())):
                            distance = self.get_bs_distance(num, edge)
                            workload = self.get_workload_of_edge_vector(edge, vectorX, vectorY)
                            if distance < self.t_max and workload + self.bs_list[num].workload < vectorX[edge] * single_max_workload:
                                F_value[edge] = w2 * distance / self.t_max + workload / (vectorX[edge] * single_max_workload)
                        sorted_F_value = sorted(F_value.items(), key=lambda x: x[1], reverse=False)
                        if len(sorted_F_value) == 0:
                            vectorY[num] = self.n
                        else:
                            link_edge = random.sample(sorted_F_value[:sample_num], 1)
                            link_edge_num = link_edge[0][0]
                            workload = self.get_workload_of_edge_vector(link_edge_num, vectorX, vectorY)
                            if (workload + self.bs_list[num].workload) / (vectorX[link_edge_num] * single_max_workload) > th and vectorX[
                                link_edge_num] < c_max and self.get_total_cost_vector(vectorX) + install_cost <= self.budget:
                                vectorX[link_edge_num] += 1
                            vectorY[num] = link_edge_num
                self.vectorX = deepcopy(vectorX)  # 拷贝X，用于下面判断是否超出预算
                if self.get_total_cost_vector() <= self.budget:
                    vectorX1 = deepcopy(vectorX)
                    vectorY1 = deepcopy(vectorY)
            if count != 1:  # 执行次数大于1，表示vectorX1才是小于预算的解
                self.vectorX = deepcopy(vectorX1)
                self.vectorY = deepcopy(vectorY1)
            if count == 1:  # 执行次数等于1，表示第一次进入，就超出预算，没有进入最后的if self.get_total_cost_vector() <= self.budget判断语句
                self.vectorX = deepcopy(init_vectorX)
                self.vectorY = deepcopy(init_vectorY)
                for num, y in enumerate(self.vectorY):
                    if y is False:
                        F_value = {}
                        for edge in (init_movable_edge_server_id + list(self.initial_basestations.keys())):
                            distance = self.get_bs_distance(num, edge)
                            workload = self.get_workload_of_edge_vector(edge, self.vectorX, self.vectorY)
                            if distance < self.t_max and workload + self.bs_list[num].workload < self.vectorX[edge] * single_max_workload:
                                F_value[edge] = w2 * distance / self.t_max + workload / (self.vectorX[edge] * single_max_workload)
                        sorted_F_value = sorted(F_value.items(), key=lambda x: x[1], reverse=False)
                        if len(sorted_F_value) == 0:
                            self.vectorY[num] = self.n
                        else:
                            link_edge = random.sample(sorted_F_value[:sample_num], 1)
                            link_edge_num = link_edge[0][0]
                            self.vectorY[num] = link_edge_num
        wrong_x, wrong_y, flag = self.check_particles_vector(model=False)
        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
            # self.randam_init_vector()
            raise Exception('wrong')

    # 更新粒子
    def evo(self, gbest):
        t1 = self.fitness_now  # 当前的解
        t3 = gbest.fitness
        p1 = t1 / (t3 * 1.25)
        print(' {:<8.6}'.format(p1))
        if p1 > 1:
            self.randam_init_vector()

    # 更新粒子
    def evolution_vector(self, gbest):

        t1 = self.fitness_now  # 当前的解
        t2 = self.pbest['fitness']  # 粒子历史最优解
        t3 = gbest.fitness

        # max_t = max(t1, t2, t3)
        # max_t = max_t * 1.2
        # tt1 = (max_t-t1)
        # tt2 = (max_t-t2)
        # tt3 = (max_t-t3)

        tt1 = 1 / t1
        tt2 = 1 / t2
        tt3 = 1 / t3
        t_123 = tt1 + tt2 + tt3
        p1 = tt1 / t_123
        p2 = tt2 / t_123
        p3 = tt3 / t_123
        print(
            '{:<11.10},  {:<11.10},   {:<11.10},  {}'.format(t1, t2, t3,
                                                             '<----' if t1 == t3 or t2 == t3 else '     '),
            end='')
        print(' {:<8.6},  {:<8.6},  {:<8.6}'.format(p1, p2, p3))
        if p1 < 0.25 or t1 == t3 or t2 == t3:
            self.randam_init_vector()
        else:  # 按照正常的杂交更新
            # if srv_print_log:
            #     print('粒子No.{0}更新速度，p1={1}, p2={2}, p3={3}'.format(self.id, p1, p2, p3))

            # 更新速度
            for i in range(self.n):
                r = random.random()
                if r <= p1:
                    if i in self.initial_basestations.keys():
                        self.V[i] = False
                    continue
                elif (r > p1) and (r <= p2 + p1):  # 如果这个粒子最佳的位置矩阵与当前粒子的位置矩阵相同，则取反
                    if i in self.initial_basestations.keys():
                        self.V[i] = False
                    else:
                        self.V[i] = not (self.pbest['vectorX'][i] == self.vectorX[i])
                else:  # 如果全局最优的服务器放置位置矩阵等于当前粒子的放置矩阵，则取反
                    if i in self.initial_basestations.keys():
                        self.V[i] = False
                    else:
                        self.V[i] = not (gbest.vectorX[i] == self.vectorX[i])

            # 更新
            for i in range(self.n):
                if self.V[i]:    # 如果速度为true，那么就是要更新放置矩阵
                    r = random.random()
                    if r < p1:
                        continue
                    elif (r > p1) and (r <= p2 + p1):
                        self.vectorX[i] = self.pbest['vectorX'][i]
                        self.vectorY[i] = self.pbest['vectorY'][i]
                        if self.pbest['vectorX'][i] != 0:
                            for j in range(self.n):
                                if self.pbest['vectorY'][j] == i:
                                    self.vectorY[j] = self.pbest['vectorY'][j]
                    else:
                        self.vectorX[i] = gbest.vectorX[i]
                        self.vectorY[i] = gbest.vectorY[i]
                        if gbest.vectorX[i] != 0:
                            for j in range(self.n):
                                if gbest.vectorY[j] == i:
                                    self.vectorY[j] = gbest.vectorY[j]

        # 检查错误
        wrong_x, wrong_y,flag = self.check_particles_vector(model=True)
        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
            self.del_and_refill_vector(wrong_x, wrong_y)

    # 更新粒子
    def evolution_vector_gapso(self, gbest):

        t1 = self.fitness_now  # 当前的解
        t2 = self.pbest['fitness']  # 粒子历史最优解
        t3 = gbest.fitness

        # max_t = max(t1, t2, t3)
        # max_t = max_t * 1.2
        # tt1 = (max_t-t1)
        # tt2 = (max_t-t2)
        # tt3 = (max_t-t3)

        tt1 = 1 / t1
        tt2 = 1 / t2
        tt3 = 1 / t3
        t_123 = tt1 + tt2 + tt3
        p1 = tt1 / t_123
        p2 = tt2 / t_123
        p3 = tt3 / t_123
        print(
            '{:<11.10},  {:<11.10},   {:<11.10},  {}'.format(t1, t2, t3,
                                                             '<----' if t1 == t3 or t2 == t3 else '     '),
            end='')
        print(' {:<8.6},  {:<8.6},  {:<8.6}'.format(p1, p2, p3))
        if p1 < 0.3 or t1 == t3 or t2 == t3:
            self.randam_init_vector()
        else:  # 按照正常的杂交更新
            # 更新速度
            for i in range(self.n):
                r = random.random()
                if r <= p1:
                    if i in self.initial_basestations.keys():
                        self.V[i] = False
                    continue
                elif (r > p1) and (r <= p2 + p1):  # 如果这个粒子最佳的位置矩阵与当前粒子的位置矩阵相同，则取反
                    if i in self.initial_basestations.keys():
                        self.V[i] = False
                    else:
                        self.V[i] = not (self.pbest['vectorX'][i] == self.vectorX[i])
                else:  # 如果全局最优的服务器放置位置矩阵等于当前粒子的放置矩阵，则取反
                    if i in self.initial_basestations.keys():
                        self.V[i] = False
                    else:
                        self.V[i] = not (gbest.vectorX[i] == self.vectorX[i])

            # 更新
            for i in range(self.n):
                if self.V[i] and self.vectorY[i] != self.n:  # 如果速度为true，那么就是要更新放置矩阵
                    r = random.random()
                    if r < p1:
                        continue
                    elif (r >= p1) and (r < p2 + p1):
                        self.vectorX[i] = self.pbest['vectorX'][i]
                        self.vectorY[i] = self.pbest['vectorY'][i]
                    else:
                        self.vectorX[i] = gbest.vectorX[i]
                        self.vectorY[i] = gbest.vectorY[i]

            # # 更新
            # for i in range(self.n):
            #     if self.V[i]:  # 如果速度为true，那么就是要更新放置矩阵
            #         r = random.random()
            #         if r < p1:
            #             continue
            #         elif (r > p1) and (r <= p2 + p1):
            #             self.vectorY[i] = self.pbest['vectorY'][i]
            #         else:
            #             self.vectorY[i] = gbest.vectorY[i]


    def crossover(self, particle):  # mode表示为输入的是矩阵，不是个体
        # cross_proability = 0.8
        # cross_point = random.randint(0, self.n-1)
        # new_vectorX1 = deepcopy(self.vectorX)
        # new_vectorX2 = deepcopy(vectorX2)
        # if random.random() <= cross_proability:
        #     new_vectorX1 = self.vectorX[:cross_point] + vectorX2[cross_point:]
        #     new_vectorX2 = vectorX2[:cross_point] + self.vectorX[cross_point:]
        total_list = [i for i in range(self.n)]
        cross_num = math.ceil(self.n / 50)
        cross_point = random.sample(total_list, cross_num)
        new_vectorX1 = deepcopy(self.vectorX)
        new_vectorX2 = deepcopy(particle.vectorX)
        new_vectorY1 = deepcopy(self.vectorY)
        new_vectorY2 = deepcopy(particle.vectorY)
        if random.random() <= self.cross_proability:
            for cp in cross_point:
                temp = new_vectorX1[cp]
                new_vectorX1[cp] = new_vectorX2[cp]
                new_vectorX2[cp] = temp
                temp = new_vectorY1[cp]
                new_vectorY1[cp] = new_vectorY2[cp]
                new_vectorY2[cp] = temp
        return new_vectorX1, new_vectorX2, new_vectorY1, new_vectorY2

    def crossover1(self, particle):  # mode表示为输入的是矩阵，不是个体
        cross_point1 = random.randint(0, self.n-1)
        cross_point2 = random.randint(0, self.n-1)
        max_cross_point = max(cross_point1,cross_point2)
        min_cross_point = min(cross_point1,cross_point2)
        new_vectorX1 = deepcopy(self.vectorX)
        new_vectorX2 = deepcopy(particle.vectorX)
        new_vectorY1 = deepcopy(self.vectorY)
        new_vectorY2 = deepcopy(particle.vectorY)
        if random.random() <= self.cross_proability:
            new_vectorX1 = self.vectorX[:min_cross_point] + particle.vectorX[min_cross_point:max_cross_point] + self.vectorX[max_cross_point:]
            new_vectorX2 = particle.vectorX[:min_cross_point] + self.vectorX[min_cross_point:max_cross_point] + particle.vectorX[max_cross_point:]
            new_vectorY1 = self.vectorY[:min_cross_point] + particle.vectorY[min_cross_point:max_cross_point] + self.vectorY[max_cross_point:]
            new_vectorY2 = particle.vectorY[:min_cross_point] + self.vectorY[min_cross_point:max_cross_point] + particle.vectorY[max_cross_point:]
        # cross_proability = 0.8
        # total_list = [i for i in range(self.n)]
        # cross_num = math.ceil(self.n / 50)
        # cross_point = random.sample(total_list, cross_num)
        # new_vectorX1 = deepcopy(self.vectorX)
        # new_vectorX2 = deepcopy(particle.vectorX)
        # new_vectorY1 = deepcopy(self.vectorY)
        # new_vectorY2 = deepcopy(particle.vectorY)
        # if random.random() <= cross_proability:
        #     for cp in cross_point:
        #         temp = new_vectorX1[cp]
        #         new_vectorX1[cp] = new_vectorX2[cp]
        #         new_vectorX2[cp] = temp
        #         temp = new_vectorY1[cp]
        #         new_vectorY1[cp] = new_vectorY2[cp]
        #         new_vectorY2[cp] = temp
        return new_vectorX1, new_vectorX2, new_vectorY1, new_vectorY2

    def mutation(self):
        if random.random() <= self.mutation_proability:
            mutation_point1 = random.randint(0, self.n-1)
            if mutation_point1 not in self.initial_basestations.keys():
                delta = random.randint(0, c_max-1)
                self.vectorX[mutation_point1] = delta
        distance_dict = {}
        workload_dict = {}
        # total_list = [i for i in range(self.n)]
        # mutation_num = math.ceil(self.n / 50)
        # mutation_point2 = random.sample(total_list, mutation_num)
        # if random.random() <= self.mutation_proability:
        #     for bs_num in range(self.n):
        #         if bs_num in mutation_point2:
        #             if bs_num not in self.initial_basestations.keys():
        #                 for num, x in enumerate(self.vectorX):
        #                     if x:
        #                         distance_dict[num] = self.get_bs_distance(num, bs_num)
        #                         workload_dict[num] = self.get_workload_of_edge_vector(num, self.vectorX, self.vectorY)
        #                 filterate_distance_dict = {}
        #                 for key, value in distance_dict.items():
        #                     if value < self.t_max and self.bs_list[bs_num].workload + workload_dict[key] < self.vectorX[key] * single_max_workload:
        #                         filterate_distance_dict[key] = w3 * value / self.t_max + workload_dict[key] / (self.vectorX[key] * single_max_workload)
        #                 if len(filterate_distance_dict) == 0:
        #                     x = sorted(distance_dict.items(), key=lambda x: x[1], reverse=False).pop(0)
        #                     filterate_distance_dict[x[0]] = x[1]
        #                 sorted_distance_k = sorted(filterate_distance_dict.items(), key=lambda x: x[1], reverse=False)
        #                 link_edge = random.sample(sorted_distance_k[:sample_num], 1)
        #                 link_edge_num = link_edge[0][0]
        #                 self.vectorY[bs_num] = link_edge_num

        for bs_num in range(self.n):
            if bs_num not in self.initial_basestations.keys():
                if random.random() <= self.mutation_proability:
                    for num, x in enumerate(self.vectorX):
                        if x:
                            distance_dict[num] = self.get_bs_distance(num, bs_num)
                            workload_dict[num] = self.get_workload_of_edge_vector(num, self.vectorX, self.vectorY)
                    filterate_distance_dict = {}
                    for key, value in distance_dict.items():
                        if value < self.t_max and self.bs_list[bs_num].workload + workload_dict[key] < self.vectorX[key] * single_max_workload:
                            filterate_distance_dict[key] = w3 * value / self.t_max + workload_dict[key] / (self.vectorX[key] * single_max_workload)
                    if len(filterate_distance_dict) == 0:
                        x = sorted(distance_dict.items(), key=lambda x: x[1], reverse=False).pop(0)
                        filterate_distance_dict[x[0]] = x[1]
                    sorted_distance_k = sorted(filterate_distance_dict.items(), key=lambda x: x[1], reverse=False)
                    link_edge = random.sample(sorted_distance_k[:sample_num], 1)
                    link_edge_num = link_edge[0][0]
                    self.vectorY[bs_num] = link_edge_num

    def ant_move(self):
        distance_dict = {}
        edge_workload_list = []
        for i in range(self.n):
            if self.vectorX[i]:
                edge_workload = self.get_workload_of_edge_vector(i,self.vectorX,self.vectorY)
                edge_workload_list.append(edge_workload / self.vectorX[i])
        workload_avg = sum(edge_workload_list) / len(edge_workload_list)
        for i in range(self.n):
            if self.vectorX[i]:
                self.vectorY[i] = i
            else:
                self.vectorY[i] = False
        # sorted_base_stations = sorted(self.bs_list, key=lambda x: x.workload, reverse=True)
        sorted_base_stations = random.sample(self.bs_list, self.n)
        for bs in sorted_base_stations:
            flag = False
            if self.vectorY[bs.id] is False:
                for i in range(self.n):
                    if self.vectorX[i]:
                        distance_dict[i] = self.get_bs_distance(i, bs.id)
                filterate_distance_dict = {}
                sorted_distance_dict = dict(sorted(distance_dict.items(), key=lambda x: x[1], reverse=False))
                for key, value in sorted_distance_dict.items():
                    if value < self.t_max:
                        workload_key = self.get_workload_of_edge_vector(key,self.vectorX,self.vectorY)
                        filterate_distance_dict[key] = value
                        if (workload_key + bs.workload) / self.vectorX[key] <= workload_avg * 1.125:
                            self.vectorY[bs.id] = key
                            flag = True
                            break
                if flag is False:
                    sorted_filterate_distance_dict = sorted(filterate_distance_dict.items(),key=lambda x: self.get_workload_of_edge_vector(x[0],self.vectorX,self.vectorY)/self.vectorX[x[0]], reverse=False)
                    if len(sorted_filterate_distance_dict) == 0:
                        self.vectorY[bs.id] = list(sorted_distance_dict.keys())[0]
                    else:
                        link_edge = random.sample(sorted_filterate_distance_dict[:sample_num], 1)
                        link_edge_num = link_edge[0][0]
                        self.vectorY[bs.id] = link_edge_num
        # cheak
        wrong_x, wrong_y,flag = self.check_particles_vector(model=True)
        # delete
        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
            self.del_and_refill_vector(wrong_x, wrong_y)
        wrong_x, wrong_y,flag = self.check_particles_vector(model=False)
        if len(wrong_x) > 0 or len(wrong_y) > 0 or flag is True:
            self.randam_init_vector()
        # ==============================????bs????????==========================================


class Gbest_vector(object):
    def __init__(self):
        self.total_workloads = 0
        self.vectorX = None
        self.vectorY = None
        self.service_state = None
        self.condition = None
        self.average_delay = 999
        self.norm_delay = None
        self.workload_std = 999
        self.norm_workload = None
        self.fitness = 1000
        self.placement_scheme = None

    def get_to_cloud_vector(self):
        remote_list = []
        for i in range(len(self.vectorY)):
            if self.vectorY[i] == len(self.vectorY):
                remote_list.append(i)
        return remote_list

    def get_condition_vector(self,bs_list):
        condition = []
        # 逐列遍历
        count = 0
        for i in range(len(bs_list)):
            edge_server_unit = self.vectorX[i]
            edge_workload_temp = 0
            bs_list_temp = []
            if edge_server_unit != 0:
                max_workload = self.vectorX[i] * single_max_workload
                for j in range(len(bs_list)):
                    if (self.vectorY[j] == i):
                        edge_workload_temp += bs_list[j].workload
                        bs_list_temp.append(bs_list[j])
                        count += 1
                dict_temp = {'at_bs_No': i, 'serving_bs': bs_list_temp, 'workload': edge_workload_temp,
                             'max_workload': max_workload}
                condition.append(dict_temp)
        return condition

    def update(self,particles):
        # temp_particle_for_sort = sorted(self.particles, key=lambda d: d.pbest['average_delay'])
        self.particles = particles
        fitness_particle_for_sort = sorted(self.particles, key=lambda d: d.pbest['fitness'], reverse=False)
        # particles.sort(key=lambda d: d.pbest['average_delay'])
        best_particle = fitness_particle_for_sort[0]
        if self.norm_delay is None or self.norm_workload is None:
            if best_particle.pbest['fitness'] < self.fitness:  # 保存表现最好的时候的各项指标
                self.vectorX = deepcopy(best_particle.pbest['vectorX'])  # 保存表现最好的时候的边缘服务器放置矩阵
                self.vectorY = deepcopy(best_particle.pbest['vectorY'])  # 保存表现最好的时候的边缘服务器放置矩阵
                self.average_delay = best_particle.pbest['average_delay']
                self.norm_delay = best_particle.pbest['norm_delay']
                self.norm_workload = best_particle.pbest['norm_workload']
                self.fitness = best_particle.pbest['fitness']
                self.placement_scheme = self.get_condition_vector(best_particle.bs_list)
                # 防止该粒子停止不动
                best_particle.randam_init_vector()
            # 检查profit是否一直都是在增大
            elif best_particle.pbest['fitness'] > self.fitness:
                print('error: gbest 在减小:      ', self.fitness, '---->', best_particle.pbest['fitness'])
                # raise ValueError('Gbest profit getting smaller')
        else:
            if best_particle.pbest['fitness'] < self.fitness:  # 保存表现最好的时候的各项指标
                # if (best_particle.norm_delay <= self.norm_delay and best_particle.norm_workload <= self.norm_workload * (1 + 0.15))\
                #    or (best_particle.norm_workload <= self.norm_workload and best_particle.norm_delay <= self.norm_delay * (1 + 0.15)):
                    self.vectorX = deepcopy(best_particle.pbest['vectorX'])  # 保存表现最好的时候的边缘服务器放置矩阵
                    self.vectorY = deepcopy(best_particle.pbest['vectorY'])  # 保存表现最好的时候的边缘服务器放置矩阵
                    self.average_delay = best_particle.pbest['average_delay']
                    self.norm_delay = best_particle.pbest['norm_delay']
                    self.norm_workload = best_particle.pbest['norm_workload']
                    self.fitness = best_particle.pbest['fitness']
                    self.placement_scheme = self.get_condition_vector(best_particle.bs_list)
                    # 防止该粒子停止不动
                    best_particle.randam_init_vector()
                    # 检查profit是否一直都是在增大
                    # elif best_particle.pbest['fitness'] > self.fitness:
                    #     print('error: gbest 在减小:      ', self.fitness, '---->', best_particle.pbest['fitness'])
                        # raise ValueError('Gbest profit getting smaller')




