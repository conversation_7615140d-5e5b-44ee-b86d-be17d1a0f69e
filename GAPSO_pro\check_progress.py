#!/usr/bin/env python3
"""
快速检查实验进度脚本
"""

import os
import re
from collections import defaultdict

def check_current_progress():
    """检查当前实验进度"""
    
    experiment_dir = 'experiments/bsnum'
    
    # 预期配置
    expected_algorithms = ['Top-K', 'Random', 'ACO', 'GA-PSO', 'PSO', 'NSGA-II', 'MOEA-D', 'DE']
    expected_scales = [300, 350, 400, 450, 500, 550, 600]
    expected_repetitions = 50
    
    print("🔍 检查实验进度...")
    print("=" * 60)
    
    # 统计已完成实验
    completed_count = defaultdict(lambda: defaultdict(int))
    total_completed = 0
    total_expected = len(expected_algorithms) * len(expected_scales) * expected_repetitions
    
    if os.path.exists(experiment_dir):
        for filename in os.listdir(experiment_dir):
            if filename.endswith('.txt'):
                match = re.match(r'(.+)_N(\d+)\.txt', filename)
                if match:
                    algorithm = match.group(1)
                    n = int(match.group(2))
                    
                    filepath = os.path.join(experiment_dir, filename)
                    with open(filepath, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        completed_count[algorithm][n] = len(lines)
                        total_completed += len(lines)
    
    # 显示详细进度
    print(f"📊 详细进度报告")
    print("-" * 60)
    
    missing_high_priority = []
    missing_medium_priority = []
    missing_low_priority = []
    
    for algorithm in expected_algorithms:
        print(f"\n{algorithm}:")
        algorithm_total = 0
        algorithm_completed = 0
        
        for n in expected_scales:
            completed = completed_count[algorithm][n]
            missing = expected_repetitions - completed
            algorithm_completed += completed
            algorithm_total += expected_repetitions
            
            if completed == expected_repetitions:
                status = "✅"
            elif completed > 0:
                status = "🔄"
            else:
                status = "❌"
            
            print(f"  N={n}: {status} {completed:2d}/{expected_repetitions} ({completed/expected_repetitions*100:5.1f}%)")
            
            # 分类缺失实验
            if missing > 0:
                priority_info = {
                    'algorithm': algorithm,
                    'n': n,
                    'missing': missing,
                    'completed': completed
                }
                
                # 高优先级：GA-PSO, NSGA-II 在关键规模
                if algorithm in ['GA-PSO', 'NSGA-II'] and n in [400, 450, 500]:
                    missing_high_priority.append(priority_info)
                # 中等优先级：多目标算法和重要规模
                elif algorithm in ['MOEA-D', 'DE', 'ACO'] or n in [300, 450, 600]:
                    missing_medium_priority.append(priority_info)
                # 低优先级：其他
                else:
                    missing_low_priority.append(priority_info)
        
        progress = algorithm_completed / algorithm_total * 100
        print(f"  总计: {algorithm_completed}/{algorithm_total} ({progress:.1f}%)")
    
    # 总体统计
    overall_progress = total_completed / total_expected * 100
    print("\n" + "=" * 60)
    print(f"📈 总体进度: {total_completed}/{total_expected} ({overall_progress:.1f}%)")
    print(f"⏱️  已完成实验数: {total_completed}")
    print(f"⏳ 剩余实验数: {total_expected - total_completed}")
    
    # 优先级分析
    print("\n🎯 优先级分析:")
    print(f"🔴 高优先级缺失: {len(missing_high_priority)} 组")
    print(f"🟡 中优先级缺失: {len(missing_medium_priority)} 组")
    print(f"🟢 低优先级缺失: {len(missing_low_priority)} 组")
    
    # 详细缺失列表
    if missing_high_priority:
        print("\n🔴 高优先级缺失详情:")
        for item in missing_high_priority:
            print(f"  {item['algorithm']} N={item['n']}: 缺失 {item['missing']} 次实验")
    
    if missing_medium_priority:
        print("\n🟡 中优先级缺失详情 (前10个):")
        for item in missing_medium_priority[:10]:
            print(f"  {item['algorithm']} N={item['n']}: 缺失 {item['missing']} 次实验")
        if len(missing_medium_priority) > 10:
            print(f"  ... 还有 {len(missing_medium_priority)-10} 组")
    
    # 时间估算
    total_missing = sum(item['missing'] for item in missing_high_priority + missing_medium_priority + missing_low_priority)
    
    print(f"\n⏰ 时间估算:")
    print(f"  剩余实验总数: {total_missing}")
    
    # 不同策略的时间估算
    strategies = [
        ("只补充高优先级", sum(item['missing'] for item in missing_high_priority), 4),
        ("补充高+中优先级", sum(item['missing'] for item in missing_high_priority + missing_medium_priority), 6),
        ("补充全部", total_missing, 8)
    ]
    
    for strategy_name, exp_count, workers in strategies:
        if exp_count > 0:
            # 优化后的时间估算（分钟/实验）
            time_per_exp = {
                300: 3, 350: 4, 400: 5, 450: 6, 500: 7, 550: 8, 600: 9
            }
            avg_time = 6  # 平均时间
            
            estimated_hours = (exp_count * avg_time) / (60 * workers)
            print(f"  {strategy_name}: {exp_count} 实验, 约 {estimated_hours:.1f} 小时 ({workers} 进程)")
    
    # 推荐策略
    print(f"\n💡 推荐策略:")
    if missing_high_priority:
        high_count = sum(item['missing'] for item in missing_high_priority)
        high_hours = (high_count * 6) / (60 * 4)
        print(f"1. 立即运行高优先级实验 ({high_count} 个, 约 {high_hours:.1f} 小时)")
        print(f"   命令: python resume_experiment.py --priority-only --workers 4")
    
    if missing_medium_priority:
        medium_count = sum(item['missing'] for item in missing_medium_priority)
        medium_hours = (medium_count * 6) / (60 * 6)
        print(f"2. 然后运行中优先级实验 ({medium_count} 个, 约 {medium_hours:.1f} 小时)")
        print(f"   命令: python resume_experiment.py --workers 6")
    
    print(f"\n📋 可用命令:")
    print(f"  python resume_experiment.py --analyze-only  # 只分析，不运行")
    print(f"  python resume_experiment.py --priority-only # 只运行高优先级")
    print(f"  python resume_experiment.py --workers 6     # 运行所有缺失实验")
    
    return {
        'total_completed': total_completed,
        'total_expected': total_expected,
        'missing_high': missing_high_priority,
        'missing_medium': missing_medium_priority,
        'missing_low': missing_low_priority
    }

if __name__ == "__main__":
    check_current_progress()
