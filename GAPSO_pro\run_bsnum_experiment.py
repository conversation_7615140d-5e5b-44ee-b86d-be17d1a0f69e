#!/usr/bin/env python3
"""
基站数量影响实验 - 对应论文图9
运行完整的基站数量影响分析实验
"""

import os
import sys
import time
import json
import logging
from datetime import datetime
from multiprocessing import Pool, cpu_count
import numpy as np

# 配置日志
def setup_logging():
    """设置日志配置"""
    os.makedirs('logs', exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/bsnum_experiment.log'),
            logging.StreamHandler()
        ]
    )

def run_single_experiment(params):
    """运行单次实验"""
    n, algorithm_name, seed, budget, rate = params
    
    try:
        # 导入必要模块
        from utils import DataUtils
        from generate_topology import generate_topology
        from algorithm import (
            TopKServerPlacer, RandomServerPlacer, ACOServerPlacer, 
            GAPSOServerPlacer, PSOServerPlacer
        )
        from multi_objective_algorithms import (
            NSGAIIServerPlacer, MOEADServerPlacer, DifferentialEvolutionServerPlacer
        )
        
        # 加载数据
        data = DataUtils('./dataset/basestations.csv', './dataset/userdata.csv')
        base_stations = data.base_stations
        delay_matrix = generate_topology(data, n)
        
        # 初始基站配置
        initial_basestations = {
            int(n*0.1): 100000, int(n*0.25): 100000, int(n*0.4): 100000,
            int(n*0.55): 100000, int(n*0.7): 100000, int(n*0.85): 100000
        }
        
        # 选择算法
        algorithms = {
            'Top-K': TopKServerPlacer,
            'Random': RandomServerPlacer,
            'ACO': ACOServerPlacer,
            'GA-PSO': GAPSOServerPlacer,
            'PSO': PSOServerPlacer,
            'NSGA-II': NSGAIIServerPlacer,
            'MOEA-D': MOEADServerPlacer,
            'DE': DifferentialEvolutionServerPlacer
        }
        
        if algorithm_name not in algorithms:
            logging.error(f"未知算法: {algorithm_name}")
            return None
            
        algorithm = algorithms[algorithm_name](base_stations[:n], delay_matrix)
        
        # 运行算法
        start_time = time.time()
        
        try:
            if algorithm_name in ['NSGA-II', 'MOEA-D', 'DE']:
                # 多目标算法使用正确的参数名
                algorithm.place_server(
                    base_station_num=n,
                    budget=budget,
                    t_max=100,
                    rate=rate,
                    initial_basestations=initial_basestations,
                    cross_probability=0.8,
                    mutation_probability=0.1,
                    seed=seed
                )
            else:
                # 传统算法使用原有的参数名（包含拼写错误）
                algorithm.place_server(
                    base_station_num=n,
                    budget=budget,
                    t_max=100,
                    rate=rate,
                    initial_basestations=initial_basestations,
                    cross_proability=0.8,  # 注意拼写错误
                    mutation_proability=0.1,
                    seed=seed
                )
        except Exception as e:
            logging.error(f"算法运行失败: {algorithm_name}, N={n}, seed={seed}, 错误: {str(e)}")
            return None
        
        end_time = time.time()
        runtime = end_time - start_time
        
        # 计算结果
        if hasattr(algorithm, 'edge_servers') and algorithm.edge_servers:
            try:
                # 获取性能指标
                delay_result = algorithm.objective_latency()
                workload_result = algorithm.objective_workload()
                fitness_result = algorithm.objective_fitness()
                
                # 处理不同算法的返回值格式
                delay = delay_result[0] if isinstance(delay_result, tuple) else delay_result
                workload = workload_result[0] if isinstance(workload_result, tuple) else workload_result
                fitness = fitness_result[0] if isinstance(fitness_result, tuple) else fitness_result
                
                num_servers = len(algorithm.edge_servers)
                
                # 数据合理性检查
                if fitness > 100 or delay > 1000 or workload > 100000:
                    logging.warning(f"异常数据: {algorithm_name}, N={n}, fitness={fitness}, delay={delay}, workload={workload}")
                
                # 保存结果
                result = {
                    'algorithm': algorithm_name,
                    'n': n,
                    'seed': seed,
                    'budget': budget,
                    'rate': rate,
                    'fitness': float(fitness),
                    'delay': float(delay),
                    'workload': float(workload),
                    'num_servers': num_servers,
                    'runtime': runtime,
                    'timestamp': datetime.now().isoformat()
                }
                
                # 写入结果文件
                os.makedirs('experiments/bsnum', exist_ok=True)
                result_file = f'experiments/bsnum/{algorithm_name}_N{n}.txt'
                
                with open(result_file, 'a', encoding='utf-8') as f:
                    result_line = f"seed={seed},N={n},t_max=100,budget={budget},rate={rate} {algorithm_name}的fitness:{fitness},delay:{delay},workload:{workload},runtime:{runtime:.2f}\n"
                    f.write(result_line)
                
                logging.info(f"完成: {algorithm_name}, N={n}, seed={seed}, fitness={fitness:.4f}, 用时{runtime:.1f}s")
                return result
                
            except Exception as e:
                logging.error(f"结果计算失败: {algorithm_name}, N={n}, seed={seed}, 错误: {str(e)}")
                return None
        else:
            logging.error(f"无边缘服务器: {algorithm_name}, N={n}, seed={seed}")
            return None
            
    except Exception as e:
        logging.error(f"实验运行失败: {algorithm_name}, N={n}, seed={seed}, 错误: {str(e)}")
        return None

def run_batch_experiment(base_station_numbers, algorithms, budget, rate, repetitions, max_workers=None):
    """运行批量实验"""
    
    # 生成所有实验参数组合
    experiment_params = []
    for n in base_station_numbers:
        for algorithm_name in algorithms:
            for seed in range(100, 100 + repetitions):
                experiment_params.append((n, algorithm_name, seed, budget, rate))
    
    total_experiments = len(experiment_params)
    logging.info(f"总实验数量: {total_experiments}")
    logging.info(f"预计运行时间: {total_experiments * 5 / 60:.1f} 分钟")
    
    # 确定并行进程数
    if max_workers is None:
        max_workers = min(cpu_count(), 4)  # 最多使用4个进程
    
    logging.info(f"使用 {max_workers} 个并行进程")
    
    # 并行运行实验
    start_time = time.time()
    successful_results = []
    
    try:
        with Pool(processes=max_workers) as pool:
            results = pool.map(run_single_experiment, experiment_params)
            successful_results = [r for r in results if r is not None]
    except KeyboardInterrupt:
        logging.info("实验被用户中断")
        return successful_results
    except Exception as e:
        logging.error(f"并行执行错误: {str(e)}")
        return successful_results
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 统计结果
    success_rate = len(successful_results) / total_experiments * 100
    
    logging.info(f"实验完成!")
    logging.info(f"总时间: {total_time/3600:.2f} 小时")
    logging.info(f"成功率: {success_rate:.1f}% ({len(successful_results)}/{total_experiments})")
    
    return successful_results

def save_experiment_summary(results, experiment_params):
    """保存实验摘要"""
    
    # 按算法统计结果
    algorithm_stats = {}
    for result in results:
        alg = result['algorithm']
        if alg not in algorithm_stats:
            algorithm_stats[alg] = []
        algorithm_stats[alg].append(result)
    
    # 计算统计信息
    summary_stats = {}
    for alg, alg_results in algorithm_stats.items():
        fitness_values = [r['fitness'] for r in alg_results]
        delay_values = [r['delay'] for r in alg_results]
        workload_values = [r['workload'] for r in alg_results]
        
        summary_stats[alg] = {
            'count': len(alg_results),
            'fitness': {
                'mean': np.mean(fitness_values),
                'std': np.std(fitness_values),
                'min': np.min(fitness_values),
                'max': np.max(fitness_values)
            },
            'delay': {
                'mean': np.mean(delay_values),
                'std': np.std(delay_values),
                'min': np.min(delay_values),
                'max': np.max(delay_values)
            },
            'workload': {
                'mean': np.mean(workload_values),
                'std': np.std(workload_values),
                'min': np.min(workload_values),
                'max': np.max(workload_values)
            }
        }
    
    # 保存摘要
    summary = {
        'experiment_type': 'base_station_number',
        'total_experiments': len(results),
        'parameters': experiment_params,
        'algorithm_statistics': summary_stats,
        'timestamp': datetime.now().isoformat()
    }
    
    os.makedirs('experiments/bsnum', exist_ok=True)
    with open('experiments/bsnum/experiment_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    logging.info("实验摘要已保存到 experiments/bsnum/experiment_summary.json")

def main():
    """主实验函数"""
    
    setup_logging()
    logging.info("开始基站数量影响实验")
    
    # 实验参数配置
    experiment_params = {
        'base_station_numbers': [300, 350, 400, 450, 500, 550, 600],  # 7个规模
        'algorithms': ['Top-K', 'Random', 'ACO', 'GA-PSO', 'PSO', 'NSGA-II', 'MOEA-D', 'DE'],  # 8个算法
        'budget': 30,  # 固定预算
        'rate': 1.0,   # 固定工作负载比例
        'repetitions': 50  # 50次重复
    }
    
    # 显示实验配置
    logging.info("实验配置:")
    for key, value in experiment_params.items():
        logging.info(f"  {key}: {value}")
    
    # 运行实验
    results = run_batch_experiment(
        base_station_numbers=experiment_params['base_station_numbers'],
        algorithms=experiment_params['algorithms'],
        budget=experiment_params['budget'],
        rate=experiment_params['rate'],
        repetitions=experiment_params['repetitions'],
        max_workers=4  # 使用4个并行进程
    )
    
    # 保存实验摘要
    if results:
        save_experiment_summary(results, experiment_params)
        
        # 显示简要结果
        logging.info("\n=== 实验结果摘要 ===")
        algorithm_counts = {}
        for result in results:
            alg = result['algorithm']
            algorithm_counts[alg] = algorithm_counts.get(alg, 0) + 1
        
        for alg, count in sorted(algorithm_counts.items()):
            expected = len(experiment_params['base_station_numbers']) * experiment_params['repetitions']
            logging.info(f"{alg}: {count}/{expected} 次实验完成")
    
    logging.info("基站数量影响实验结束")

if __name__ == "__main__":
    main()
